#!/usr/bin/env python3

import sys
import os
import pandas as pd
import io

# Add the current directory to Python path
sys.path.append(os.getcwd())

def test_validation():
    """Test the validation logic directly"""
    
    # Create test data with validation errors
    test_data = """policyno,packages,bus,pep,agentcode,agentname,status
POL001,Standard,true,false,AG001,John Agent,Active
POL002,InvalidPackage,maybe,false,AG002,Jane Agent,Active
POL003,,true,invalid_bool,AG003,,Pending
POL004,Premier,false,true,,Bob Agent,Active"""
    
    print("=== TESTING VALIDATION LOGIC ===")
    
    try:
        # Parse the CSV
        df = pd.read_csv(io.StringIO(test_data))
        print(f"✅ CSV parsed successfully. Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print(f"Data:\n{df}")
        
        # Test the validation logic
        import_type = "policy"
        required_columns = ['policyno', 'packages', 'bus', 'pep', 'agentcode', 'agentname', 'status']
        
        print(f"\n=== VALIDATION DETAILS ===")
        print(f"Required columns: {required_columns}")
        
        # Check for missing columns
        file_columns = [col.strip().lower() for col in df.columns]
        required_columns_lower = [col.lower() for col in required_columns]
        missing_columns = [col for col in required_columns if col.lower() not in file_columns]
        
        print(f"File columns (normalized): {file_columns}")
        print(f"Missing columns: {missing_columns}")
        
        # Test row validation
        validation_errors = []
        
        if import_type == "policy":
            print(f"\n=== POLICY VALIDATION DETAILS ===")
            
            try:
                # Convert dataframe to list of dictionaries to avoid pandas type issues
                records = df.to_dict('records')
                print(f"Records: {records}")
                
                for idx, record in enumerate(records):
                    row_errors = []
                    row_number = idx + 2  # +2 for header and 0-based index
                    
                    print(f"\nValidating row {row_number}: {record}")
                    
                    # Check required fields
                    for req_col in required_columns:
                        # Find the actual column name (case insensitive)
                        actual_col = None
                        for col_name in record.keys():
                            if str(col_name).strip().lower() == req_col.lower():
                                actual_col = col_name
                                break
                        
                        if actual_col is not None:
                            cell_value = record[actual_col]
                            if pd.isna(cell_value) or str(cell_value).strip() == '':
                                row_errors.append(f"'{req_col}' is empty")
                                print(f"  ❌ {req_col} is empty")
                    
                    # Check boolean fields format
                    for bool_field in ['bus', 'pep']:
                        actual_col = None
                        for col_name in record.keys():
                            if str(col_name).strip().lower() == bool_field.lower():
                                actual_col = col_name
                                break
                        
                        if actual_col is not None:
                            cell_value = record[actual_col]
                            if not pd.isna(cell_value):
                                val = str(cell_value).strip().lower()
                                if val not in ['true', 'false', '1', '0', 'yes', 'no']:
                                    row_errors.append(f"'{bool_field}' has invalid boolean value: '{cell_value}'")
                                    print(f"  ❌ {bool_field} has invalid boolean: '{cell_value}'")
                    
                    # Check packages field
                    packages_col = None
                    for col_name in record.keys():
                        if str(col_name).strip().lower() == 'packages':
                            packages_col = col_name
                            break
                    
                    if packages_col is not None:
                        cell_value = record[packages_col]
                        if not pd.isna(cell_value):
                            packages_val = str(cell_value).strip()
                            if packages_val not in ['Standard', 'Premier', 'Lite']:
                                row_errors.append(f"'packages' has invalid value: '{packages_val}'. Must be: Standard, Premier, or Lite")
                                print(f"  ❌ packages has invalid value: '{packages_val}'")
                    
                    if row_errors:
                        validation_errors.append({
                            "row": row_number,
                            "errors": row_errors,
                            "data": {k: str(v) for k, v in record.items() if not pd.isna(v)}
                        })
                        print(f"  ❌ Row {row_number} has errors: {row_errors}")
                    else:
                        print(f"  ✅ Row {row_number} is valid")
                
                print(f"\n=== VALIDATION SUMMARY ===")
                print(f"Found {len(validation_errors)} rows with validation errors")
                if validation_errors:
                    for error in validation_errors:
                        print(f"Row {error['row']}: {error['errors']}")
                        
            except Exception as validation_error:
                print(f"❌ Error during validation: {str(validation_error)}")
                import traceback
                print(f"Validation traceback: {traceback.format_exc()}")
        
        print(f"\n✅ Validation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_validation()
