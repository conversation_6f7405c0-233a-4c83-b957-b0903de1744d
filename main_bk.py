from fastapi import <PERSON><PERSON><PERSON>,Depends, HTTPException,status
import services, models, schema
from db import get_db, Base
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Annotated
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from passlib.context import CryptContext
from auth import hash_password, verify_password, create_access_token, decode_access_token
from datetime import timedelta
import uvicorn
# from starlette.requests import Request
# import requests, time
#from schemas.department import Department, DepartmentCreate
# from models.models import department
# from db import get_db, Base
# from sqlalchemy.orm import session

# SECRET_KEY = "my_secret_key"
# ALGORITHM = "HS256"
# ACCESS_TOKEN_EXPIRE_MINUTES = 30

# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

app = FastAPI()
security = HTTPBearer()

db_dependency = Annotated[Session, Depends(get_db)]
# user_dependency = Annotated[dict, Depends(get_current_user)]

# Setting up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

@app.get("/")
def read_root():
    return {"Hello": "World"}

# Register users
@app.post("/signup")
def signup(user: schema.UserCreate, db: Session = Depends(get_db)):
    hashed_password = hash_password(user.password)
    user.password = hashed_password
    return services.create_user(db, user)

#login
@app.post("/login")
def login(user: schema.Userlogin, db: Session = Depends(get_db)):
    db_user = db.query(models.User).filter(models.User.username == user.username).first()
    if not db_user:
        raise HTTPException(status_code=400, detail="Incorrect username")
    
    if not verify_password(user.password, db_user.password if isinstance(db_user.password, str) else db_user.password.value):
        raise HTTPException(status_code=400, detail="Incorrect password")
    
    access_token = create_access_token(data={"sub": db_user.username}, expires_delta=timedelta(minutes=30))
    return {"access_token": access_token, "token_type": "bearer", "user": db_user}

# @app.post("/login")
# def login(user: schema.Userlogin, db: Session = Depends(get_db)):
#     db_user = db.query(models.User).filter(models.User.email == user.email).first()
#     if not db_user:
#         raise HTTPException(status_code=400, detail="Incorrect username")
    
#     if not verify_password(user.password, db_user.password if isinstance(db_user.password, str) else db_user.password.value):
#         raise HTTPException(status_code=400, detail="Incorrect password")
    
#     access_token = create_access_token(data={"sub": db_user.email}, expires_delta=timedelta(minutes=30))
#     return {"access_token": access_token, "token_type": "bearer", "user": db_user}

# Get user details
@app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

@app.get("/users/{user_id}", response_model=schema.UserBase)
def get_user(user_id: int, db: Session = Depends(get_db)):
    user = services.get_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

# Get all users
@app.get("/users", response_model=List[schema.UserBase])
def get_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
    ,current_user: dict = Depends(get_current_user)
):
    users = services.get_users(db, skip=skip, limit=limit)
    return users

# Example of a protected route
# @app.get("/secure-data")
# def secure_data(current_user=Depends(get_current_user)):
#     return {"message": "This is protected data", "user": current_user}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5000)

# Member actions
# get user header details - DONE!!!
# update user header details - 
# get user beneficiries - DONE
# add Beneficiary
# update beneficiary
# delete beneficiary

# endorser actions
# endorse beneficiary by updating status

# file management
# upload from compen - DONE!!!
# export for compen - DONE!!!