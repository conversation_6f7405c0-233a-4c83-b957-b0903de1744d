/* Custom CSS Variables for Green, White, Black Theme */
:root {
  --primary-green: #28a745;
  --dark-green: #1e7e34;
  --light-green: #d4edda;
  --primary-black: #212529;
  --light-gray: #f8f9fa;
  --white: #ffffff;
  --border-color: #dee2e6;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--light-gray);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom Bootstrap Overrides */
.btn-primary {
  background-color: var(--primary-green);
  border-color: var(--primary-green);
}

.btn-primary:hover {
  background-color: var(--dark-green);
  border-color: var(--dark-green);
}

.navbar-brand {
  color: var(--white) !important;
  font-weight: bold;
}

.navbar-dark {
  background-color: var(--primary-black) !important;
}

.card {
  border: 1px solid var(--border-color);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: var(--light-green);
}

.text-primary {
  color: var(--primary-green) !important;
}

.bg-primary {
  background-color: var(--primary-green) !important;
}

.border-primary {
  border-color: var(--primary-green) !important;
}

/* Custom Classes */
.sidebar {
  background-color: var(--white);
  border-right: 1px solid var(--border-color);
  min-height: calc(100vh - 56px);
}

.main-content {
  background-color: var(--light-gray);
  min-height: calc(100vh - 56px);
}

.metric-card {
  background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
  color: var(--white);
  border: none;
}

.policy-card {
  transition: transform 0.2s;
}

.policy-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.search-container {
  background-color: var(--white);
  border-radius: 0.375rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.loading-spinner {
  color: var(--primary-green);
}

.alert-success {
  background-color: var(--light-green);
  border-color: var(--primary-green);
  color: var(--dark-green);
}
