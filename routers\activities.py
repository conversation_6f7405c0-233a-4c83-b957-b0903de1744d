from fastapi import Fast<PERSON><PERSON>, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import <PERSON>A<PERSON>2P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/policyactivities",
    tags=['PolicyActivities'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# search policy activities by policy number with pagination
@router.get("/search/{policy_no}", response_model=List[schema.PolicyActivities])
def search_policy_activities(policy_no: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policy_activities = db.query(models.PolicyActivities).filter(models.PolicyActivities.policyno == policy_no).offset(skip).limit(limit).all()
    if not policy_activities:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policy activities found for the specified policy number")
    return policy_activities

# search by activity description and policy number with pagination
@router.get("/search/activity/{activity_desc}/{policy_no}", response_model=List[schema.PolicyActivities])
def search_policy_activity_by_desc(activity_desc: str, policy_no: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policy_activities = db.query(models.PolicyActivities).filter(
        func.lower(models.PolicyActivities.activitydesc) == func.lower(activity_desc),
        models.PolicyActivities.policyno == policy_no
    ).offset(skip).limit(limit).all()
    
    if not policy_activities:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policy activities found for the specified activity description and policy number")
    
    return policy_activities

# create new policy activity
@router.post("/", response_model=schema.PolicyActivities, status_code=status.HTTP_201_CREATED)
def create_policy_activity(policy_activity: schema.PolicyActivities, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    # Check if the policy ID exists
    policy_exists = db.query(models.Policy).filter(models.Policy.id == policy_activity.policyid).first()
    if not policy_exists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    
    # Create the policy activity
    policy_activity_data = models.PolicyActivities(**policy_activity.model_dump(), createdby=current_user['id'])
    db.add(policy_activity_data)
    db.commit()
    db.refresh(policy_activity_data)
    return policy_activity_data

# update policy activty
@router.put("/{activity_id}", response_model=schema.PolicyActivities)
def update_policy_activity(activity_id: int, policy_activity: schema.PolicyActivities, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_activity = db.query(models.PolicyActivities).filter(models.PolicyActivities.id == activity_id).first()
    if not existing_activity:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy activity not found")
    
    # Update the activity details
    for key, value in policy_activity.model_dump().items():
        setattr(existing_activity, key, value)
    
    db.commit()
    db.refresh(existing_activity)
    return existing_activity

# delete policy activity
@router.delete("/{activity_id}", response_model=schema.PolicyActivities)
def delete_policy_activity(activity_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_activity = db.query(models.PolicyActivities).filter(models.PolicyActivities.id == activity_id).first()
    if not existing_activity:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy activity not found")
    
    db.delete(existing_activity)
    db.commit()
    return existing_activity