#!/bin/bash

echo "========================================"
echo "Mthunzi Policy Management Frontend Setup"
echo "========================================"
echo

echo "Checking if Node.js is installed..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js is installed: $(node --version)"
echo

echo "Navigating to frontend directory..."
cd frontend

echo "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "Creating .env file from example..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo ".env file created successfully"
else
    echo ".env file already exists"
fi

echo
echo "========================================"
echo "Setup completed successfully!"
echo "========================================"
echo
echo "To start the development server:"
echo "  cd frontend"
echo "  npm start"
echo
echo "The application will be available at:"
echo "  http://localhost:3000"
echo
echo "Make sure your backend API is running on:"
echo "  http://localhost:5000"
echo
