#!/usr/bin/env python3
"""
Test script to debug the validation endpoint
"""
import requests
import json
import jwt
from datetime import datetime, timedelta, timezone

# Test the validation endpoint
def test_validation():
    url = "http://localhost:8000/imports/validate"

    # Create a valid JWT token using the same secret as the backend
    SECRET_KEY = "my_secret_key"
    ALGORITHM = "HS256"

    # Create token payload
    payload = {
        "id": 1,
        "username": "test_user",
        "email": "<EMAIL>",
        "exp": datetime.now(timezone.utc) + timedelta(minutes=30)
    }

    # Generate token
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # Test with our policy test file
    try:
        # Test 1: Valid file
        print("\n=== TEST 1: Valid Policy File ===")
        with open('test_data/policy_test.csv', 'rb') as f:
            files = {
                'file': ('policy_test.csv', f, 'text/csv')
            }
            data = {
                'import_type': 'policy'
            }
            
            print("Testing validation endpoint...")
            print(f"URL: {url}")
            print(f"Headers: {headers}")
            print(f"Data: {data}")
            print(f"File: policy_test.csv")
            
            response = requests.post(url, headers=headers, files=files, data=data)
            
            print(f"\nResponse Status: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            print(f"Response Text: {response.text}")
            
            if response.status_code == 200:
                print("\n✅ SUCCESS!")
                result = response.json()
                print(f"Validation Result: {json.dumps(result, indent=2)}")
            else:
                print(f"\n❌ ERROR: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"Error Detail: {json.dumps(error_detail, indent=2)}")
                except:
                    print(f"Raw Error: {response.text}")

        # Test 2: Invalid file
        print("\n=== TEST 2: Invalid File ===")
        with open('test_data/invalid_test.csv', 'rb') as f:
            files = {
                'file': ('invalid_test.csv', f, 'text/csv')
            }
            data = {
                'import_type': 'policy'
            }

            response = requests.post(url, headers=headers, files=files, data=data)

            print(f"Response Status: {response.status_code}")
            print(f"Response Text: {response.text}")

            if response.status_code != 200:
                try:
                    error_detail = response.json()
                    print(f"Error Detail: {json.dumps(error_detail, indent=2)}")
                except:
                    print(f"Raw Error: {response.text}")

    except FileNotFoundError as e:
        print(f"❌ Test file not found: {e}")
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_validation()
