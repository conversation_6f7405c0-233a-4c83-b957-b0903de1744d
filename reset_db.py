#!/usr/bin/env python3
"""
Reset database script - drops all tables and recreates them
"""

from db import engine
from sqlalchemy import text
from models import Base

def drop_all_tables():
    """Drop all tables and indexes in the database"""
    try:
        with engine.connect() as conn:
            # First, drop all indexes
            result = conn.execute(text("""
                SELECT indexname FROM pg_indexes
                WHERE schemaname = 'public'
                AND indexname NOT LIKE 'pg_%'
                AND indexname NOT LIKE '%_pkey'
            """))
            indexes = [row[0] for row in result]

            if indexes:
                print(f"Dropping {len(indexes)} indexes...")
                for index in indexes:
                    try:
                        conn.execute(text(f"DROP INDEX IF EXISTS {index} CASCADE"))
                        print(f"Dropped index: {index}")
                    except Exception as e:
                        print(f"Error dropping index {index}: {e}")

            # Then drop all tables
            result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public'"))
            tables = [row[0] for row in result]

            if tables:
                print(f"Dropping {len(tables)} tables...")
                for table in tables:
                    try:
                        conn.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE"))
                        print(f"Dropped table: {table}")
                    except Exception as e:
                        print(f"Error dropping table {table}: {e}")

                conn.commit()
                print("All tables and indexes dropped successfully")
            else:
                print("No tables to drop")

    except Exception as e:
        print(f"Error dropping tables: {e}")

def create_all_tables():
    """Create all tables using SQLAlchemy"""
    try:
        Base.metadata.create_all(bind=engine)
        print("All tables created successfully")
        return True
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

def verify_tables():
    """Verify that tables were created"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public'"))
            tables = [row[0] for row in result]
            print(f"Tables created: {tables}")
            return len(tables) > 0
    except Exception as e:
        print(f"Error verifying tables: {e}")
        return False

if __name__ == "__main__":
    print("Resetting database...")
    print("=" * 50)
    
    # Step 1: Drop all tables
    drop_all_tables()
    
    # Step 2: Create all tables
    print("\nCreating tables...")
    success = create_all_tables()
    
    if success:
        # Step 3: Verify tables
        print("\nVerifying tables...")
        if verify_tables():
            print("\n✅ Database reset completed successfully!")
        else:
            print("\n❌ Table verification failed!")
    else:
        print("\n❌ Database reset failed!")
