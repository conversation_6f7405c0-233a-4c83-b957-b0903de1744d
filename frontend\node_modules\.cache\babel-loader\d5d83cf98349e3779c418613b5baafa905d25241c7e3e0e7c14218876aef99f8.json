{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\mthunzibackend\\\\frontend\\\\src\\\\components\\\\Policy\\\\PolicyDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Alert, Spinner, Badge, Table } from 'react-bootstrap';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport apiService from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PolicyDetails = () => {\n  _s();\n  const [policy, setPolicy] = useState(null);\n  const [policyholder, setPolicyholder] = useState(null);\n  const [policyMandate, setPolicyMandate] = useState(null);\n  const [policySummary, setPolicySummary] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n  useEffect(() => {\n    fetchPolicyDetails();\n  }, [id]);\n  const fetchPolicyDetails = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch policy details\n      const policyResponse = await apiService.getPolicy(id);\n      setPolicy(policyResponse.data);\n\n      // Try to fetch related data\n      try {\n        const policyholderResponse = await apiService.searchPolicyholders(policyResponse.data.policyno);\n        if (policyholderResponse.data.length > 0) {\n          setPolicyholder(policyholderResponse.data[0]);\n        }\n      } catch (err) {\n        console.log('No policyholder found');\n      }\n\n      // Fetch other related data similarly...\n    } catch (err) {\n      console.error('Error fetching policy details:', err);\n      setError('Failed to load policy details');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusBadge = status => {\n    const statusMap = {\n      'Active': 'success',\n      'Pending': 'warning',\n      'Expired': 'danger',\n      'Cancelled': 'secondary'\n    };\n    return statusMap[status] || 'secondary';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        variant: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this);\n  }\n  if (!policy) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"warning\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-info-circle me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), \"Policy not found\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"h3 mb-0 text-gray-800\",\n              children: \"Policy Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: [\"Policy Number: \", policy.policyno]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: () => navigate(`/policies/${id}/edit`),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-edit me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), \"Edit Policy\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              onClick: () => navigate('/policies'),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), \"Back to Policies\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-file-contract me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), \"Policy Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  borderless: true,\n                  children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Policy Number:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 133,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.policyno\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 134,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Package:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 137,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.packages\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 138,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Business Unit:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.bus\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 142,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 145,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: getStatusBadge(policy.status),\n                          children: policy.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 147,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 146,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Applicant Signed Date:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.applicantsigneddate\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 154,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Date Created:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 157,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.datecreated\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 158,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  borderless: true,\n                  children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Agent Code:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 167,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.agentcode\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 168,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Agent Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 171,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.agentname\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Scheme Code:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 175,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.schemecode || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 176,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Worksite Code:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 179,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.worksitecode || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 180,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Risk Profile:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.riskprofile || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), policyholder && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), \"Policyholder Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  borderless: true,\n                  children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Title:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Full Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: `${policyholder.initials} ${policyholder.surname}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Gender:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 216,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.gender\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Date of Birth:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.dateofbirth\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Country of Birth:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 224,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.countryofbirth\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  borderless: true,\n                  children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Marital Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 234,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.maritalstatus\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Primary ID:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 238,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.primaryid\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Email:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.emailaddress || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Phone:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.phoneno || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: \"Occupation:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policyholder.occupation || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-bolt me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), \"Quick Actions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-grid gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: () => navigate(`/policies/${id}/edit`),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-edit me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), \"Edit Policy\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-print me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), \"Print Policy\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-success\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-download me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), \"Download PDF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-warning\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-envelope me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), \"Send Email\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-chart-line me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), \"Policy Summary\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-primary\",\n                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: getStatusBadge(policy.status),\n                    className: \"fs-6\",\n                    children: policy.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-end\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h5 mb-0\",\n                      children: \"0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"Claims\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h5 mb-0\",\n                    children: \"$0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Premium Paid\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-history me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), \"Recent Activities\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted py-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock fa-2x mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No recent activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(PolicyDetails, \"0TSvxS64pifM9bkCRLQfh+uiQZ0=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = PolicyDetails;\nexport default PolicyDetails;\nvar _c;\n$RefreshReg$(_c, \"PolicyDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Badge", "Table", "useNavigate", "useParams", "apiService", "jsxDEV", "_jsxDEV", "PolicyDetails", "_s", "policy", "setPolicy", "policyholder", "setPolicyholder", "policyMandate", "setPolicyMandate", "policySummary", "setPolicySummary", "loading", "setLoading", "error", "setError", "navigate", "id", "fetchPolicyDetails", "policyResponse", "getPolicy", "data", "policyholderResponse", "searchPolicyholders", "policyno", "length", "err", "console", "log", "getStatusBadge", "status", "statusMap", "className", "style", "height", "children", "animation", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fluid", "onClick", "lg", "Header", "Body", "md", "borderless", "packages", "bus", "bg", "applicantsigneddate", "datecreated", "agentcode", "agentname", "schemecode", "worksitecode", "riskprofile", "title", "initials", "surname", "gender", "dateofbirth", "countryofbirth", "<PERSON><PERSON><PERSON>", "primaryid", "emailaddress", "phoneno", "occupation", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/mthunzibackend/frontend/src/components/Policy/PolicyDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, Table } from 'react-bootstrap';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport apiService from '../../services/apiService';\n\nconst PolicyDetails = () => {\n  const [policy, setPolicy] = useState(null);\n  const [policyholder, setPolicyholder] = useState(null);\n  const [policyMandate, setPolicyMandate] = useState(null);\n  const [policySummary, setPolicySummary] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const { id } = useParams();\n\n  useEffect(() => {\n    fetchPolicyDetails();\n  }, [id]);\n\n  const fetchPolicyDetails = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch policy details\n      const policyResponse = await apiService.getPolicy(id);\n      setPolicy(policyResponse.data);\n      \n      // Try to fetch related data\n      try {\n        const policyholderResponse = await apiService.searchPolicyholders(policyResponse.data.policyno);\n        if (policyholderResponse.data.length > 0) {\n          setPolicyholder(policyholderResponse.data[0]);\n        }\n      } catch (err) {\n        console.log('No policyholder found');\n      }\n      \n      // Fetch other related data similarly...\n      \n    } catch (err) {\n      console.error('Error fetching policy details:', err);\n      setError('Failed to load policy details');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusBadge = (status) => {\n    const statusMap = {\n      'Active': 'success',\n      'Pending': 'warning',\n      'Expired': 'danger',\n      'Cancelled': 'secondary'\n    };\n    return statusMap[status] || 'secondary';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\n        <Spinner animation=\"border\" variant=\"primary\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container fluid>\n        <Alert variant=\"danger\">\n          <i className=\"fas fa-exclamation-triangle me-2\"></i>\n          {error}\n        </Alert>\n      </Container>\n    );\n  }\n\n  if (!policy) {\n    return (\n      <Container fluid>\n        <Alert variant=\"warning\">\n          <i className=\"fas fa-info-circle me-2\"></i>\n          Policy not found\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h1 className=\"h3 mb-0 text-gray-800\">Policy Details</h1>\n              <p className=\"text-muted\">Policy Number: {policy.policyno}</p>\n            </div>\n            <div className=\"d-flex gap-2\">\n              <Button \n                variant=\"primary\" \n                onClick={() => navigate(`/policies/${id}/edit`)}\n              >\n                <i className=\"fas fa-edit me-2\"></i>\n                Edit Policy\n              </Button>\n              <Button \n                variant=\"outline-secondary\" \n                onClick={() => navigate('/policies')}\n              >\n                <i className=\"fas fa-arrow-left me-2\"></i>\n                Back to Policies\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      <Row>\n        <Col lg={8}>\n          {/* Policy Information */}\n          <Card className=\"mb-4\">\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-file-contract me-2\"></i>\n                Policy Information\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              <Row>\n                <Col md={6}>\n                  <Table borderless>\n                    <tbody>\n                      <tr>\n                        <td className=\"fw-bold\">Policy Number:</td>\n                        <td>{policy.policyno}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Package:</td>\n                        <td>{policy.packages}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Business Unit:</td>\n                        <td>{policy.bus}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Status:</td>\n                        <td>\n                          <Badge bg={getStatusBadge(policy.status)}>\n                            {policy.status}\n                          </Badge>\n                        </td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Applicant Signed Date:</td>\n                        <td>{policy.applicantsigneddate}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Date Created:</td>\n                        <td>{policy.datecreated}</td>\n                      </tr>\n                    </tbody>\n                  </Table>\n                </Col>\n                <Col md={6}>\n                  <Table borderless>\n                    <tbody>\n                      <tr>\n                        <td className=\"fw-bold\">Agent Code:</td>\n                        <td>{policy.agentcode}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Agent Name:</td>\n                        <td>{policy.agentname}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Scheme Code:</td>\n                        <td>{policy.schemecode || 'N/A'}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Worksite Code:</td>\n                        <td>{policy.worksitecode || 'N/A'}</td>\n                      </tr>\n                      <tr>\n                        <td className=\"fw-bold\">Risk Profile:</td>\n                        <td>{policy.riskprofile || 'N/A'}</td>\n                      </tr>\n                    </tbody>\n                  </Table>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n\n          {/* Policyholder Information */}\n          {policyholder && (\n            <Card className=\"mb-4\">\n              <Card.Header>\n                <h5 className=\"mb-0\">\n                  <i className=\"fas fa-user me-2\"></i>\n                  Policyholder Information\n                </h5>\n              </Card.Header>\n              <Card.Body>\n                <Row>\n                  <Col md={6}>\n                    <Table borderless>\n                      <tbody>\n                        <tr>\n                          <td className=\"fw-bold\">Title:</td>\n                          <td>{policyholder.title}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Full Name:</td>\n                          <td>{`${policyholder.initials} ${policyholder.surname}`}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Gender:</td>\n                          <td>{policyholder.gender}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Date of Birth:</td>\n                          <td>{policyholder.dateofbirth}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Country of Birth:</td>\n                          <td>{policyholder.countryofbirth}</td>\n                        </tr>\n                      </tbody>\n                    </Table>\n                  </Col>\n                  <Col md={6}>\n                    <Table borderless>\n                      <tbody>\n                        <tr>\n                          <td className=\"fw-bold\">Marital Status:</td>\n                          <td>{policyholder.maritalstatus}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Primary ID:</td>\n                          <td>{policyholder.primaryid}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Email:</td>\n                          <td>{policyholder.emailaddress || 'N/A'}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Phone:</td>\n                          <td>{policyholder.phoneno || 'N/A'}</td>\n                        </tr>\n                        <tr>\n                          <td className=\"fw-bold\">Occupation:</td>\n                          <td>{policyholder.occupation || 'N/A'}</td>\n                        </tr>\n                      </tbody>\n                    </Table>\n                  </Col>\n                </Row>\n              </Card.Body>\n            </Card>\n          )}\n        </Col>\n\n        <Col lg={4}>\n          {/* Quick Actions */}\n          <Card className=\"mb-4\">\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-bolt me-2\"></i>\n                Quick Actions\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"d-grid gap-2\">\n                <Button variant=\"primary\" onClick={() => navigate(`/policies/${id}/edit`)}>\n                  <i className=\"fas fa-edit me-2\"></i>\n                  Edit Policy\n                </Button>\n                <Button variant=\"outline-info\">\n                  <i className=\"fas fa-print me-2\"></i>\n                  Print Policy\n                </Button>\n                <Button variant=\"outline-success\">\n                  <i className=\"fas fa-download me-2\"></i>\n                  Download PDF\n                </Button>\n                <Button variant=\"outline-warning\">\n                  <i className=\"fas fa-envelope me-2\"></i>\n                  Send Email\n                </Button>\n              </div>\n            </Card.Body>\n          </Card>\n\n          {/* Policy Summary */}\n          <Card className=\"mb-4\">\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-chart-line me-2\"></i>\n                Policy Summary\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"text-center\">\n                <div className=\"mb-3\">\n                  <h3 className=\"text-primary\">\n                    <Badge bg={getStatusBadge(policy.status)} className=\"fs-6\">\n                      {policy.status}\n                    </Badge>\n                  </h3>\n                </div>\n                <div className=\"row text-center\">\n                  <div className=\"col-6\">\n                    <div className=\"border-end\">\n                      <div className=\"h5 mb-0\">0</div>\n                      <small className=\"text-muted\">Claims</small>\n                    </div>\n                  </div>\n                  <div className=\"col-6\">\n                    <div className=\"h5 mb-0\">$0</div>\n                    <small className=\"text-muted\">Premium Paid</small>\n                  </div>\n                </div>\n              </div>\n            </Card.Body>\n          </Card>\n\n          {/* Recent Activities */}\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-history me-2\"></i>\n                Recent Activities\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"text-center text-muted py-3\">\n                <i className=\"fas fa-clock fa-2x mb-2\"></i>\n                <p>No recent activities</p>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default PolicyDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACjG,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,UAAU,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM8B,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB;EAAG,CAAC,GAAGnB,SAAS,CAAC,CAAC;EAE1BX,SAAS,CAAC,MAAM;IACd+B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACD,EAAE,CAAC,CAAC;EAER,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMM,cAAc,GAAG,MAAMpB,UAAU,CAACqB,SAAS,CAACH,EAAE,CAAC;MACrDZ,SAAS,CAACc,cAAc,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAI;QACF,MAAMC,oBAAoB,GAAG,MAAMvB,UAAU,CAACwB,mBAAmB,CAACJ,cAAc,CAACE,IAAI,CAACG,QAAQ,CAAC;QAC/F,IAAIF,oBAAoB,CAACD,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;UACxClB,eAAe,CAACe,oBAAoB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtC;;MAEA;IAEF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEY,GAAG,CAAC;MACpDX,QAAQ,CAAC,+BAA+B,CAAC;IAC3C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,SAAS,GAAG;MAChB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,SAAS,EAAE,QAAQ;MACnB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACD,MAAM,CAAC,IAAI,WAAW;EACzC,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK+B,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC3FlC,OAAA,CAACP,OAAO;QAAC0C,SAAS,EAAC,QAAQ;QAACC,OAAO,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,IAAI3B,KAAK,EAAE;IACT,oBACEb,OAAA,CAACb,SAAS;MAACsD,KAAK;MAAAP,QAAA,eACdlC,OAAA,CAACR,KAAK;QAAC4C,OAAO,EAAC,QAAQ;QAAAF,QAAA,gBACrBlC,OAAA;UAAG+B,SAAS,EAAC;QAAkC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnD3B,KAAK;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,IAAI,CAACrC,MAAM,EAAE;IACX,oBACEH,OAAA,CAACb,SAAS;MAACsD,KAAK;MAAAP,QAAA,eACdlC,OAAA,CAACR,KAAK;QAAC4C,OAAO,EAAC,SAAS;QAAAF,QAAA,gBACtBlC,OAAA;UAAG+B,SAAS,EAAC;QAAyB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,oBAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACExC,OAAA,CAACb,SAAS;IAACsD,KAAK;IAAAP,QAAA,gBACdlC,OAAA,CAACZ,GAAG;MAAC2C,SAAS,EAAC,MAAM;MAAAG,QAAA,eACnBlC,OAAA,CAACX,GAAG;QAAA6C,QAAA,eACFlC,OAAA;UAAK+B,SAAS,EAAC,mDAAmD;UAAAG,QAAA,gBAChElC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAI+B,SAAS,EAAC,uBAAuB;cAAAG,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDxC,OAAA;cAAG+B,SAAS,EAAC,YAAY;cAAAG,QAAA,GAAC,iBAAe,EAAC/B,MAAM,CAACoB,QAAQ;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNxC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAG,QAAA,gBAC3BlC,OAAA,CAACT,MAAM;cACL6C,OAAO,EAAC,SAAS;cACjBM,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,aAAaC,EAAE,OAAO,CAAE;cAAAkB,QAAA,gBAEhDlC,OAAA;gBAAG+B,SAAS,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxC,OAAA,CAACT,MAAM;cACL6C,OAAO,EAAC,mBAAmB;cAC3BM,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,WAAW,CAAE;cAAAmB,QAAA,gBAErClC,OAAA;gBAAG+B,SAAS,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oBAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxC,OAAA,CAACZ,GAAG;MAAA8C,QAAA,gBACFlC,OAAA,CAACX,GAAG;QAACsD,EAAE,EAAE,CAAE;QAAAT,QAAA,gBAETlC,OAAA,CAACV,IAAI;UAACyC,SAAS,EAAC,MAAM;UAAAG,QAAA,gBACpBlC,OAAA,CAACV,IAAI,CAACsD,MAAM;YAAAV,QAAA,eACVlC,OAAA;cAAI+B,SAAS,EAAC,MAAM;cAAAG,QAAA,gBAClBlC,OAAA;gBAAG+B,SAAS,EAAC;cAA2B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACV,IAAI,CAACuD,IAAI;YAAAX,QAAA,eACRlC,OAAA,CAACZ,GAAG;cAAA8C,QAAA,gBACFlC,OAAA,CAACX,GAAG;gBAACyD,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACTlC,OAAA,CAACL,KAAK;kBAACoD,UAAU;kBAAAb,QAAA,eACflC,OAAA;oBAAAkC,QAAA,gBACElC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3CxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACoB;sBAAQ;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrCxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAAC6C;sBAAQ;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3CxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAAC8C;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpCxC,OAAA;wBAAAkC,QAAA,eACElC,OAAA,CAACN,KAAK;0BAACwD,EAAE,EAAEtB,cAAc,CAACzB,MAAM,CAAC0B,MAAM,CAAE;0BAAAK,QAAA,EACtC/B,MAAM,CAAC0B;wBAAM;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAsB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnDxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACgD;sBAAmB;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1CxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACiD;sBAAW;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNxC,OAAA,CAACX,GAAG;gBAACyD,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACTlC,OAAA,CAACL,KAAK;kBAACoD,UAAU;kBAAAb,QAAA,eACflC,OAAA;oBAAAkC,QAAA,gBACElC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxCxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACkD;sBAAS;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxCxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACmD;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzCxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACoD,UAAU,IAAI;sBAAK;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3CxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACqD,YAAY,IAAI;sBAAK;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1CxC,OAAA;wBAAAkC,QAAA,EAAK/B,MAAM,CAACsD,WAAW,IAAI;sBAAK;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGNnC,YAAY,iBACXL,OAAA,CAACV,IAAI;UAACyC,SAAS,EAAC,MAAM;UAAAG,QAAA,gBACpBlC,OAAA,CAACV,IAAI,CAACsD,MAAM;YAAAV,QAAA,eACVlC,OAAA;cAAI+B,SAAS,EAAC,MAAM;cAAAG,QAAA,gBAClBlC,OAAA;gBAAG+B,SAAS,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,4BAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACV,IAAI,CAACuD,IAAI;YAAAX,QAAA,eACRlC,OAAA,CAACZ,GAAG;cAAA8C,QAAA,gBACFlC,OAAA,CAACX,GAAG;gBAACyD,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACTlC,OAAA,CAACL,KAAK;kBAACoD,UAAU;kBAAAb,QAAA,eACflC,OAAA;oBAAAkC,QAAA,gBACElC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnCxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAACqD;sBAAK;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvCxC,OAAA;wBAAAkC,QAAA,EAAK,GAAG7B,YAAY,CAACsD,QAAQ,IAAItD,YAAY,CAACuD,OAAO;sBAAE;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpCxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAACwD;sBAAM;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3CxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAACyD;sBAAW;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9CxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAAC0D;sBAAc;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNxC,OAAA,CAACX,GAAG;gBAACyD,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACTlC,OAAA,CAACL,KAAK;kBAACoD,UAAU;kBAAAb,QAAA,eACflC,OAAA;oBAAAkC,QAAA,gBACElC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5CxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAAC2D;sBAAa;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxCxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAAC4D;sBAAS;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnCxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAAC6D,YAAY,IAAI;sBAAK;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnCxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAAC8D,OAAO,IAAI;sBAAK;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACLxC,OAAA;sBAAAkC,QAAA,gBACElC,OAAA;wBAAI+B,SAAS,EAAC,SAAS;wBAAAG,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxCxC,OAAA;wBAAAkC,QAAA,EAAK7B,YAAY,CAAC+D,UAAU,IAAI;sBAAK;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxC,OAAA,CAACX,GAAG;QAACsD,EAAE,EAAE,CAAE;QAAAT,QAAA,gBAETlC,OAAA,CAACV,IAAI;UAACyC,SAAS,EAAC,MAAM;UAAAG,QAAA,gBACpBlC,OAAA,CAACV,IAAI,CAACsD,MAAM;YAAAV,QAAA,eACVlC,OAAA;cAAI+B,SAAS,EAAC,MAAM;cAAAG,QAAA,gBAClBlC,OAAA;gBAAG+B,SAAS,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACV,IAAI,CAACuD,IAAI;YAAAX,QAAA,eACRlC,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAG,QAAA,gBAC3BlC,OAAA,CAACT,MAAM;gBAAC6C,OAAO,EAAC,SAAS;gBAACM,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,aAAaC,EAAE,OAAO,CAAE;gBAAAkB,QAAA,gBACxElC,OAAA;kBAAG+B,SAAS,EAAC;gBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxC,OAAA,CAACT,MAAM;gBAAC6C,OAAO,EAAC,cAAc;gBAAAF,QAAA,gBAC5BlC,OAAA;kBAAG+B,SAAS,EAAC;gBAAmB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxC,OAAA,CAACT,MAAM;gBAAC6C,OAAO,EAAC,iBAAiB;gBAAAF,QAAA,gBAC/BlC,OAAA;kBAAG+B,SAAS,EAAC;gBAAsB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxC,OAAA,CAACT,MAAM;gBAAC6C,OAAO,EAAC,iBAAiB;gBAAAF,QAAA,gBAC/BlC,OAAA;kBAAG+B,SAAS,EAAC;gBAAsB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,cAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGPxC,OAAA,CAACV,IAAI;UAACyC,SAAS,EAAC,MAAM;UAAAG,QAAA,gBACpBlC,OAAA,CAACV,IAAI,CAACsD,MAAM;YAAAV,QAAA,eACVlC,OAAA;cAAI+B,SAAS,EAAC,MAAM;cAAAG,QAAA,gBAClBlC,OAAA;gBAAG+B,SAAS,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACV,IAAI,CAACuD,IAAI;YAAAX,QAAA,eACRlC,OAAA;cAAK+B,SAAS,EAAC,aAAa;cAAAG,QAAA,gBAC1BlC,OAAA;gBAAK+B,SAAS,EAAC,MAAM;gBAAAG,QAAA,eACnBlC,OAAA;kBAAI+B,SAAS,EAAC,cAAc;kBAAAG,QAAA,eAC1BlC,OAAA,CAACN,KAAK;oBAACwD,EAAE,EAAEtB,cAAc,CAACzB,MAAM,CAAC0B,MAAM,CAAE;oBAACE,SAAS,EAAC,MAAM;oBAAAG,QAAA,EACvD/B,MAAM,CAAC0B;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNxC,OAAA;gBAAK+B,SAAS,EAAC,iBAAiB;gBAAAG,QAAA,gBAC9BlC,OAAA;kBAAK+B,SAAS,EAAC,OAAO;kBAAAG,QAAA,eACpBlC,OAAA;oBAAK+B,SAAS,EAAC,YAAY;oBAAAG,QAAA,gBACzBlC,OAAA;sBAAK+B,SAAS,EAAC,SAAS;sBAAAG,QAAA,EAAC;oBAAC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChCxC,OAAA;sBAAO+B,SAAS,EAAC,YAAY;sBAAAG,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxC,OAAA;kBAAK+B,SAAS,EAAC,OAAO;kBAAAG,QAAA,gBACpBlC,OAAA;oBAAK+B,SAAS,EAAC,SAAS;oBAAAG,QAAA,EAAC;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjCxC,OAAA;oBAAO+B,SAAS,EAAC,YAAY;oBAAAG,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGPxC,OAAA,CAACV,IAAI;UAAA4C,QAAA,gBACHlC,OAAA,CAACV,IAAI,CAACsD,MAAM;YAAAV,QAAA,eACVlC,OAAA;cAAI+B,SAAS,EAAC,MAAM;cAAAG,QAAA,gBAClBlC,OAAA;gBAAG+B,SAAS,EAAC;cAAqB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACV,IAAI,CAACuD,IAAI;YAAAX,QAAA,eACRlC,OAAA;cAAK+B,SAAS,EAAC,6BAA6B;cAAAG,QAAA,gBAC1ClC,OAAA;gBAAG+B,SAAS,EAAC;cAAyB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CxC,OAAA;gBAAAkC,QAAA,EAAG;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACtC,EAAA,CAnVID,aAAa;EAAA,QAOAL,WAAW,EACbC,SAAS;AAAA;AAAAwE,EAAA,GARpBpE,aAAa;AAqVnB,eAAeA,aAAa;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}