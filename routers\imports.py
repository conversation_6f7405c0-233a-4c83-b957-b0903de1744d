from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import FileResponse
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import List, Optional
import pandas as pd
import io
import os
from datetime import datetime

import models, schema, auth

def convert_date_format(date_str: str, field_name: str = "date") -> str:
    """
    Convert date from DD/MM/YYYY format to YYYY-MM-DD format

    Args:
        date_str: Date string in DD/MM/YYYY format
        field_name: Name of the field for error messages

    Returns:
        Date string in YYYY-MM-DD format

    Raises:
        ValueError: If date format is invalid
    """
    if not date_str or pd.isna(date_str) or str(date_str).strip() == '':
        return ''

    date_str = str(date_str).strip()

    try:
        # Parse DD/MM/YYYY format
        parsed_date = datetime.strptime(date_str, '%d/%m/%Y')
        # Return in YYYY-MM-DD format
        return parsed_date.strftime('%Y-%m-%d')
    except ValueError:
        # Try alternative formats as fallback
        try:
            # Try DD-MM-YYYY format
            parsed_date = datetime.strptime(date_str, '%d-%m-%Y')
            return parsed_date.strftime('%Y-%m-%d')
        except ValueError:
            try:
                # Try YYYY-MM-DD format (already correct)
                parsed_date = datetime.strptime(date_str, '%Y-%m-%d')
                return date_str  # Already in correct format
            except ValueError:
                raise ValueError(f"Invalid {field_name} format '{date_str}'. Use DD/MM/YYYY format (e.g., 15/06/2024)")
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/imports",
    tags=['Data Import'],
)

def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = auth.decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

@router.get("/template/{import_type}")
async def download_template(
    import_type: str,
    current_user: dict = Depends(get_current_user)
):
    """Download CSV template for the specified import type"""

    # Validate import type
    if import_type not in ["policy", "policyholder", "policymandate", "beneficiaries"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid import type '{import_type}'. Supported types are: policy, policyholder, policymandate, beneficiaries."
        )

    # Template file path
    template_path = f"import_templates/{import_type}_template.csv"

    # Check if template file exists
    if not os.path.exists(template_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template file for '{import_type}' not found."
        )

    # Return the file
    return FileResponse(
        path=template_path,
        filename=f"{import_type}_import_template.csv",
        media_type="text/csv"
    )

@router.post("/validate")
async def validate_import_file(
    file: UploadFile = File(...),
    import_type: str = Form(...),
    current_user: dict = Depends(get_current_user)
):
    """Validate the uploaded file and return preview data"""

    # Debug logging
    print(f"\n=== VALIDATION REQUEST DEBUG ===")
    print(f"File: {file.filename}")
    print(f"Content Type: {file.content_type}")
    print(f"Import Type: {import_type}")
    print(f"User: {current_user.get('username', 'Unknown')}")

    # Check file type
    if not file.filename or not file.filename.endswith(('.csv', '.xlsx', '.xls')):
        print(f"ERROR: Invalid file type - {file.filename}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only CSV and Excel files are supported"
        )
    
    try:
        # Read file content
        content = await file.read()
        print(f"File size: {len(content)} bytes")

        # Validate file is not empty
        if len(content) == 0:
            print("ERROR: File is empty")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The uploaded file is empty. Please upload a file with data."
            )

        # Parse based on file type
        try:
            print(f"Parsing file as: {'CSV' if file.filename and file.filename.endswith('.csv') else 'Excel'}")
            if file.filename and file.filename.endswith('.csv'):
                # Show first 200 chars of content for debugging
                content_preview = content.decode('utf-8')[:200]
                print(f"CSV content preview: {content_preview}")
                df = pd.read_csv(io.StringIO(content.decode('utf-8')))
            else:
                df = pd.read_excel(io.BytesIO(content))

            print(f"DataFrame shape: {df.shape}")
            print(f"DataFrame columns: {list(df.columns)}")
            print(f"First few rows:\n{df.head()}")

        except UnicodeDecodeError as ude:
            print(f"ERROR: Unicode decode error - {ude}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File encoding error. Please ensure your CSV file is saved with UTF-8 encoding."
            )
        except pd.errors.EmptyDataError as ede:
            print(f"ERROR: Empty data error - {ede}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The file contains no data. Please upload a file with at least a header row."
            )
        except pd.errors.ParserError as pe:
            print(f"ERROR: Parser error - {pe}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File parsing error: {str(pe)}. Please check your file format and ensure it's a valid CSV or Excel file."
            )

        # Check if dataframe is empty
        if df.empty:
            print("ERROR: DataFrame is empty")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The file contains no data rows. Please upload a file with data."
            )

        # Validate import type
        print(f"Validating import type: {import_type}")
        if import_type not in ["policy", "policyholder", "policymandate", "beneficiaries"]:
            print(f"ERROR: Invalid import type - {import_type}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid import type '{import_type}'. Supported types are: policy, policyholder, policymandate, beneficiaries."
            )

        # Validate import type and required columns
        required_columns = get_required_columns(import_type)
        print(f"Required columns for {import_type}: {required_columns}")
        if not required_columns:
            print(f"ERROR: No column configuration for {import_type}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No column configuration found for import type '{import_type}'."
            )

        # Check for missing columns
        file_columns = [col.strip().lower() for col in df.columns]
        required_columns_lower = [col.lower() for col in required_columns]
        missing_columns = [col for col in required_columns if col.lower() not in file_columns]

        print(f"File columns (normalized): {file_columns}")
        print(f"Required columns (normalized): {required_columns_lower}")
        print(f"Missing columns: {missing_columns}")

        # Check for completely empty columns
        empty_columns = [col for col in df.columns if df[col].isna().all()]
        print(f"Empty columns: {empty_columns}")

        # Additional validation for all import types
        validation_errors = []

        if import_type == "policy":
            print(f"=== POLICY VALIDATION DETAILS ===")

            try:
                # Convert dataframe to list of dictionaries to avoid pandas type issues
                records = df.to_dict('records')

                for idx, record in enumerate(records):
                    row_errors = []
                    row_number = idx + 2  # +2 for header and 0-based index

                    # Check required fields
                    for req_col in required_columns:
                        # Find the actual column name (case insensitive)
                        actual_col = None
                        for col_name in record.keys():
                            if str(col_name).strip().lower() == req_col.lower():
                                actual_col = col_name
                                break

                        if actual_col is not None:
                            cell_value = record[actual_col]
                            if pd.isna(cell_value) or str(cell_value).strip() == '':
                                row_errors.append(f"'{req_col}' is empty")

                    # Check boolean fields format
                    for bool_field in ['bus', 'pep']:
                        actual_col = None
                        for col_name in record.keys():
                            if str(col_name).strip().lower() == bool_field.lower():
                                actual_col = col_name
                                break

                        if actual_col is not None:
                            cell_value = record[actual_col]
                            if not pd.isna(cell_value):
                                val = str(cell_value).strip().lower()
                                if val not in ['true', 'false', '1', '0', 'yes', 'no']:
                                    row_errors.append(f"'{bool_field}' has invalid boolean value: '{cell_value}'")

                    # Check packages field
                    packages_col = None
                    for col_name in record.keys():
                        if str(col_name).strip().lower() == 'packages':
                            packages_col = col_name
                            break

                    if packages_col is not None:
                        cell_value = record[packages_col]
                        if not pd.isna(cell_value):
                            packages_val = str(cell_value).strip()
                            if packages_val not in ['Standard', 'Premier', 'Lite']:
                                row_errors.append(f"'packages' has invalid value: '{packages_val}'. Must be: Standard, Premier, or Lite")

                    if row_errors:
                        validation_errors.append({
                            "row": row_number,
                            "errors": row_errors,
                            "data": {k: str(v) for k, v in record.items() if not pd.isna(v)}
                        })

                print(f"Found {len(validation_errors)} rows with validation errors")
                if validation_errors:
                    for error in validation_errors[:3]:  # Show first 3 errors
                        print(f"Row {error['row']}: {error['errors']}")

            except Exception as validation_error:
                print(f"Error during validation: {str(validation_error)}")
                import traceback
                print(f"Validation traceback: {traceback.format_exc()}")
                # Don't fail the entire validation, just log the error
                validation_errors = []

        # Return preview data (first 5 rows)
        try:
            preview_data = df.head(5).to_dict('records')
            # Convert any NaN values to None for JSON serialization
            for row in preview_data:
                for key, value in row.items():
                    if pd.isna(value):
                        row[key] = None
        except Exception as preview_error:
            print(f"Error creating preview data: {preview_error}")
            preview_data = []

        response_data = {
            "status": "success" if not missing_columns and not validation_errors else "warning",
            "total_rows": len(df),
            "columns": list(df.columns),
            "preview_data": preview_data,
            "missing_columns": missing_columns,
            "required_columns": required_columns,
            "empty_columns": empty_columns,
            "validation_errors": validation_errors[:10] if validation_errors else [],  # Return first 10 errors
            "import_type": import_type,
            "file_info": {
                "filename": file.filename,
                "size_bytes": len(content),
                "rows_with_data": len(df.dropna(how='all'))
            }
        }

        print(f"SUCCESS: Validation completed")
        print(f"Response: {response_data}")
        print("=== END VALIDATION DEBUG ===\n")

        return response_data

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Catch any other unexpected errors with detailed logging
        import traceback
        error_type = type(e).__name__
        error_details = str(e)
        stack_trace = traceback.format_exc()

        # Log the full error for debugging
        print(f"IMPORT VALIDATION ERROR:")
        print(f"Error Type: {error_type}")
        print(f"Error Message: {error_details}")
        print(f"File: {file.filename}")
        print(f"Import Type: {import_type}")
        print(f"Stack Trace:\n{stack_trace}")

        # Return detailed error to frontend
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_category": "System Error",
                "error_type": error_type,
                "error_message": f"Unexpected error processing file: {error_details}",
                "file_info": {
                    "filename": file.filename,
                    "import_type": import_type
                },
                "troubleshooting": [
                    "Check if the file format is correct (CSV or Excel)",
                    "Ensure the file is not corrupted",
                    "Verify the file contains valid data",
                    "Try with a smaller file to test"
                ]
            }
        )

@router.post("/process")
async def process_import(
    file: UploadFile = File(...),
    import_type: str = Form(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Process the import and insert data into database"""
    
    try:
        # Read file content
        content = await file.read()

        # Validate file is not empty
        if len(content) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The uploaded file is empty. Please upload a file with data."
            )

        # Parse based on file type
        try:
            if file.filename and file.filename.endswith('.csv'):
                df = pd.read_csv(io.StringIO(content.decode('utf-8')))
            else:
                df = pd.read_excel(io.BytesIO(content))
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File encoding error. Please ensure your CSV file is saved with UTF-8 encoding."
            )
        except pd.errors.EmptyDataError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The file contains no data. Please upload a file with at least a header row."
            )
        except pd.errors.ParserError as pe:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File parsing error: {str(pe)}. Please check your file format."
            )

        # Check if dataframe is empty
        if df.empty:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The file contains no data rows. Please upload a file with data."
            )

        # Process based on import type
        result = await process_import_data(df, import_type, current_user, db)

        return result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        db.rollback()
        raise
    except Exception as e:
        # Catch any other unexpected errors with detailed logging
        db.rollback()
        import traceback
        error_type = type(e).__name__
        error_details = str(e)
        stack_trace = traceback.format_exc()

        # Log the full error for debugging
        print(f"IMPORT PROCESSING ERROR:")
        print(f"Error Type: {error_type}")
        print(f"Error Message: {error_details}")
        print(f"File: {file.filename}")
        print(f"Import Type: {import_type}")
        print(f"Stack Trace:\n{stack_trace}")

        # Return detailed error to frontend
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_category": "Processing Error",
                "error_type": error_type,
                "error_message": f"Unexpected error during import processing: {error_details}",
                "file_info": {
                    "filename": file.filename,
                    "import_type": import_type
                },
                "troubleshooting": [
                    "Check if all required columns are present",
                    "Verify data format matches expected types",
                    "Ensure referenced records exist (for foreign keys)",
                    "Try processing a smaller batch of data"
                ]
            }
        )

def get_required_columns(import_type: str) -> List[str]:
    """Get required columns for each import type"""
    
    column_mappings = {
        "policy": [
            "policyno", "packages", "bus", "pep", "agentcode", "agentname", "applicantsigneddate", "status"
            # Optional fields: schemecode, worksitecode, riskprofile, agentsigneddate
        ],
        "policyholder": [
            "policyno", "title", "initials", "firstname", "surname", "dateofbirth",
            "gender", "maritalstatus", "capturedate"
            # Optional fields: idnumber, idtype, occupation, mobilenumber, alternativenumber,
            # postaladdressline1, postaladdressline2, postaladdressline3, postaladdressline4,
            # postalcity, postalcountrycode, town, emailaddress, residentialaddressline1,
            # residentialcountrycode, residentialdistrict, residentialvillage,
            # traditionalauthority, contractsigneddate, residentialstatus
        ],
        "policymandate": [
            "policyno", "firstname", "surname", "mobilenumber", "frequency",
            "premium", "modeofpayment", "FirstDeductionStartDate", "PaypointName"
            # Optional: BankAccountNumber, BankAccountType, BranchName
        ],
        "beneficiaries": [
            "policyno", "title", "initials", "firstname", "surname", "gender",
            "birthdate", "relationship", "membertype"
            # Optional: idnumber
        ]
    }
    
    return column_mappings.get(import_type, [])

async def process_import_data(df: pd.DataFrame, import_type: str, current_user: dict, db: Session):
    """Process the import data based on type"""
    
    success_count = 0
    error_count = 0
    errors = []
    
    for index, row in df.iterrows():
        try:
            if import_type == "policy":
                await process_policy_row(row, current_user, db)
            elif import_type == "policyholder":
                await process_policyholder_row(row, current_user, db)
            elif import_type == "policymandate":
                await process_policymandate_row(row, current_user, db)
            elif import_type == "beneficiaries":
                await process_beneficiaries_row(row, current_user, db)
            else:
                raise ValueError(f"Unsupported import type: {import_type}")
            
            success_count += 1
            
        except Exception as e:
            error_count += 1
            error_type = type(e).__name__

            # Create more informative error message
            error_message = str(e)
            if "already exists" in error_message:
                error_category = "Duplicate Record"
            elif "not found" in error_message:
                error_category = "Missing Reference"
            elif "required" in error_message.lower() or "cannot be null" in error_message.lower():
                error_category = "Missing Required Field"
            elif "invalid" in error_message.lower():
                error_category = "Invalid Data"
            else:
                error_category = "Processing Error"

            errors.append({
                "row": str(index) + "_row",  # Convert index to string to avoid type issues
                "error_category": error_category,
                "error_type": error_type,
                "error_message": error_message,
                "data": {k: v for k, v in row.to_dict().items() if pd.notna(v)}  # Only include non-null values
            })

    # Commit all successful transactions
    if success_count > 0:
        db.commit()
    
    return {
        "status": "completed",
        "total_rows": len(df),
        "success_count": success_count,
        "error_count": error_count,
        "errors": errors[:10]  # Return first 10 errors
    }

async def process_policy_row(row: pd.Series, current_user: dict, db: Session):
    """Process a single policy row"""

    # Validate required fields (schemecode, worksitecode, riskprofile are now optional)
    required_fields = ['policyno', 'packages', 'bus', 'pep', 'agentcode', 'agentname', 'applicantsigneddate', 'status']
    missing_fields = []

    for field in required_fields:
        try:
            if field not in row.index:
                missing_fields.append(field)
                continue

            value = row[field]
            # Convert to string and check if empty
            str_value = str(value).strip()
            if str_value == '' or str_value.lower() == 'nan':
                missing_fields.append(field)
        except Exception:
            missing_fields.append(field)

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    # Validate policy number format
    policyno = str(row['policyno']).strip()
    if not policyno:
        raise ValueError("Policy number cannot be empty")

    # Check if policy already exists
    existing_policy = db.query(models.Policy).filter(
        models.Policy.policyno == policyno
    ).first()

    if existing_policy:
        raise ValueError(f"Policy {policyno} already exists in the database")

    # Validate boolean fields
    def parse_boolean(value):
        if pd.isna(value):
            return False
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ['true', '1', 'yes', 'y']
        return bool(value)

    # Validate packages
    valid_packages = ['Lite', 'Standard', 'Premier']
    packages = str(row['packages']).strip()
    if packages not in valid_packages:
        raise ValueError(f"Invalid package '{packages}'. Must be one of: {', '.join(valid_packages)}")

    # Validate and convert applicant signed date from DD/MM/YYYY to YYYY-MM-DD
    applicant_signed_date_raw = str(row['applicantsigneddate']).strip()
    if not applicant_signed_date_raw:
        raise ValueError("Applicant signed date cannot be empty")

    # Convert date format
    applicant_signed_date = convert_date_format(applicant_signed_date_raw, "applicant signed date")

    # Validate status
    valid_statuses = ['Active', 'Pending', 'Inactive', 'Cancelled']
    status = str(row['status']).strip()
    if status not in valid_statuses:
        raise ValueError(f"Invalid status '{status}'. Must be one of: {', '.join(valid_statuses)}")

    # Create policy data
    try:
        policy_data = {
            "policyno": policyno,
            "packages": packages,
            "bus": parse_boolean(row['bus']),
            "policypayer": row.get('policypayer', ''),
            "agentcode": str(row['agentcode']).strip(),
            "agentname": str(row['agentname']).strip(),
            "pep": parse_boolean(row['pep']),
            "aml": row.get('aml', False),
            "applicantsigneddate": applicant_signed_date,
            "agentsigneddate": row.get('agentsigneddate', ''),
            "applicationform": row.get('applicationform', False),
            "kycform": row.get('kycform', False),
            "mandateform": row.get('mandateform', False),
            "schemecode": str(row.get('schemecode', '')).strip(),
            "worksitecode": str(row.get('worksitecode', '')).strip(),
            "riskprofile": str(row.get('riskprofile', '')).strip(),
            "status": status,
            "datecreated": datetime.now().strftime('%Y-%m-%d'),
            "createdby": int(current_user.get('id', 1))
        }

        policy = models.Policy(**policy_data)
        db.add(policy)

    except Exception as e:
        raise ValueError(f"Error creating policy record: {str(e)}")

async def process_policyholder_row(row: pd.Series, current_user: dict, db: Session):
    """Process a single policyholder row"""

    # Validate required fields
    required_fields = ['policyno', 'title', 'initials', 'firstname', 'surname', 'dateofbirth', 'gender', 'maritalstatus', 'capturedate']
    missing_fields = []

    for field in required_fields:
        try:
            if field not in row.index:
                missing_fields.append(field)
                continue

            value = row[field]
            # Convert to string and check if empty
            str_value = str(value).strip()
            if str_value == '' or str_value.lower() == 'nan':
                missing_fields.append(field)
        except Exception:
            missing_fields.append(field)

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    # Check if policy exists
    policyno = str(row['policyno']).strip()
    policy = db.query(models.Policy).filter(
        models.Policy.policyno == policyno
    ).first()

    if not policy:
        raise ValueError(f"Policy {policyno} not found")

    # Check if policyholder already exists for this policy
    existing_holder = db.query(models.PolicyHolder).filter(
        models.PolicyHolder.policyno == policyno
    ).first()

    if existing_holder:
        raise ValueError(f"PolicyHolder for policy {policyno} already exists")

    # Create policyholder data with all available fields
    holder_data = {
        "policyno": policyno,
        "title": row.get('title', ''),
        "initials": row.get('initials', ''),
        "middlename": row.get('middlename', ''),
        "firstname": row.get('firstname', ''),
        "surname": row.get('surname', ''),
        "previousname": row.get('previousname', ''),
        "gender": row.get('gender', ''),
        "dateofbirth": row.get('dateofbirth', ''),
        "countryofbirth": row.get('countryofbirth', ''),
        "maritalstatus": row.get('maritalstatus', ''),
        "residentialstatus": row.get('residentialstatus', ''),
        "primaryid": row.get('primaryid', ''),
        "primaryidexpirydate": row.get('primaryidexpirydate', ''),
        "secondaryid": row.get('secondaryid', ''),
        "secondaryidexpirydate": row.get('secondaryidexpirydate', ''),
        "expatriate": row.get('expatriate', ''),
        "workpermit": row.get('workpermit', ''),
        "workpermitexpirydate": row.get('workpermitexpirydate', ''),
        "sourceofincome": row.get('sourceofincome', ''),
        "netmonthlyincome": row.get('netmonthlyincome', ''),
        "specifyincome": row.get('specifyincome', ''),
        "proofofsourceoffunds": row.get('proofofsourceoffunds', ''),
        "specifyproofofsourceoffunds": row.get('specifyproofofsourceoffunds', ''),
        "occupation": row.get('occupation', ''),
        "industry": row.get('industry', ''),
        "employer": row.get('employer', ''),
        "other": row.get('other', ''),
        "commpreference": row.get('commpreference', ''),
        "emailaddress": row.get('emailaddress', ''),
        "phoneno": row.get('phoneno', ''),
        "altmobileno": row.get('altmobileno', ''),
        "physicaladdressline1": row.get('physicaladdressline1', ''),
        "physicaladdressline2": row.get('physicaladdressline2', ''),
        "physicaladdressline3": row.get('physicaladdressline3', ''),
        "physicaladdressline4": row.get('physicaladdressline4', ''),
        "physicaladdressline5city": row.get('physicaladdressline5city', ''),
        "physicaladdressline6country": row.get('physicaladdressline6country', ''),
        "permanentaddressvillage": row.get('permanentaddressvillage', ''),
        "permanentaddressta": row.get('permanentaddressta', ''),
        "permanentaddressdistrict": row.get('permanentaddressdistrict', ''),
        "postaladdressline1": row.get('postaladdressline1', ''),
        "postaladdressline2": row.get('postaladdressline2', ''),
        "postaladdressline3": row.get('postaladdressline3', ''),
        "postaladdressline4": row.get('postaladdressline4', ''),
        "postaladdressline5city": row.get('postaladdressline5city', ''),
        "postaladdressline6country": row.get('postaladdressline6country', ''),
        "datecreated": datetime.now().strftime('%Y-%m-%d'),
        "createdby": int(current_user.get('id', 1)),
        # New required and optional fields
        "capturedate": str(row.get('capturedate', '')).strip(),
        "idnumber": str(row.get('idnumber', '')).strip(),
        "idtype": str(row.get('idtype', '')).strip(),
        "mobilenumber": str(row.get('mobilenumber', '')).strip(),
        "alternativenumber": str(row.get('alternativenumber', '')).strip(),
        "postalcity": str(row.get('postalcity', '')).strip(),
        "postalcountrycode": str(row.get('postalcountrycode', '')).strip(),
        "town": str(row.get('town', '')).strip(),
        "residentialaddressline1": str(row.get('residentialaddressline1', '')).strip(),
        "residentialcountrycode": str(row.get('residentialcountrycode', '')).strip(),
        "residentialdistrict": str(row.get('residentialdistrict', '')).strip(),
        "residentialvillage": str(row.get('residentialvillage', '')).strip(),
        "traditionalauthority": str(row.get('traditionalauthority', '')).strip(),
        "contractsigneddate": str(row.get('contractsigneddate', '')).strip()
    }
    
    try:
        # Debug: Print the data being inserted
        print(f"DEBUG: Creating PolicyHolder with data:")
        for key, value in holder_data.items():
            print(f"  {key}: '{value}' (type: {type(value)})")

        holder = models.PolicyHolder(**holder_data)
        db.add(holder)
        db.flush()  # Force the insert to happen now to catch the exact error

    except Exception as e:
        print(f"ERROR: Failed to create PolicyHolder: {str(e)}")
        print(f"ERROR TYPE: {type(e)}")

        # Check if it contains "Stella Maris" to identify the problematic field
        if "Stella Maris" in str(e):
            print(f"FOUND 'Stella Maris' in error - checking which field contains this value:")
            for key, value in holder_data.items():
                if value and "Stella Maris" in str(value):
                    print(f"  Field '{key}' contains 'Stella Maris': '{value}'")

        raise ValueError(f"Error creating PolicyHolder record: {str(e)}")

async def process_policymandate_row(row: pd.Series, current_user: dict, db: Session):
    """Process a single policy mandate row"""

    # Validate required fields (BankAccountNumber, BankAccountType and BranchName are now optional)
    required_fields = ['policyno', 'firstname', 'surname', 'mobilenumber', 'frequency',
                      'premium', 'modeofpayment', 'FirstDeductionStartDate', 'PaypointName']
    missing_fields = []

    for field in required_fields:
        try:
            if field not in row.index:
                missing_fields.append(field)
                continue

            value = row[field]
            # Convert to string and check if empty
            str_value = str(value).strip()
            if str_value == '' or str_value.lower() == 'nan':
                missing_fields.append(field)
        except Exception:
            missing_fields.append(field)

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    # Check if policy exists
    policyno = str(row['policyno']).strip()
    policy = db.query(models.Policy).filter(
        models.Policy.policyno == policyno
    ).first()

    if not policy:
        raise ValueError(f"Policy {policyno} not found")

    # Check if mandate already exists for this policy
    existing_mandate = db.query(models.PolicyMandate).filter(
        models.PolicyMandate.policyno == policyno
    ).first()

    if existing_mandate:
        raise ValueError(f"PolicyMandate for policy {policyno} already exists")

    # Create mandate data with field mappings
    paypoint_name = str(row.get('PaypointName', '')).strip()

    mandate_data = {
        "policyno": policyno,
        # Direct mappings
        "frequency": str(row.get('frequency', '')).strip(),
        "premium": str(row.get('premium', '')).strip(),
        "modeofpayment": str(row.get('modeofpayment', '')).strip(),
        "firstdeductiondate": str(row.get('FirstDeductionStartDate', '')).strip(),

        # Mapped field names
        "debitorderFirstname": str(row.get('firstname', '')).strip(),
        "debitorderLastname": str(row.get('surname', '')).strip(),
        "mobilephoneno": str(row.get('mobilenumber', '')).strip(),
        "debitorderAccountno": str(row.get('BankAccountNumber', '')).strip(),  # Optional
        "debitorderTypeofaccount": str(row.get('BankAccountType', '')).strip(),  # Optional
        "debitorderBranch": str(row.get('BranchName', '')).strip(),  # Optional

        # PaypointName maps to multiple fields
        "stoporderEmployername": paypoint_name,
        "mobileoperator": paypoint_name,
        "debitorderNameofbank": paypoint_name,

        # Optional fields (set to empty if not provided)
        "debitorderSalaryfundingdate": '',
        "debitorderPremiumonapplication": '',
        "stoporderEmployeeid": '',
        "stoporderEmployeename": '',
        "stoporderEmployeraddress": '',

        # Audit fields
        "datecreated": datetime.now().strftime('%Y-%m-%d'),
        "createdby": int(current_user.get('id', 1))
    }

    mandate = models.PolicyMandate(**mandate_data)
    db.add(mandate)

async def process_beneficiaries_row(row: pd.Series, current_user: dict, db: Session):
    """Process a single beneficiaries row"""

    # Validate required fields
    required_fields = ['policyno', 'title', 'initials', 'firstname', 'surname', 'gender', 'birthdate', 'relationship', 'membertype']
    missing_fields = []

    for field in required_fields:
        try:
            if field not in row.index:
                missing_fields.append(field)
                continue

            value = row[field]
            # Convert to string and check if empty
            str_value = str(value).strip()
            if str_value == '' or str_value.lower() == 'nan':
                missing_fields.append(field)
        except Exception:
            missing_fields.append(field)

    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    # Check if policy exists
    policyno = str(row['policyno']).strip()
    policy = db.query(models.Policy).filter(
        models.Policy.policyno == policyno
    ).first()

    if not policy:
        raise ValueError(f"Policy with number {policyno} not found")

    # Create beneficiary data
    beneficiary_data = {
        "policyid": str(policy.id),  # Use the policy ID from the found policy
        "policyno": policyno,  # Added policyno field
        "title": str(row.get('title', '')).strip(),
        "initials": str(row.get('initials', '')).strip(),
        "firstname": str(row.get('firstname', '')).strip(),
        "middlename": str(row.get('middlename', '')).strip(),
        "surname": str(row.get('surname', '')).strip(),
        "gender": str(row.get('gender', '')).strip(),
        "birthdate": str(row.get('birthdate', '')).strip(),
        "maritalstatus": str(row.get('maritalstatus', '')).strip(),
        "membertype": str(row.get('membertype', '')).strip(),
        "benofownership": str(row.get('benofownership', '')).strip(),
        "relationship": str(row.get('relationship', '')).strip(),
        "status": str(row.get('status', 'ACTIVE')).strip(),
        "datecreated": datetime.now().strftime('%Y-%m-%d'),
        "createdby": int(current_user.get('id', 1)),
        "idnumber": str(row.get('idnumber', '')).strip()  # Added optional idnumber field
    }

    try:
        beneficiary = models.Beneficiaries(**beneficiary_data)
        db.add(beneficiary)

    except Exception as e:
        raise ValueError(f"Error creating Beneficiary record: {str(e)}")
