"""Add policyno and idnumber fields to Beneficiaries model

Revision ID: 20c36e2a5503
Revises: 5fa5d09864b6
Create Date: 2025-08-05 10:33:30.963785

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '20c36e2a5503'
down_revision: Union[str, Sequence[str], None] = '5fa5d09864b6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('beneficiaries', sa.Column('policyno', sa.String(), nullable=True))
    op.add_column('beneficiaries', sa.Column('idnumber', sa.String(), nullable=True))
    op.create_index(op.f('ix_beneficiaries_idnumber'), 'beneficiaries', ['idnumber'], unique=False)
    op.create_index(op.f('ix_beneficiaries_policyno'), 'beneficiaries', ['policyno'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_beneficiaries_policyno'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_idnumber'), table_name='beneficiaries')
    op.drop_column('beneficiaries', 'idnumber')
    op.drop_column('beneficiaries', 'policyno')
    # ### end Alembic commands ###
