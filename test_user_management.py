#!/usr/bin/env python3
"""
Test user management API functionality
"""

import requests
import json

def test_admin_login():
    """Test login with new admin credentials"""
    try:
        login_data = {
            "username": "admin",
            "password": "password"
        }
        
        response = requests.post(
            'http://localhost:8000/login',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Admin login successful!")
            print(f"   Token: {data.get('access_token', 'No token')[:20]}...")
            return data.get('access_token')
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Admin login test failed: {e}")
        return None

def test_user_management_api(token):
    """Test user management API endpoints"""
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # Test get all users
        print("\n🔍 Testing get all users...")
        response = requests.get('http://localhost:8000/users/', headers=headers)
        if response.status_code == 200:
            users = response.json()
            print(f"✅ Found {len(users)} users")
            for user in users[:3]:  # Show first 3 users
                print(f"   - {user['username']} ({user['email']})")
        else:
            print(f"❌ Get users failed: {response.status_code}")
            return False
        
        # Test search users
        print("\n🔍 Testing search users...")
        response = requests.get('http://localhost:8000/users/search?q=admin', headers=headers)
        if response.status_code == 200:
            search_results = response.json()
            print(f"✅ Search found {len(search_results)} users matching 'admin'")
        else:
            print(f"❌ Search users failed: {response.status_code}")
        
        # Test create user
        print("\n➕ Testing create user...")
        new_user_data = {
            "username": "testuser_api",
            "email": "<EMAIL>",
            "password": "testpass123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "no",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no"
        }
        
        response = requests.post('http://localhost:8000/users/', json=new_user_data, headers=headers)
        if response.status_code == 201:
            created_user = response.json()
            print(f"✅ User created successfully: {created_user['username']}")
            user_id = created_user['id']
            
            # Test update user
            print("\n✏️ Testing update user...")
            update_data = {
                "username": "testuser_api",
                "email": "<EMAIL>",
                "status": "active",
                "viewonly": "no",
                "policyadmin": "yes",  # Changed this
                "servicing": "yes",
                "mimoadmin": "no",
                "sysadmin": "no"
            }
            
            response = requests.put(f'http://localhost:8000/users/{user_id}', json=update_data, headers=headers)
            if response.status_code == 200:
                print("✅ User updated successfully")
            else:
                print(f"❌ Update user failed: {response.status_code}")
            
            # Test change password
            print("\n🔑 Testing change password...")
            password_data = {"new_password": "newpassword123"}
            response = requests.put(f'http://localhost:8000/users/{user_id}/password', json=password_data, headers=headers)
            if response.status_code == 200:
                print("✅ Password changed successfully")
            else:
                print(f"❌ Change password failed: {response.status_code}")
            
            # Test delete user
            print("\n🗑️ Testing delete user...")
            response = requests.delete(f'http://localhost:8000/users/{user_id}', headers=headers)
            if response.status_code == 204:
                print("✅ User deleted successfully")
            else:
                print(f"❌ Delete user failed: {response.status_code}")
                
        elif response.status_code == 400:
            print("⚠️  User might already exist (this is expected)")
        else:
            print(f"❌ Create user failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ User management API test failed: {e}")
        return False

def main():
    print("Testing User Management API...")
    print("=" * 50)
    
    # Test admin login
    token = test_admin_login()
    
    if token:
        # Test user management endpoints
        api_ok = test_user_management_api(token)
        
        print("\n" + "=" * 50)
        if api_ok:
            print("🎉 All user management tests passed!")
            print("\nUser Management Features Available:")
            print("- ✅ View all users")
            print("- ✅ Search users by multiple criteria")
            print("- ✅ Create new users")
            print("- ✅ Update user details")
            print("- ✅ Change user passwords")
            print("- ✅ Delete users")
            print("- ✅ Admin permission checks")
            print("\nAccess the frontend at: http://localhost:3000/users")
            print("Login with: admin / password")
        else:
            print("❌ Some user management tests failed")
    else:
        print("❌ Cannot test user management without valid admin token")

if __name__ == "__main__":
    main()
