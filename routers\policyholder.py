from fastapi import FastAP<PERSON>, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/policyholder",
    tags=['Policyholder'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# get all policyholders
@router.get("/", response_model=List[schema.PolicyHolder])
def get_policyholders(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policyholders = db.query(models.PolicyHolder).offset(skip).limit(limit).all()
    if not policyholders:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policyholders found")
    return policyholders

# get policyholder
@router.get("/{policyholder_id}", response_model=schema.PolicyHolder)
def get_policyholder(policyholder_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policyholder = db.query(models.PolicyHolder).filter(models.PolicyHolder.id == policyholder_id).first()
    if not policyholder:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policyholder not found")
    return policyholder

# search policy holder by policy number
@router.get("/search/{policy_no}", response_model=List[schema.PolicyHolder])
def search_policyholder(policy_no: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policyholders = db.query(models.PolicyHolder).filter(models.PolicyHolder.policyno == policy_no).all()
    if not policyholders:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policyholder not found")
    return policyholders

# search policy holder by firstname and surname
@router.get("/search/{firstname}/{surname}", response_model=List[schema.PolicyHolder])
def search_policyholder_by_name(firstname: str, surname: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policyholders = db.query(models.PolicyHolder).filter(
        func.lower(models.PolicyHolder.firstname) == func.lower(firstname),
        func.lower(models.PolicyHolder.surname) == func.lower(surname)
    ).all()
    if not policyholders:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policyholder not found")
    return policyholders

# create policyholder
@router.post("/", response_model=schema.PolicyHolder, status_code=status.HTTP_201_CREATED)
def create_policyholder(policyholder: schema.PolicyHolder, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    # Check if the policy number already exists
    existing_policyholder = db.query(models.PolicyHolder).filter(models.PolicyHolder.policyno == policyholder.policyno).first()
    if existing_policyholder:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Policyholder with this policy number already exists")
    
    # Create the policyholder
    policyholder_data = models.PolicyHolder(**policyholder.model_dump(), createdby=current_user['id'])
    db.add(policyholder_data)
    db.commit()
    db.refresh(policyholder_data)
    return policyholder_data

# update policyholder
@router.put("/{policyholder_id}", response_model=schema.PolicyHolder)
def update_policyholder(policyholder_id: int, policyholder: schema.PolicyHolder, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policyholder = db.query(models.PolicyHolder).filter(models.PolicyHolder.id == policyholder_id).first()
    if not existing_policyholder:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policyholder not found")
    
    # Check if the policy number already exists
    if policyholder.policyno != existing_policyholder.policyno:
        existing_policy = db.query(models.PolicyHolder).filter(models.PolicyHolder.policyno == policyholder.policyno).first()
        if existing_policy:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Policyholder with this policy number already exists")
    
    for key, value in policyholder.model_dump().items():
        setattr(existing_policyholder, key, value)
    
    db.commit()
    db.refresh(existing_policyholder)
    return existing_policyholder

# delete policyholder
@router.delete("/{policyholder_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_policyholder(policyholder_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policyholder = db.query(models.PolicyHolder).filter(models.PolicyHolder.id == policyholder_id).first()
    if not existing_policyholder:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policyholder not found")
    
    db.delete(existing_policyholder)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)
