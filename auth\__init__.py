# Import functions from the auth.py module to make them available when importing from the auth package
import os
import importlib.util

# Get the path to the auth.py file in the parent directory
auth_py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'auth.py')

# Load the auth.py module directly
spec = importlib.util.spec_from_file_location("auth_module", auth_py_path)
if spec is not None and spec.loader is not None:
    auth_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(auth_module)

    # Import the functions from the loaded module
    hash_password = auth_module.hash_password
    verify_password = auth_module.verify_password
    create_access_token = auth_module.create_access_token
    decode_access_token = auth_module.decode_access_token
    get_current_user = auth_module.get_current_user
else:
    # Fallback if module loading fails
    def hash_password(*_args, **_kwargs):
        raise ImportError("Could not load auth.py module")

    def verify_password(*_args, **_kwargs):
        raise ImportError("Could not load auth.py module")

    def create_access_token(*_args, **_kwargs):
        raise ImportError("Could not load auth.py module")

    def decode_access_token(*_args, **_kwargs):
        raise ImportError("Could not load auth.py module")

    def get_current_user(*_args, **_kwargs):
        raise ImportError("Could not load auth.py module")

# Make these functions available when importing from the auth package
__all__ = ['hash_password', 'verify_password', 'create_access_token', 'decode_access_token', 'get_current_user']