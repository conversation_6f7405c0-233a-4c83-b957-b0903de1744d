#!/usr/bin/env python3
"""
Test runner script for the Mthunzi Policy Management System
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and print results"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        return result.returncode == 0
    
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Main test runner"""
    print("Mthunzi Backend Test Suite")
    print("=" * 60)
    
    # Change to the script directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    tests_to_run = [
        ("python -m pytest tests/test_auth.py::TestPasswordHashing::test_hash_password -v", "Single Auth Test"),
        ("python -m pytest tests/test_auth.py -k test_hash_password -v", "Password Hashing Tests"),
        ("python -m pytest tests/test_models.py::TestUserModel::test_create_user -v", "User Model Test"),
    ]
    
    results = []
    
    for command, description in tests_to_run:
        success = run_command(command, description)
        results.append((description, success))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {description}")
    
    # Overall result
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
