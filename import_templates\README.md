# Import Templates

This directory contains CSV templates for importing data into the Mthunzi Policy Management System.

## Available Templates

### 1. Policy Template (`policy_template.csv`)
**Required Columns:**
- `policyno` - Unique policy number
- `packages` - Package type (Standard, Premier, Lite)
- `bus` - Business unit flag (true/false)
- `pep` - Politically Exposed Person flag (true/false)
- `agentcode` - Agent code
- `agentname` - Agent name
- `applicantsigneddate` - Date when applicant signed the policy (YYYY-MM-DD format) **REQUIRED**
- `status` - Policy status (Active, Pending, Suspended)

**Optional Columns:**
- `schemecode` - Scheme code
- `worksitecode` - Worksite code
- `riskprofile` - Risk profile (Low, Medium, High)
- `agentsigneddate` - Date when agent signed the policy (YYYY-MM-DD format)

### 2. Policy Holder Template (`policyholder_template.csv`)
**Required Columns:**
- `policyno` - Policy number (must exist in Policy table)
- `title` - Title (Mr, Ms, Mrs, Dr)
- `initials` - Initials
- `firstname` - First name
- `surname` - Surname
- `dateofbirth` - Date of birth (YYYY-MM-DD format)
- `gender` - Gender (Male, Female)
- `maritalstatus` - Marital status (Single, Married, Divorced, Widowed)
- `capturedate` - Capture date (YYYY-MM-DD format)

**Optional Columns:**
- `idnumber` - ID number
- `idtype` - ID type (National ID, Passport, Driver's License)
- `occupation` - Occupation
- `mobilenumber` - Mobile number
- `alternativenumber` - Alternative number
- `postaladdressline1-4` - Postal address lines
- `postalcity` - Postal city
- `postalcountrycode` - Postal country code
- `town` - Town
- `emailaddress` - Email address
- `residentialaddressline1` - Residential address
- `residentialcountrycode` - Residential country code
- `residentialdistrict` - Residential district
- `residentialvillage` - Residential village
- `traditionalauthority` - Traditional authority
- `contractsigneddate` - Contract signed date
- `residentialstatus` - Residential status

### 3. Policy Mandate Template (`policymandate_template.csv`)
**Required Columns:**
- `policyno` - Policy number (must exist in Policy table)
- `firstname` - First name of mandate holder
- `surname` - Surname of mandate holder
- `mobilenumber` - Mobile phone number
- `frequency` - Payment frequency (Monthly, Quarterly, Annually)
- `premium` - Premium amount
- `modeofpayment` - Payment mode (Debit Order, Stop Order, Mobile Money)
- `FirstDeductionStartDate` - First deduction date (YYYY-MM-DD format)
- `PaypointName` - Paypoint name (bank/employer/mobile operator)

**Optional Columns:**
- `BankAccountNumber` - Bank account number
- `BankAccountType` - Account type (Savings, Current, etc.)
- `BranchName` - Bank branch name

**Field Mappings:**
- `firstname` → `debitorderFirstname`
- `surname` → `debitorderLastname`
- `mobilenumber` → `mobilephoneno`
- `BankAccountNumber` → `debitorderAccountno`
- `BankAccountType` → `debitorderTypeofaccount`
- `BranchName` → `debitorderBranch`
- `FirstDeductionStartDate` → `firstdeductiondate`
- `PaypointName` → `stoporderEmployername`, `mobileoperator`, `debitorderNameofbank`

### 4. Beneficiaries Template (`beneficiaries_template.csv`)
**Required Columns:**
- `policyno` - Policy number (must exist in Policy table)
- `title` - Title (Mr, Ms, Mrs, Dr)
- `initials` - Initials
- `firstname` - First name
- `surname` - Surname
- `gender` - Gender (Male, Female)
- `birthdate` - Birth date (YYYY-MM-DD format)
- `relationship` - Relationship to policy holder
- `membertype` - Member type (Family, Other, BFO)

**Optional Columns:**
- `idnumber` - ID number of the beneficiary

## How to Use

1. **Download Template**: Use the "Download Template" button in the import interface
2. **Fill Data**: Open the CSV file and replace sample data with your actual data
3. **Validate**: Upload the file and click "Validate File" to check for errors
4. **Import**: If validation passes, click "Process Import" to import the data

## Important Notes

- **Date Format**: Use YYYY-MM-DD format for all dates
- **Boolean Values**: Use "true" or "false" for boolean fields
- **Policy Dependencies**: Policy Holder, Policy Mandate, and Beneficiaries require existing policies
- **File Format**: Save as CSV (Comma Separated Values) format
- **Encoding**: Use UTF-8 encoding to avoid character issues

## Sample Data

Each template includes 5 rows of sample data to show the correct format:

**Policy Template Sample:**
- POL001: Standard package, Business unit, Low risk, signed 2024-01-15
- POL002: Premier package, PEP flagged, Medium risk, signed 2024-01-20
- POL003: Lite package, High risk, Pending status, signed 2024-01-25
- POL004: Standard package, Active status, signed 2024-02-01
- POL005: Premier package, Business + PEP, Suspended, signed 2024-02-05

**Policy Holder Template Sample:**
- Complete personal information for 5 policy holders
- Various titles (Mr, Ms, Dr, Mrs)
- Proper date formats and contact details

**Policy Mandate Template Sample:**
- Different payment modes (Debit Order, Stop Order, Mobile Money)
- Various frequencies (Monthly, Quarterly)
- Realistic premium amounts

**Beneficiaries Template Sample:**
- Multiple beneficiaries per policy
- Family relationships (Son, Daughter, Mother, Brother)
- Different member types (Family, Other)

**Important:** Replace all sample data with your actual data before importing!

## Error Handling

The system will validate your data and show detailed error messages if:
- Required columns are missing
- Data format is incorrect
- Referenced policies don't exist
- File format is invalid

## Support

If you encounter issues with imports, check:
1. File format is CSV or Excel
2. All required columns are present
3. Data follows the correct format
4. Referenced policies exist (for dependent imports)
