# Mthunzi Policy Management System - Frontend Application

## Project Overview

This is a modern, responsive React frontend application for the Mthunzi Policy Management System. The application features a professional green, white, and black color scheme and provides comprehensive policy management capabilities.

## ✅ Completed Features

### 1. **Authentication System**
- JWT-based login/logout functionality
- Protected routes with automatic redirection
- Token validation and refresh
- Secure session management

### 2. **Dashboard**
- Key metrics display (total policies, active policies, premiums, pending policies)
- Interactive charts using Chart.js (Bar charts, Doughnut charts)
- Recent policies table
- Responsive card-based layout

### 3. **Policy Management**
- **Policy List**: Sortable and filterable table with search functionality
- **Policy Form**: Comprehensive form for creating/editing policies
- **Policy Details**: Detailed view with related information
- Full CRUD operations with API integration

### 4. **Search Functionality**
- Multi-type search (policies, policyholders)
- Real-time search results
- Advanced filtering options
- Search tips and guidance

### 5. **Reports Module**
- Custom report generation with multiple filters
- Visual charts and statistics
- CSV export functionality
- Summary metrics display

### 6. **UI/UX Design**
- **Color Scheme**: Professional green (#28a745), white, and black theme
- **Responsive Design**: Mobile-first approach using Bootstrap 5
- **Navigation**: Intuitive sidebar and top navigation
- **Icons**: Font Awesome integration
- **Accessibility**: Proper ARIA labels and semantic HTML

## 🏗️ Architecture

### **Frontend Stack**
- **React 18**: Modern React with hooks and functional components
- **React Router 6**: Client-side routing with protected routes
- **Bootstrap 5**: Responsive UI framework
- **React Bootstrap**: Bootstrap components for React
- **Axios**: HTTP client with interceptors
- **Chart.js**: Interactive data visualization
- **Context API**: State management for authentication

### **Project Structure**
```
frontend/
├── public/
├── src/
│   ├── components/
│   │   ├── Auth/           # Login component
│   │   ├── Dashboard/      # Dashboard with metrics
│   │   ├── Layout/         # Navbar and Sidebar
│   │   ├── Policy/         # Policy CRUD components
│   │   ├── Reports/        # Reports and analytics
│   │   └── Search/         # Search functionality
│   ├── contexts/           # React Context for auth
│   ├── services/           # API service layer
│   └── App.js             # Main application component
├── package.json
└── README.md
```

## 🔌 API Integration

The frontend integrates with the existing FastAPI backend through:

- **Authentication**: `/login`, `/signup`, `/checktoken`
- **Policies**: `/policies/` (GET, POST, PUT, DELETE)
- **Policyholders**: `/policyholder/` (GET, POST, PUT)
- **Search**: `/policies/search/{term}`, `/policyholder/search/{term}`
- **Related Data**: Policy summaries, mandates, premium payers

## 🎨 Design System

### **Color Palette**
- Primary Green: `#28a745`
- Dark Green: `#1e7e34`
- Light Green: `#d4edda`
- Primary Black: `#212529`
- Light Gray: `#f8f9fa`
- White: `#ffffff`

### **Typography**
- System fonts with fallbacks
- Consistent sizing and spacing
- Proper hierarchy with headings

### **Components**
- Reusable Bootstrap components
- Custom styled cards and forms
- Consistent button styles
- Professional table layouts

## 📱 Responsive Features

- **Mobile Navigation**: Collapsible sidebar and navbar
- **Responsive Tables**: Horizontal scrolling on small screens
- **Flexible Layouts**: Grid system adapts to screen size
- **Touch-Friendly**: Appropriate button sizes and spacing

## 🔒 Security Features

- JWT token management with automatic refresh
- Protected routes with authentication checks
- Secure API communication with interceptors
- Automatic logout on token expiration

## 🚀 Getting Started

### **Prerequisites**
- Node.js (v14+)
- npm or yarn
- Backend API running on port 5000

### **Quick Setup**
1. Run the setup script:
   ```bash
   # Windows
   setup-frontend.bat
   
   # Unix/Linux/Mac
   chmod +x setup-frontend.sh
   ./setup-frontend.sh
   ```

2. Or manual setup:
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. Access the application at `http://localhost:3000`

## 📊 Key Features Demonstration

### **Dashboard**
- Real-time policy statistics
- Visual charts for data analysis
- Quick access to recent policies
- Metric cards with icons

### **Policy Management**
- Create policies with comprehensive forms
- View all policies with sorting and filtering
- Edit existing policies
- Detailed policy information display

### **Search & Reports**
- Advanced search with multiple criteria
- Generate custom reports with filters
- Export data to CSV format
- Visual representation of data

## 🔧 Configuration

### **Environment Variables**
```
REACT_APP_API_URL=http://localhost:5000
REACT_APP_APP_NAME=Mthunzi Policy Management
```

### **API Service Configuration**
- Automatic token attachment
- Request/response interceptors
- Error handling and retry logic
- Base URL configuration

## 📈 Performance Optimizations

- Code splitting with React.lazy (ready for implementation)
- Optimized bundle size
- Efficient re-rendering with proper key props
- Memoization where appropriate

## 🧪 Testing Ready

The application is structured for easy testing:
- Component-based architecture
- Separated business logic
- Mock-friendly API service
- Test utilities included

## 🔄 Future Enhancements

The application is designed to easily accommodate:
- Real-time notifications
- Advanced reporting features
- File upload capabilities
- Multi-language support
- Dark mode toggle
- Advanced user permissions

## 📝 Documentation

- Comprehensive README files
- Inline code comments
- API service documentation
- Component prop documentation

This frontend application provides a solid foundation for the Mthunzi Policy Management System with modern web technologies, professional design, and comprehensive functionality.
