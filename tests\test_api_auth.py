"""
API tests for authentication endpoints
"""

import pytest

class TestAuthenticationAPI:
    """Test authentication API endpoints"""
    
    def test_signup_success(self, client):
        """Test successful user signup"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "newpass123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "yes",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": "2024-01-01 10:00:00"
        }
        
        response = client.post("/signup", json=user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert "password" not in data  # Password should not be returned

    def test_signup_duplicate_username(self, client, test_user):
        """Test signup with duplicate username"""
        user_data = {
            "username": test_user.username,  # Use existing username
            "email": "<EMAIL>",
            "password": "newpass123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "yes",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": "2024-01-01 10:00:00"
        }
        
        response = client.post("/signup", json=user_data)
        
        assert response.status_code == 400 or response.status_code == 422

    def test_signup_duplicate_email(self, client, test_user):
        """Test signup with duplicate email"""
        user_data = {
            "username": "differentuser",
            "email": test_user.email,  # Use existing email
            "password": "newpass123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "yes",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": "2024-01-01 10:00:00"
        }
        
        response = client.post("/signup", json=user_data)
        
        assert response.status_code == 400 or response.status_code == 422

    def test_login_success(self, client, test_user):
        """Test successful login"""
        login_data = {
            "username": test_user.username,
            "password": "testpass123"
        }
        
        response = client.post("/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["username"] == test_user.username

    def test_login_invalid_username(self, client):
        """Test login with invalid username"""
        login_data = {
            "username": "nonexistentuser",
            "password": "anypassword"
        }
        
        response = client.post("/login", json=login_data)
        
        assert response.status_code == 400
        assert "Invalid username" in response.json()["detail"]

    def test_login_invalid_password(self, client, test_user):
        """Test login with invalid password"""
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword"
        }
        
        response = client.post("/login", json=login_data)
        
        assert response.status_code == 400
        assert "Invalid username" in response.json()["detail"]

    def test_login_missing_fields(self, client):
        """Test login with missing fields"""
        # Missing password
        response = client.post("/login", json={"username": "testuser"})
        assert response.status_code == 422
        
        # Missing username
        response = client.post("/login", json={"password": "testpass"})
        assert response.status_code == 422
        
        # Empty request
        response = client.post("/login", json={})
        assert response.status_code == 422

class TestTokenValidation:
    """Test token validation endpoints"""
    
    def test_check_token_valid(self, client, test_user):
        """Test token validation with valid token"""
        # First login to get token
        login_data = {
            "username": test_user.username,
            "password": "testpass123"
        }
        login_response = client.post("/login", json=login_data)
        token = login_response.json()["access_token"]
        
        # Check token
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/checktoken/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["sub"] == test_user.username

    def test_check_token_invalid(self, client):
        """Test token validation with invalid token"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/checktoken/", headers=headers)
        
        assert response.status_code == 401
        assert "Invalid authentication credentials" in response.json()["detail"]

    def test_check_token_missing(self, client):
        """Test token validation without token"""
        response = client.get("/checktoken/")
        
        assert response.status_code == 403  # Forbidden due to missing auth

    def test_check_token_malformed_header(self, client):
        """Test token validation with malformed authorization header"""
        # Missing Bearer prefix
        headers = {"Authorization": "invalid_token"}
        response = client.get("/checktoken/", headers=headers)
        
        assert response.status_code == 403

class TestAuthenticationFlow:
    """Test complete authentication flows"""
    
    def test_signup_login_flow(self, client):
        """Test complete signup -> login flow"""
        # 1. Signup
        user_data = {
            "username": "flowuser",
            "email": "<EMAIL>",
            "password": "flowpass123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "yes",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": "2024-01-01 10:00:00"
        }
        
        signup_response = client.post("/signup", json=user_data)
        assert signup_response.status_code == 200
        
        # 2. Login
        login_data = {
            "username": "flowuser",
            "password": "flowpass123"
        }
        
        login_response = client.post("/login", json=login_data)
        assert login_response.status_code == 200
        
        token = login_response.json()["access_token"]
        assert token is not None
        
        # 3. Use token to access protected endpoint
        headers = {"Authorization": f"Bearer {token}"}
        check_response = client.get("/checktoken/", headers=headers)
        assert check_response.status_code == 200
        assert check_response.json()["sub"] == "flowuser"

    def test_multiple_logins_different_tokens(self, client, test_user):
        """Test that multiple logins generate different tokens"""
        login_data = {
            "username": test_user.username,
            "password": "testpass123"
        }
        
        # First login
        response1 = client.post("/login", json=login_data)
        token1 = response1.json()["access_token"]
        
        # Second login
        response2 = client.post("/login", json=login_data)
        token2 = response2.json()["access_token"]
        
        # Tokens should be different (due to timestamp)
        assert token1 != token2
        
        # Both tokens should be valid
        headers1 = {"Authorization": f"Bearer {token1}"}
        headers2 = {"Authorization": f"Bearer {token2}"}
        
        check1 = client.get("/checktoken/", headers=headers1)
        check2 = client.get("/checktoken/", headers=headers2)
        
        assert check1.status_code == 200
        assert check2.status_code == 200
