#!/usr/bin/env python3
"""
Verification script for the Mthunzi Backend Setup
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        import auth
        print("✅ auth module imported successfully")
        
        # Test auth functions
        password = "test123"
        hashed = auth.hash_password(password)
        verified = auth.verify_password(password, hashed)
        print(f"✅ Password hashing works: {verified}")
        
        token = auth.create_access_token({"sub": "testuser"})
        decoded = auth.decode_access_token(token)
        print(f"✅ JWT tokens work: {decoded['sub'] == 'testuser'}")
        
    except Exception as e:
        print(f"❌ Auth import failed: {e}")
        return False
    
    try:
        import models
        print("✅ models module imported successfully")
    except Exception as e:
        print(f"❌ Models import failed: {e}")
        return False
    
    try:
        import services
        print("✅ services module imported successfully")
    except Exception as e:
        print(f"❌ Services import failed: {e}")
        return False
    
    try:
        import main
        print("✅ main module imported successfully")
    except Exception as e:
        print(f"❌ Main import failed: {e}")
        return False
    
    return True

def test_database():
    """Test database connection"""
    print("\nTesting database connection...")
    
    try:
        from db import SessionLocal, engine
        from models import User
        
        # Test database connection
        db = SessionLocal()
        user_count = db.query(User).count()
        db.close()
        
        print(f"✅ Database connection works. Found {user_count} users.")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_api():
    """Test API startup"""
    print("\nTesting API...")
    
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        response = client.get("/docs")
        
        if response.status_code == 200:
            print("✅ API starts and responds to /docs")
            return True
        else:
            print(f"❌ API /docs returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def main():
    """Main verification function"""
    print("Mthunzi Backend Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Database Tests", test_database),
        ("API Tests", test_api),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print(f"\n{'='*50}")
    print("VERIFICATION SUMMARY")
    print('='*50)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    # Overall result
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n🎉 All verifications passed! Backend is ready.")
        print("\nNext steps:")
        print("1. Run migrations: alembic upgrade head")
        print("2. Seed database: python seeders/seed_data.py")
        print("3. Start server: uvicorn main:app --reload")
        print("4. Run tests: python -m pytest tests/ -v")
        return 0
    else:
        print("\n💥 Some verifications failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
