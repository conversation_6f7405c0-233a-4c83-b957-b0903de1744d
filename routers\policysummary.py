from fastapi import FastAP<PERSON>, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import <PERSON>A<PERSON>2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/policysummary",
    tags=['Policysummary'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# get all policy summaries
@router.get("/", response_model=List[schema.PolicySummary])
def get_policy_summaries(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policy_summaries = db.query(models.PolicySummary).offset(skip).limit(limit).all()
    if not policy_summaries:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policy summaries found")
    return policy_summaries

# get policy summary by id
@router.get("/{policy_summary_id}", response_model=schema.PolicySummary)    
def get_policy_summary(policy_summary_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policy_summary = db.query(models.PolicySummary).filter(models.PolicySummary.id == policy_summary_id).first()
    if not policy_summary:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy summary not found")
    return policy_summary

# get policy summary by policy number
@router.get("/search/{policy_no}", response_model=List[schema.PolicySummary])
def search_policy_summary(policy_no: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policy_summaries = db.query(models.PolicySummary).filter(models.PolicySummary.policyno == policy_no).all()
    if not policy_summaries:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy summary not found")
    return policy_summaries

# create policy summary
@router.post("/", response_model=schema.PolicySummary, status_code=status.HTTP_201_CREATED)
def create_policy_summary(policy_summary: schema.PolicySummary, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    # Check if the policy number already exists
    existing_policy_summary = db.query(models.PolicySummary).filter(models.PolicySummary.policyno == policy_summary.policyno).first()
    if existing_policy_summary:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Policy summary with this policy number already exists")
    
    # Create the policy summary
    policy_summary_data = models.PolicySummary(**policy_summary.model_dump(), createdby=current_user['id'])
    db.add(policy_summary_data)
    db.commit()
    db.refresh(policy_summary_data)
    return policy_summary_data

# update policy summary
@router.put("/{policy_summary_id}", response_model=schema.PolicySummary)
def update_policy_summary(policy_summary_id: int, policy_summary: schema.PolicySummary, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policy_summary = db.query(models.PolicySummary).filter(models.PolicySummary.id == policy_summary_id).first()
    if not existing_policy_summary:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy summary not found")
    
    # Update the policy summary
    for key, value in policy_summary.model_dump().items():
        setattr(existing_policy_summary, key, value)
    
    db.commit()
    db.refresh(existing_policy_summary)
    return existing_policy_summary

# delete policy summary
@router.delete("/{policy_summary_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_policy_summary(policy_summary_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policy_summary = db.query(models.PolicySummary).filter(models.PolicySummary.id == policy_summary_id).first()
    if not existing_policy_summary:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy summary not found")
    
    db.delete(existing_policy_summary)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)