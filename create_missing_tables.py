#!/usr/bin/env python3
"""
Create missing tables script
"""

from db import engine
from sqlalchemy import text

def create_missing_tables():
    """Create missing tables manually"""
    try:
        with engine.connect() as conn:
            # PolicyHolder table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS policyholder (
                    id SERIAL PRIMARY KEY,
                    policyno VARCHAR,
                    title VA<PERSON>HA<PERSON>,
                    initials <PERSON><PERSON><PERSON><PERSON>,
                    middlename <PERSON><PERSON><PERSON><PERSON>,
                    surname <PERSON><PERSON><PERSON><PERSON>,
                    previousname <PERSON><PERSON><PERSON><PERSON>,
                    gender VARCHAR,
                    dateofbirth VARCHAR,
                    countryofbirth VARCHAR,
                    maritalstatus VARCHAR,
                    residentialstatus VARCHAR,
                    primaryid VARCHAR,
                    primaryidexpirydate VARCHAR,
                    secondaryid VARCHAR,
                    secondaryidexpirydate VARCHAR,
                    expatriate <PERSON><PERSON><PERSON><PERSON>,
                    workpermit VARCHAR,
                    workpermitexpirydate VARCHAR,
                    sourceofincome VARCHAR,
                    netmonthlyincome VARCHAR,
                    specifyincome VARCHAR,
                    proofofsourceoffunds VARCHAR,
                    specifyproofofsourceoffunds VARCHAR,
                    occupation VARCHAR,
                    industry VARCHAR,
                    employer VARCHAR,
                    other VARCHAR,
                    commpreference VARCHAR,
                    emailaddress VARCHAR,
                    phoneno VARCHAR,
                    altmobileno VARCHAR,
                    physicaladdressline1 VARCHAR,
                    physicaladdressline2 VARCHAR,
                    physicaladdressline3 VARCHAR,
                    physicaladdressline4 VARCHAR,
                    physicaladdressline5city VARCHAR,
                    physicaladdressline6country VARCHAR,
                    permanentaddressvillage VARCHAR,
                    permanentaddressta VARCHAR,
                    permanentaddressdistrict VARCHAR,
                    postaladdressline1 VARCHAR,
                    postaladdressline2 VARCHAR,
                    postaladdressline3 VARCHAR,
                    postaladdressline4 VARCHAR,
                    postaladdressline5city VARCHAR,
                    postaladdressline6country VARCHAR
                )
            """))
            
            # Beneficiaries table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS beneficiaries (
                    id SERIAL PRIMARY KEY,
                    policyid VARCHAR,
                    title VARCHAR,
                    initials VARCHAR,
                    firstname VARCHAR,
                    middlename VARCHAR,
                    surname VARCHAR,
                    gender VARCHAR,
                    birthdate VARCHAR,
                    maritalstatus VARCHAR,
                    membertype VARCHAR,
                    relationship VARCHAR,
                    benofownership VARCHAR,
                    status VARCHAR,
                    datecreated VARCHAR,
                    createdby INTEGER
                )
            """))
            
            # PremiumPayer table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS premiumpayer (
                    id SERIAL PRIMARY KEY,
                    policyno VARCHAR,
                    title VARCHAR,
                    initials VARCHAR,
                    firstname VARCHAR,
                    middlename VARCHAR,
                    surname VARCHAR,
                    gender VARCHAR,
                    dateofbirth VARCHAR,
                    countryofbirth VARCHAR,
                    maritalstatus VARCHAR,
                    residentialstatus VARCHAR,
                    primaryid VARCHAR,
                    primaryidexpirydate VARCHAR,
                    secondaryid VARCHAR,
                    secondaryidexpirydate VARCHAR,
                    expatriate VARCHAR,
                    workpermit VARCHAR,
                    workpermitexpirydate VARCHAR,
                    sourceofincome VARCHAR,
                    netmonthlyincome VARCHAR,
                    specifyincome VARCHAR,
                    proofofsourceoffunds VARCHAR,
                    specifyproofofsourceoffunds VARCHAR,
                    occupation VARCHAR,
                    industry VARCHAR,
                    employer VARCHAR,
                    other VARCHAR,
                    commpreference VARCHAR,
                    emailaddress VARCHAR,
                    phoneno VARCHAR,
                    altmobileno VARCHAR,
                    physicaladdressline1 VARCHAR,
                    physicaladdressline2 VARCHAR,
                    physicaladdressline3 VARCHAR,
                    physicaladdressline4 VARCHAR,
                    physicaladdressline5city VARCHAR,
                    physicaladdressline6country VARCHAR,
                    permanentaddressvillage VARCHAR,
                    permanentaddressta VARCHAR,
                    permanentaddressdistrict VARCHAR,
                    postaladdressline1 VARCHAR,
                    postaladdressline2 VARCHAR,
                    postaladdressline3 VARCHAR,
                    postaladdressline4 VARCHAR,
                    postaladdressline5city VARCHAR,
                    postaladdressline6country VARCHAR
                )
            """))
            
            # PolicyMandate table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS policymandate (
                    id SERIAL PRIMARY KEY,
                    policyno VARCHAR,
                    modeofpayment VARCHAR,
                    frequency VARCHAR,
                    premium VARCHAR,
                    firstdeductiondate VARCHAR,
                    debitorderFirstname VARCHAR,
                    debitorderLastname VARCHAR
                )
            """))
            
            # PolicySummary table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS policysummary (
                    id SERIAL PRIMARY KEY,
                    policyno VARCHAR,
                    premiumsdue VARCHAR,
                    premiumspaid VARCHAR,
                    totalpremiumspaid VARCHAR,
                    outstanding VARCHAR
                )
            """))
            
            # PolicyActivities table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS policyactivities (
                    id SERIAL PRIMARY KEY,
                    policyid VARCHAR,
                    policyno VARCHAR,
                    activitydesc VARCHAR,
                    remarks VARCHAR,
                    status VARCHAR,
                    datecreated VARCHAR,
                    createdby INTEGER,
                    effectivedate VARCHAR
                )
            """))
            
            conn.commit()
            print("All missing tables created successfully")
            return True
            
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

if __name__ == "__main__":
    print("Creating missing tables...")
    if create_missing_tables():
        print("✅ All tables created!")
    else:
        print("❌ Failed to create tables!")
