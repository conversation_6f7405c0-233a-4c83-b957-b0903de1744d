"""
Unit tests for service layer functions
"""

import pytest
import sys
import os

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import services
from models import User, Policy, PolicyHolder

class TestUserServices:
    """Test user-related service functions"""
    
    def test_create_user_service(self, test_db_session):
        """Test user creation service"""
        user_data = User(
            username="serviceuser",
            email="<EMAIL>",
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            viewonly="no",
            policyadmin="yes",
            servicing="yes",
            mimoadmin="no",
            sysadmin="no",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        # Test if create_user service function exists and works
        try:
            created_user = services.create_user(test_db_session, user_data)
            assert created_user.id is not None
            assert created_user.username == "serviceuser"
            assert created_user.email == "<EMAIL>"
        except AttributeError:
            # If service function doesn't exist, skip this test
            pytest.skip("create_user service function not implemented")

    def test_get_user_by_username(self, test_db_session, test_user):
        """Test getting user by username"""
        try:
            found_user = services.get_user_by_username(test_db_session, test_user.username)
            assert found_user is not None
            assert found_user.username == test_user.username
            assert found_user.email == test_user.email
        except AttributeError:
            pytest.skip("get_user_by_username service function not implemented")

    def test_get_user_by_email(self, test_db_session, test_user):
        """Test getting user by email"""
        try:
            found_user = services.get_user_by_email(test_db_session, test_user.email)
            assert found_user is not None
            assert found_user.username == test_user.username
            assert found_user.email == test_user.email
        except AttributeError:
            pytest.skip("get_user_by_email service function not implemented")

    def test_get_all_users(self, test_db_session, test_user):
        """Test getting all users"""
        try:
            users = services.get_all_users(test_db_session)
            assert isinstance(users, list)
            assert len(users) >= 1
            
            # Check if our test user is in the list
            usernames = [user.username for user in users]
            assert test_user.username in usernames
        except AttributeError:
            pytest.skip("get_all_users service function not implemented")

class TestPolicyServices:
    """Test policy-related service functions"""
    
    def test_create_policy_service(self, test_db_session):
        """Test policy creation service"""
        policy_data = Policy(
            policyno="SVC001",
            packages="Service Test Policy",
            bus="Individual",
            agentcode="AGT999",
            agentname="Service Agent",
            pep=False,
            aml=True,
            status="active",
            datecreated="2024-01-01",
            createdby=1
        )
        
        try:
            created_policy = services.create_policy(test_db_session, policy_data)
            assert created_policy.id is not None
            assert created_policy.policyno == "SVC001"
            assert created_policy.packages == "Service Test Policy"
        except AttributeError:
            pytest.skip("create_policy service function not implemented")

    def test_get_policy_by_number(self, test_db_session, test_policy):
        """Test getting policy by policy number"""
        try:
            found_policy = services.get_policy_by_number(test_db_session, test_policy.policyno)
            assert found_policy is not None
            assert found_policy.policyno == test_policy.policyno
            assert found_policy.packages == test_policy.packages
        except AttributeError:
            pytest.skip("get_policy_by_number service function not implemented")

    def test_get_all_policies(self, test_db_session, test_policy):
        """Test getting all policies"""
        try:
            policies = services.get_all_policies(test_db_session)
            assert isinstance(policies, list)
            assert len(policies) >= 1
            
            # Check if our test policy is in the list
            policy_numbers = [policy.policyno for policy in policies]
            assert test_policy.policyno in policy_numbers
        except AttributeError:
            pytest.skip("get_all_policies service function not implemented")

    def test_update_policy_status(self, test_db_session, test_policy):
        """Test updating policy status"""
        try:
            updated_policy = services.update_policy_status(
                test_db_session, 
                test_policy.id, 
                "inactive"
            )
            assert updated_policy.status == "inactive"
        except AttributeError:
            pytest.skip("update_policy_status service function not implemented")

class TestServiceValidation:
    """Test service layer validation"""
    
    def test_create_user_duplicate_username(self, test_db_session, test_user):
        """Test creating user with duplicate username"""
        duplicate_user = User(
            username=test_user.username,  # Same username
            email="<EMAIL>",
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        try:
            # Should handle duplicate username gracefully
            with pytest.raises(Exception):
                services.create_user(test_db_session, duplicate_user)
        except AttributeError:
            pytest.skip("create_user service function not implemented")

    def test_get_nonexistent_user(self, test_db_session):
        """Test getting non-existent user"""
        try:
            user = services.get_user_by_username(test_db_session, "nonexistentuser")
            assert user is None
        except AttributeError:
            pytest.skip("get_user_by_username service function not implemented")

    def test_get_nonexistent_policy(self, test_db_session):
        """Test getting non-existent policy"""
        try:
            policy = services.get_policy_by_number(test_db_session, "NONEXISTENT")
            assert policy is None
        except AttributeError:
            pytest.skip("get_policy_by_number service function not implemented")

class TestServiceErrorHandling:
    """Test service layer error handling"""
    
    def test_create_user_invalid_data(self, test_db_session):
        """Test creating user with invalid data"""
        try:
            # Try to create user with None data
            with pytest.raises(Exception):
                services.create_user(test_db_session, None)
        except AttributeError:
            pytest.skip("create_user service function not implemented")

    def test_database_session_handling(self, test_db_session):
        """Test that services handle database sessions properly"""
        # This is more of a smoke test to ensure services don't break sessions
        try:
            # Try multiple service calls to ensure session is handled properly
            users = services.get_all_users(test_db_session)
            assert isinstance(users, list)
            
            # Session should still be usable
            user_count = test_db_session.query(User).count()
            assert isinstance(user_count, int)
        except AttributeError:
            pytest.skip("Service functions not implemented")

class TestServiceIntegration:
    """Test service layer integration"""
    
    def test_user_policy_relationship(self, test_db_session, test_user):
        """Test relationship between users and policies"""
        # Create a policy with the test user as creator
        policy_data = Policy(
            policyno="REL001",
            packages="Relationship Test",
            status="active",
            datecreated="2024-01-01",
            createdby=test_user.id
        )
        
        try:
            created_policy = services.create_policy(test_db_session, policy_data)
            assert created_policy.createdby == test_user.id
            
            # Try to get policies by creator
            user_policies = services.get_policies_by_creator(test_db_session, test_user.id)
            assert len(user_policies) >= 1
            
            policy_numbers = [p.policyno for p in user_policies]
            assert "REL001" in policy_numbers
        except AttributeError:
            pytest.skip("Policy relationship service functions not implemented")
