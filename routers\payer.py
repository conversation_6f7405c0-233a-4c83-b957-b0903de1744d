from fastapi import FastAPI, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/payer",
    tags=['Payer'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# get all premium payers
@router.get("/", response_model=List[schema.PremiumPayer])
def get_premium_payers(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    premium_payers = db.query(models.PremiumPayer).offset(skip).limit(limit).all()
    if not premium_payers:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No premium payers found")
    return premium_payers

# search premium payer by policy number with pagination
@router.get("/search/{policy_no}", response_model=List[schema.PremiumPayer])
def search_premium_payer(policy_no: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    premium_payers = db.query(models.PremiumPayer).filter(func.lower(models.PremiumPayer.policyno) == func.lower(policy_no)).offset(skip).limit(limit).all()
    if not premium_payers:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No premium payers found for the specified policy number")
    return premium_payers

# get premium payer by ID
@router.get("/{payer_id}", response_model=schema.PremiumPayer)
def get_premium_payer(payer_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    premium_payer = db.query(models.PremiumPayer).filter(models.PremiumPayer.id == payer_id).first()
    if not premium_payer:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Premium payer not found")
    return premium_payer

# create premium payer
@router.post("/", response_model=schema.PremiumPayer, status_code=status.HTTP_201_CREATED)
def create_premium_payer(premium_payer: schema.PremiumPayer, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    # Check if the policy no exists
    policy_exists = db.query(models.Policy).filter(models.Policy.policyno == premium_payer.policyno).first()
    if not policy_exists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    
    # Create the premium payer
    premium_payer_data = models.PremiumPayer(**premium_payer.model_dump(), createdby=current_user['id'])
    db.add(premium_payer_data)
    db.commit()
    db.refresh(premium_payer_data)
    return premium_payer_data

# update premium payer
@router.put("/{payer_id}", response_model=schema.PremiumPayer)
def update_premium_payer(payer_id: int, premium_payer: schema.PremiumPayer, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_premium_payer = db.query(models.PremiumPayer).filter(models.PremiumPayer.id == payer_id).first()
    if not existing_premium_payer:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Premium payer not found")
    
    # Update the premium payer
    for key, value in premium_payer.model_dump().items():
        setattr(existing_premium_payer, key, value)
    
    db.commit()
    db.refresh(existing_premium_payer)
    return existing_premium_payer

# delete premium payer
@router.delete("/{payer_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_premium_payer(payer_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_premium_payer = db.query(models.PremiumPayer).filter(models.PremiumPayer.id == payer_id).first()
    if not existing_premium_payer:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Premium payer not found")
    
    db.delete(existing_premium_payer)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)