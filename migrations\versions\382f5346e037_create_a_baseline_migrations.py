"""Create a baseline migrations

Revision ID: 382f5346e037
Revises: 
Create Date: 2025-07-06 23:46:10.246792

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '382f5346e037'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('viewonly', sa.String(), nullable=True),
    sa.Column('policyadmin', sa.String(), nullable=True),
    sa.Column('servicing', sa.String(), nullable=True),
    sa.Column('mimoadmin', sa.String(), nullable=True),
    sa.Column('sysadmin', sa.String(), nullable=True),
    sa.Column('createdby', sa.String(), nullable=True),
    sa.Column('datecreated', sa.String(), nullable=True),
    sa.Column('hashed_password', sa.String(), nullable=True),
    sa.Column('password', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_depart_id', 'users', ['id'], unique=False)
    op.create_index('ix_depart_username', 'users', ['username'], unique=False)
    op.create_index(op.f('ix_users_createdby'), 'users', ['createdby'], unique=False)
    op.create_index(op.f('ix_users_datecreated'), 'users', ['datecreated'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_mimoadmin'), 'users', ['mimoadmin'], unique=False)
    op.create_index(op.f('ix_users_policyadmin'), 'users', ['policyadmin'], unique=False)
    op.create_index(op.f('ix_users_servicing'), 'users', ['servicing'], unique=False)
    op.create_index(op.f('ix_users_status'), 'users', ['status'], unique=False)
    op.create_index(op.f('ix_users_sysadmin'), 'users', ['sysadmin'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_viewonly'), 'users', ['viewonly'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_viewonly'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_sysadmin'), table_name='users')
    op.drop_index(op.f('ix_users_status'), table_name='users')
    op.drop_index(op.f('ix_users_servicing'), table_name='users')
    op.drop_index(op.f('ix_users_policyadmin'), table_name='users')
    op.drop_index(op.f('ix_users_mimoadmin'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_datecreated'), table_name='users')
    op.drop_index(op.f('ix_users_createdby'), table_name='users')
    op.drop_index('ix_depart_username', table_name='users')
    op.drop_index('ix_depart_id', table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
