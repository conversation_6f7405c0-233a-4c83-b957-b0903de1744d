import React from 'react';
import { <PERSON>, <PERSON>, Card, Form, Button } from 'react-bootstrap';
import { FaUser, FaCreditCard, FaFileContract, FaUsers, FaPlus, FaTrash } from 'react-icons/fa';

// Step 2: Policy Holder Form
export const PolicyHolderStep = ({ policyHolderData, handlePolicyHolderChange }) => (
  <Row>
    <Col lg={10}>
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">
            <FaUser className="me-2" />
            Policy Holder Information
          </h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Title *</Form.Label>
                <Form.Select
                  name="title"
                  value={policyHolderData.title}
                  onChange={handlePolicyHolderChange}
                  required
                >
                  <option value="">Select title</option>
                  <option value="Mr">Mr</option>
                  <option value="Mrs">Mrs</option>
                  <option value="Ms">Ms</option>
                  <option value="Dr">Dr</option>
                  <option value="Prof">Prof</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Initials *</Form.Label>
                <Form.Control
                  type="text"
                  name="initials"
                  value={policyHolderData.initials}
                  onChange={handlePolicyHolderChange}
                  required
                  placeholder="e.g., J.D."
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Middle Name</Form.Label>
                <Form.Control
                  type="text"
                  name="middlename"
                  value={policyHolderData.middlename}
                  onChange={handlePolicyHolderChange}
                  placeholder="Middle name"
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Surname *</Form.Label>
                <Form.Control
                  type="text"
                  name="surname"
                  value={policyHolderData.surname}
                  onChange={handlePolicyHolderChange}
                  required
                  placeholder="Last name"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Previous Name</Form.Label>
                <Form.Control
                  type="text"
                  name="previousname"
                  value={policyHolderData.previousname}
                  onChange={handlePolicyHolderChange}
                  placeholder="Previous name"
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Gender *</Form.Label>
                <Form.Select
                  name="gender"
                  value={policyHolderData.gender}
                  onChange={handlePolicyHolderChange}
                  required
                >
                  <option value="">Select gender</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Date of Birth *</Form.Label>
                <Form.Control
                  type="date"
                  name="dateofbirth"
                  value={policyHolderData.dateofbirth}
                  onChange={handlePolicyHolderChange}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Country of Birth *</Form.Label>
                <Form.Control
                  type="text"
                  name="countryofbirth"
                  value={policyHolderData.countryofbirth}
                  onChange={handlePolicyHolderChange}
                  required
                  placeholder="Country of birth"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Marital Status *</Form.Label>
                <Form.Select
                  name="maritalstatus"
                  value={policyHolderData.maritalstatus}
                  onChange={handlePolicyHolderChange}
                  required
                >
                  <option value="">Select status</option>
                  <option value="Single">Single</option>
                  <option value="Married">Married</option>
                  <option value="Divorced">Divorced</option>
                  <option value="Widowed">Widowed</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Residential Status *</Form.Label>
                <Form.Select
                  name="residentialstatus"
                  value={policyHolderData.residentialstatus}
                  onChange={handlePolicyHolderChange}
                  required
                >
                  <option value="">Select status</option>
                  <option value="Citizen">Citizen</option>
                  <option value="Permanent Resident">Permanent Resident</option>
                  <option value="Temporary Resident">Temporary Resident</option>
                  <option value="Non-Resident">Non-Resident</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Primary ID *</Form.Label>
                <Form.Control
                  type="text"
                  name="primaryid"
                  value={policyHolderData.primaryid}
                  onChange={handlePolicyHolderChange}
                  required
                  placeholder="ID number"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Primary ID Expiry Date *</Form.Label>
                <Form.Control
                  type="date"
                  name="primaryidexpirydate"
                  value={policyHolderData.primaryidexpirydate}
                  onChange={handlePolicyHolderChange}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Secondary ID</Form.Label>
                <Form.Control
                  type="text"
                  name="secondaryid"
                  value={policyHolderData.secondaryid}
                  onChange={handlePolicyHolderChange}
                  placeholder="Secondary ID number"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Secondary ID Expiry Date</Form.Label>
                <Form.Control
                  type="date"
                  name="secondaryidexpirydate"
                  value={policyHolderData.secondaryidexpirydate}
                  onChange={handlePolicyHolderChange}
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Expatriate</Form.Label>
                <Form.Select
                  name="expatriate"
                  value={policyHolderData.expatriate}
                  onChange={handlePolicyHolderChange}
                >
                  <option value="">Select</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Work Permit</Form.Label>
                <Form.Control
                  type="text"
                  name="workpermit"
                  value={policyHolderData.workpermit}
                  onChange={handlePolicyHolderChange}
                  placeholder="Work permit number"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Work Permit Expiry Date</Form.Label>
                <Form.Control
                  type="date"
                  name="workpermitexpirydate"
                  value={policyHolderData.workpermitexpirydate}
                  onChange={handlePolicyHolderChange}
                />
              </Form.Group>
            </Col>
          </Row>

          <h6 className="mt-4 mb-3">Income & Employment Information</h6>
          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Source of Income</Form.Label>
                <Form.Select
                  name="sourceofincome"
                  value={policyHolderData.sourceofincome}
                  onChange={handlePolicyHolderChange}
                >
                  <option value="">Select source</option>
                  <option value="Employment">Employment</option>
                  <option value="Business">Business</option>
                  <option value="Investment">Investment</option>
                  <option value="Pension">Pension</option>
                  <option value="Other">Other</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Net Monthly Income</Form.Label>
                <Form.Control
                  type="number"
                  name="netmonthlyincome"
                  value={policyHolderData.netmonthlyincome}
                  onChange={handlePolicyHolderChange}
                  placeholder="0.00"
                  step="0.01"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Specify Income</Form.Label>
                <Form.Control
                  type="text"
                  name="specifyincome"
                  value={policyHolderData.specifyincome}
                  onChange={handlePolicyHolderChange}
                  placeholder="Income details"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Proof of Source of Funds</Form.Label>
                <Form.Select
                  name="proofofsourceoffunds"
                  value={policyHolderData.proofofsourceoffunds}
                  onChange={handlePolicyHolderChange}
                >
                  <option value="">Select proof type</option>
                  <option value="Payslip">Payslip</option>
                  <option value="Bank Statement">Bank Statement</option>
                  <option value="Tax Certificate">Tax Certificate</option>
                  <option value="Business Registration">Business Registration</option>
                  <option value="Other">Other</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Specify Proof of Source of Funds</Form.Label>
                <Form.Control
                  type="text"
                  name="specifyproofofsourceoffunds"
                  value={policyHolderData.specifyproofofsourceoffunds}
                  onChange={handlePolicyHolderChange}
                  placeholder="Specify proof details"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Occupation</Form.Label>
                <Form.Control
                  type="text"
                  name="occupation"
                  value={policyHolderData.occupation}
                  onChange={handlePolicyHolderChange}
                  placeholder="Occupation"
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Industry</Form.Label>
                <Form.Control
                  type="text"
                  name="industry"
                  value={policyHolderData.industry}
                  onChange={handlePolicyHolderChange}
                  placeholder="Industry"
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Employer</Form.Label>
                <Form.Control
                  type="text"
                  name="employer"
                  value={policyHolderData.employer}
                  onChange={handlePolicyHolderChange}
                  placeholder="Employer name"
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <Form.Label>Other</Form.Label>
                <Form.Control
                  type="text"
                  name="other"
                  value={policyHolderData.other}
                  onChange={handlePolicyHolderChange}
                  placeholder="Other information"
                />
              </Form.Group>
            </Col>
          </Row>

          <h6 className="mt-4 mb-3">Contact Information</h6>
          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Communication Preference</Form.Label>
                <Form.Select
                  name="commpreference"
                  value={policyHolderData.commpreference}
                  onChange={handlePolicyHolderChange}
                >
                  <option value="">Select preference</option>
                  <option value="Email">Email</option>
                  <option value="Phone">Phone</option>
                  <option value="SMS">SMS</option>
                  <option value="Post">Post</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Email Address</Form.Label>
                <Form.Control
                  type="email"
                  name="emailaddress"
                  value={policyHolderData.emailaddress}
                  onChange={handlePolicyHolderChange}
                  placeholder="Email address"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Phone Number</Form.Label>
                <Form.Control
                  type="tel"
                  name="phoneno"
                  value={policyHolderData.phoneno}
                  onChange={handlePolicyHolderChange}
                  placeholder="Phone number"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Alternative Mobile Number</Form.Label>
                <Form.Control
                  type="tel"
                  name="altmobileno"
                  value={policyHolderData.altmobileno}
                  onChange={handlePolicyHolderChange}
                  placeholder="Alternative mobile number"
                />
              </Form.Group>
            </Col>
          </Row>

          <h6 className="mt-4 mb-3">Address Information</h6>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Physical Address Line 1</Form.Label>
                <Form.Control
                  type="text"
                  name="physicaladdressline1"
                  value={policyHolderData.physicaladdressline1}
                  onChange={handlePolicyHolderChange}
                  placeholder="Street address"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Physical Address Line 2</Form.Label>
                <Form.Control
                  type="text"
                  name="physicaladdressline2"
                  value={policyHolderData.physicaladdressline2}
                  onChange={handlePolicyHolderChange}
                  placeholder="Apartment, suite, etc."
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Address Line 3</Form.Label>
                <Form.Control
                  type="text"
                  name="physicaladdressline3"
                  value={policyHolderData.physicaladdressline3}
                  onChange={handlePolicyHolderChange}
                  placeholder="Additional address info"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Address Line 4</Form.Label>
                <Form.Control
                  type="text"
                  name="physicaladdressline4"
                  value={policyHolderData.physicaladdressline4}
                  onChange={handlePolicyHolderChange}
                  placeholder="Additional address info"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>City</Form.Label>
                <Form.Control
                  type="text"
                  name="physicaladdressline5city"
                  value={policyHolderData.physicaladdressline5city}
                  onChange={handlePolicyHolderChange}
                  placeholder="City"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Country</Form.Label>
                <Form.Control
                  type="text"
                  name="physicaladdressline6country"
                  value={policyHolderData.physicaladdressline6country}
                  onChange={handlePolicyHolderChange}
                  placeholder="Country"
                />
              </Form.Group>
            </Col>
          </Row>

          <h6 className="mt-4 mb-3">Permanent Address</h6>
          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Village</Form.Label>
                <Form.Control
                  type="text"
                  name="permanentaddressvillage"
                  value={policyHolderData.permanentaddressvillage}
                  onChange={handlePolicyHolderChange}
                  placeholder="Village"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Traditional Authority</Form.Label>
                <Form.Control
                  type="text"
                  name="permanentaddressta"
                  value={policyHolderData.permanentaddressta}
                  onChange={handlePolicyHolderChange}
                  placeholder="Traditional Authority"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>District</Form.Label>
                <Form.Control
                  type="text"
                  name="permanentaddressdistrict"
                  value={policyHolderData.permanentaddressdistrict}
                  onChange={handlePolicyHolderChange}
                  placeholder="District"
                />
              </Form.Group>
            </Col>
          </Row>

          <h6 className="mt-4 mb-3">Postal Address</h6>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Postal Address Line 1</Form.Label>
                <Form.Control
                  type="text"
                  name="postaladdressline1"
                  value={policyHolderData.postaladdressline1}
                  onChange={handlePolicyHolderChange}
                  placeholder="P.O. Box or postal address"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Postal Address Line 2</Form.Label>
                <Form.Control
                  type="text"
                  name="postaladdressline2"
                  value={policyHolderData.postaladdressline2}
                  onChange={handlePolicyHolderChange}
                  placeholder="Additional postal info"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Postal Address Line 3</Form.Label>
                <Form.Control
                  type="text"
                  name="postaladdressline3"
                  value={policyHolderData.postaladdressline3}
                  onChange={handlePolicyHolderChange}
                  placeholder="Additional postal info"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Postal Address Line 4</Form.Label>
                <Form.Control
                  type="text"
                  name="postaladdressline4"
                  value={policyHolderData.postaladdressline4}
                  onChange={handlePolicyHolderChange}
                  placeholder="Additional postal info"
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Postal City</Form.Label>
                <Form.Control
                  type="text"
                  name="postaladdressline5city"
                  value={policyHolderData.postaladdressline5city}
                  onChange={handlePolicyHolderChange}
                  placeholder="Postal city"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Postal Country</Form.Label>
                <Form.Control
                  type="text"
                  name="postaladdressline6country"
                  value={policyHolderData.postaladdressline6country}
                  onChange={handlePolicyHolderChange}
                  placeholder="Postal country"
                />
              </Form.Group>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </Col>
  </Row>
);

// Step 3: Premium Payer Form
export const PremiumPayerStep = ({ premiumPayerData, handlePremiumPayerChange, sameAsPolicyHolder, setSameAsPolicyHolder }) => (
  <Row>
    <Col lg={10}>
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">
            <FaCreditCard className="me-2" />
            Premium Payer Information
          </h5>
        </Card.Header>
        <Card.Body>
          <Form.Check
            type="checkbox"
            label="Same as Policy Holder"
            checked={sameAsPolicyHolder}
            onChange={(e) => setSameAsPolicyHolder(e.target.checked)}
            className="mb-4"
          />

          {!sameAsPolicyHolder && (
            <>
              <h6 className="mb-3">Personal Information</h6>
              <Row>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Title *</Form.Label>
                    <Form.Select
                      name="title"
                      value={premiumPayerData.title}
                      onChange={handlePremiumPayerChange}
                      required
                    >
                      <option value="">Select title</option>
                      <option value="Mr">Mr</option>
                      <option value="Mrs">Mrs</option>
                      <option value="Ms">Ms</option>
                      <option value="Dr">Dr</option>
                      <option value="Prof">Prof</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Initials *</Form.Label>
                    <Form.Control
                      type="text"
                      name="initials"
                      value={premiumPayerData.initials}
                      onChange={handlePremiumPayerChange}
                      required
                      placeholder="e.g., J.D."
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Middle Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="middlename"
                      value={premiumPayerData.middlename}
                      onChange={handlePremiumPayerChange}
                      placeholder="Middle name"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Surname *</Form.Label>
                    <Form.Control
                      type="text"
                      name="surname"
                      value={premiumPayerData.surname}
                      onChange={handlePremiumPayerChange}
                      required
                      placeholder="Last name"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Previous Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="previousname"
                      value={premiumPayerData.previousname}
                      onChange={handlePremiumPayerChange}
                      placeholder="Previous name"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Gender *</Form.Label>
                    <Form.Select
                      name="gender"
                      value={premiumPayerData.gender}
                      onChange={handlePremiumPayerChange}
                      required
                    >
                      <option value="">Select gender</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Date of Birth *</Form.Label>
                    <Form.Control
                      type="date"
                      name="dateofbirth"
                      value={premiumPayerData.dateofbirth}
                      onChange={handlePremiumPayerChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Country of Birth *</Form.Label>
                    <Form.Control
                      type="text"
                      name="countryofbirth"
                      value={premiumPayerData.countryofbirth}
                      onChange={handlePremiumPayerChange}
                      required
                      placeholder="Country of birth"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Marital Status *</Form.Label>
                    <Form.Select
                      name="maritalstatus"
                      value={premiumPayerData.maritalstatus}
                      onChange={handlePremiumPayerChange}
                      required
                    >
                      <option value="">Select status</option>
                      <option value="Single">Single</option>
                      <option value="Married">Married</option>
                      <option value="Divorced">Divorced</option>
                      <option value="Widowed">Widowed</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Residential Status *</Form.Label>
                    <Form.Select
                      name="residentialstatus"
                      value={premiumPayerData.residentialstatus}
                      onChange={handlePremiumPayerChange}
                      required
                    >
                      <option value="">Select status</option>
                      <option value="Citizen">Citizen</option>
                      <option value="Permanent Resident">Permanent Resident</option>
                      <option value="Temporary Resident">Temporary Resident</option>
                      <option value="Non-Resident">Non-Resident</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Primary ID *</Form.Label>
                    <Form.Control
                      type="text"
                      name="primaryid"
                      value={premiumPayerData.primaryid}
                      onChange={handlePremiumPayerChange}
                      required
                      placeholder="ID number"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Primary ID Expiry Date *</Form.Label>
                    <Form.Control
                      type="date"
                      name="primaryidexpirydate"
                      value={premiumPayerData.primaryidexpirydate}
                      onChange={handlePremiumPayerChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Secondary ID</Form.Label>
                    <Form.Control
                      type="text"
                      name="secondaryid"
                      value={premiumPayerData.secondaryid}
                      onChange={handlePremiumPayerChange}
                      placeholder="Secondary ID number"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Secondary ID Expiry Date</Form.Label>
                    <Form.Control
                      type="date"
                      name="secondaryidexpirydate"
                      value={premiumPayerData.secondaryidexpirydate}
                      onChange={handlePremiumPayerChange}
                    />
                  </Form.Group>
                </Col>
              </Row>

              <h6 className="mt-4 mb-3">Contact Information</h6>
              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Email Address</Form.Label>
                    <Form.Control
                      type="email"
                      name="emailaddress"
                      value={premiumPayerData.emailaddress}
                      onChange={handlePremiumPayerChange}
                      placeholder="Email address"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Phone Number</Form.Label>
                    <Form.Control
                      type="tel"
                      name="phoneno"
                      value={premiumPayerData.phoneno}
                      onChange={handlePremiumPayerChange}
                      placeholder="Phone number"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Alternative Mobile Number</Form.Label>
                    <Form.Control
                      type="tel"
                      name="altmobileno"
                      value={premiumPayerData.altmobileno}
                      onChange={handlePremiumPayerChange}
                      placeholder="Alternative mobile number"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Expatriate Status</Form.Label>
                    <Form.Select
                      name="expatriate"
                      value={premiumPayerData.expatriate}
                      onChange={handlePremiumPayerChange}
                    >
                      <option value="">Select status</option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Work Permit</Form.Label>
                    <Form.Control
                      type="text"
                      name="workpermit"
                      value={premiumPayerData.workpermit}
                      onChange={handlePremiumPayerChange}
                      placeholder="Work permit number"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Work Permit Expiry Date</Form.Label>
                    <Form.Control
                      type="date"
                      name="workpermitexpirydate"
                      value={premiumPayerData.workpermitexpirydate}
                      onChange={handlePremiumPayerChange}
                    />
                  </Form.Group>
                </Col>
              </Row>

              <h6 className="mt-4 mb-3">Income & Employment Information</h6>
              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Source of Income</Form.Label>
                    <Form.Select
                      name="sourceofincome"
                      value={premiumPayerData.sourceofincome}
                      onChange={handlePremiumPayerChange}
                    >
                      <option value="">Select source</option>
                      <option value="Employment">Employment</option>
                      <option value="Business">Business</option>
                      <option value="Investment">Investment</option>
                      <option value="Pension">Pension</option>
                      <option value="Other">Other</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Net Monthly Income</Form.Label>
                    <Form.Control
                      type="number"
                      name="netmonthlyincome"
                      value={premiumPayerData.netmonthlyincome}
                      onChange={handlePremiumPayerChange}
                      placeholder="0.00"
                      step="0.01"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Specify Income Details</Form.Label>
                    <Form.Control
                      type="text"
                      name="specifyincome"
                      value={premiumPayerData.specifyincome}
                      onChange={handlePremiumPayerChange}
                      placeholder="Income details"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Proof of Source of Funds</Form.Label>
                    <Form.Select
                      name="proofofsourceoffunds"
                      value={premiumPayerData.proofofsourceoffunds}
                      onChange={handlePremiumPayerChange}
                    >
                      <option value="">Select proof type</option>
                      <option value="Payslip">Payslip</option>
                      <option value="Bank Statement">Bank Statement</option>
                      <option value="Tax Return">Tax Return</option>
                      <option value="Business Registration">Business Registration</option>
                      <option value="Other">Other</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Specify Proof Details</Form.Label>
                    <Form.Control
                      type="text"
                      name="specifyproofofsourceoffunds"
                      value={premiumPayerData.specifyproofofsourceoffunds}
                      onChange={handlePremiumPayerChange}
                      placeholder="Proof details"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Occupation</Form.Label>
                    <Form.Control
                      type="text"
                      name="occupation"
                      value={premiumPayerData.occupation}
                      onChange={handlePremiumPayerChange}
                      placeholder="Occupation"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Industry</Form.Label>
                    <Form.Control
                      type="text"
                      name="industry"
                      value={premiumPayerData.industry}
                      onChange={handlePremiumPayerChange}
                      placeholder="Industry"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Employer</Form.Label>
                    <Form.Control
                      type="text"
                      name="employer"
                      value={premiumPayerData.employer}
                      onChange={handlePremiumPayerChange}
                      placeholder="Employer name"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Other Details</Form.Label>
                    <Form.Control
                      type="text"
                      name="other"
                      value={premiumPayerData.other}
                      onChange={handlePremiumPayerChange}
                      placeholder="Other information"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Communication Preference</Form.Label>
                    <Form.Select
                      name="commpreference"
                      value={premiumPayerData.commpreference}
                      onChange={handlePremiumPayerChange}
                    >
                      <option value="">Select preference</option>
                      <option value="Email">Email</option>
                      <option value="Phone">Phone</option>
                      <option value="SMS">SMS</option>
                      <option value="Post">Post</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <h6 className="mt-4 mb-3">Address Information</h6>
              <h6 className="mb-3">Physical Address</h6>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Address Line 1</Form.Label>
                    <Form.Control
                      type="text"
                      name="physicaladdressline1"
                      value={premiumPayerData.physicaladdressline1}
                      onChange={handlePremiumPayerChange}
                      placeholder="Street address"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Address Line 2</Form.Label>
                    <Form.Control
                      type="text"
                      name="physicaladdressline2"
                      value={premiumPayerData.physicaladdressline2}
                      onChange={handlePremiumPayerChange}
                      placeholder="Apartment, suite, etc."
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Address Line 3</Form.Label>
                    <Form.Control
                      type="text"
                      name="physicaladdressline3"
                      value={premiumPayerData.physicaladdressline3}
                      onChange={handlePremiumPayerChange}
                      placeholder="Additional address info"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Address Line 4</Form.Label>
                    <Form.Control
                      type="text"
                      name="physicaladdressline4"
                      value={premiumPayerData.physicaladdressline4}
                      onChange={handlePremiumPayerChange}
                      placeholder="Additional address info"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>City</Form.Label>
                    <Form.Control
                      type="text"
                      name="physicaladdressline5city"
                      value={premiumPayerData.physicaladdressline5city}
                      onChange={handlePremiumPayerChange}
                      placeholder="City"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Country</Form.Label>
                    <Form.Control
                      type="text"
                      name="physicaladdressline6country"
                      value={premiumPayerData.physicaladdressline6country}
                      onChange={handlePremiumPayerChange}
                      placeholder="Country"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <h6 className="mt-4 mb-3">Permanent Address</h6>
              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Village</Form.Label>
                    <Form.Control
                      type="text"
                      name="permanentaddressvillage"
                      value={premiumPayerData.permanentaddressvillage}
                      onChange={handlePremiumPayerChange}
                      placeholder="Village"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Traditional Authority</Form.Label>
                    <Form.Control
                      type="text"
                      name="permanentaddressta"
                      value={premiumPayerData.permanentaddressta}
                      onChange={handlePremiumPayerChange}
                      placeholder="Traditional Authority"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>District</Form.Label>
                    <Form.Control
                      type="text"
                      name="permanentaddressdistrict"
                      value={premiumPayerData.permanentaddressdistrict}
                      onChange={handlePremiumPayerChange}
                      placeholder="District"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <h6 className="mt-4 mb-3">Postal Address</h6>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Postal Address Line 1</Form.Label>
                    <Form.Control
                      type="text"
                      name="postaladdressline1"
                      value={premiumPayerData.postaladdressline1}
                      onChange={handlePremiumPayerChange}
                      placeholder="P.O. Box or postal address"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Postal Address Line 2</Form.Label>
                    <Form.Control
                      type="text"
                      name="postaladdressline2"
                      value={premiumPayerData.postaladdressline2}
                      onChange={handlePremiumPayerChange}
                      placeholder="Additional postal info"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Postal Address Line 3</Form.Label>
                    <Form.Control
                      type="text"
                      name="postaladdressline3"
                      value={premiumPayerData.postaladdressline3}
                      onChange={handlePremiumPayerChange}
                      placeholder="Additional postal info"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Postal Address Line 4</Form.Label>
                    <Form.Control
                      type="text"
                      name="postaladdressline4"
                      value={premiumPayerData.postaladdressline4}
                      onChange={handlePremiumPayerChange}
                      placeholder="Additional postal info"
                    />
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Postal City</Form.Label>
                    <Form.Control
                      type="text"
                      name="postaladdressline5city"
                      value={premiumPayerData.postaladdressline5city}
                      onChange={handlePremiumPayerChange}
                      placeholder="Postal city"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Postal Country</Form.Label>
                    <Form.Control
                      type="text"
                      name="postaladdressline6country"
                      value={premiumPayerData.postaladdressline6country}
                      onChange={handlePremiumPayerChange}
                      placeholder="Postal country"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </>
          )}

          {sameAsPolicyHolder && (
            <div className="text-center py-4">
              <p className="text-muted">Premium payer information will be copied from policy holder details.</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Col>
  </Row>
);

// Step 4: Policy Mandate Form
export const PolicyMandateStep = ({ policyMandateData, handlePolicyMandateChange }) => (
  <Row>
    <Col lg={10}>
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">
            <FaFileContract className="me-2" />
            Payment Mandate Information
          </h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Mode of Payment *</Form.Label>
                <Form.Select
                  name="modeofpayment"
                  value={policyMandateData.modeofpayment}
                  onChange={handlePolicyMandateChange}
                  required
                >
                  <option value="">Select payment mode</option>
                  <option value="Debit Order">Debit Order</option>
                  <option value="Stop Order">Stop Order</option>
                  <option value="Mobile Money">Mobile Money</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Frequency *</Form.Label>
                <Form.Select
                  name="frequency"
                  value={policyMandateData.frequency}
                  onChange={handlePolicyMandateChange}
                  required
                >
                  <option value="">Select frequency</option>
                  <option value="Monthly">Monthly</option>
                  <option value="Quarterly">Quarterly</option>
                  <option value="Semi-Annual">Semi-Annual</option>
                  <option value="Annual">Annual</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Premium Amount *</Form.Label>
                <Form.Control
                  type="number"
                  name="premium"
                  value={policyMandateData.premium}
                  onChange={handlePolicyMandateChange}
                  required
                  placeholder="0.00"
                  step="0.01"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>First Deduction Date *</Form.Label>
                <Form.Control
                  type="date"
                  name="firstdeductiondate"
                  value={policyMandateData.firstdeductiondate}
                  onChange={handlePolicyMandateChange}
                  required
                />
              </Form.Group>
            </Col>
          </Row>

          {policyMandateData.modeofpayment === 'Debit Order' && (
            <>
              <h6 className="mt-4 mb-3">Debit Order Details</h6>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Account Holder First Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="debitorderFirstname"
                      value={policyMandateData.debitorderFirstname}
                      onChange={handlePolicyMandateChange}
                      placeholder="First name"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Account Holder Last Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="debitorderLastname"
                      value={policyMandateData.debitorderLastname}
                      onChange={handlePolicyMandateChange}
                      placeholder="Last name"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Bank Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="debitorderNameofbank"
                      value={policyMandateData.debitorderNameofbank}
                      onChange={handlePolicyMandateChange}
                      placeholder="Bank name"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Branch</Form.Label>
                    <Form.Control
                      type="text"
                      name="debitorderBranch"
                      value={policyMandateData.debitorderBranch}
                      onChange={handlePolicyMandateChange}
                      placeholder="Branch name"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Account Number</Form.Label>
                    <Form.Control
                      type="text"
                      name="debitorderAccountno"
                      value={policyMandateData.debitorderAccountno}
                      onChange={handlePolicyMandateChange}
                      placeholder="Account number"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Account Type</Form.Label>
                    <Form.Select
                      name="debitorderTypeofaccount"
                      value={policyMandateData.debitorderTypeofaccount}
                      onChange={handlePolicyMandateChange}
                    >
                      <option value="">Select account type</option>
                      <option value="Savings">Savings</option>
                      <option value="Current">Current</option>
                      <option value="Transmission">Transmission</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Salary Funding Date</Form.Label>
                    <Form.Control
                      type="date"
                      name="debitorderSalaryfundingdate"
                      value={policyMandateData.debitorderSalaryfundingdate}
                      onChange={handlePolicyMandateChange}
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Premium on Application</Form.Label>
                    <Form.Control
                      type="number"
                      name="debitorderPremiumonapplication"
                      value={policyMandateData.debitorderPremiumonapplication}
                      onChange={handlePolicyMandateChange}
                      placeholder="0.00"
                      step="0.01"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </>
          )}

          {policyMandateData.modeofpayment === 'Stop Order' && (
            <>
              <h6 className="mt-4 mb-3">Stop Order Details</h6>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Employee ID</Form.Label>
                    <Form.Control
                      type="text"
                      name="stoporderEmployeeid"
                      value={policyMandateData.stoporderEmployeeid}
                      onChange={handlePolicyMandateChange}
                      placeholder="Employee ID"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Employee Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="stoporderEmployeename"
                      value={policyMandateData.stoporderEmployeename}
                      onChange={handlePolicyMandateChange}
                      placeholder="Employee name"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Employer Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="stoporderEmployername"
                      value={policyMandateData.stoporderEmployername}
                      onChange={handlePolicyMandateChange}
                      placeholder="Employer name"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Employer Address</Form.Label>
                    <Form.Control
                      type="text"
                      name="stoporderEmployeraddress"
                      value={policyMandateData.stoporderEmployeraddress}
                      onChange={handlePolicyMandateChange}
                      placeholder="Employer address"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </>
          )}

          {policyMandateData.modeofpayment === 'Mobile Money' && (
            <>
              <h6 className="mt-4 mb-3">Mobile Money Details</h6>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Mobile Operator</Form.Label>
                    <Form.Select
                      name="mobileoperator"
                      value={policyMandateData.mobileoperator}
                      onChange={handlePolicyMandateChange}
                    >
                      <option value="">Select operator</option>
                      <option value="MTN">MTN</option>
                      <option value="Vodacom">Vodacom</option>
                      <option value="Cell C">Cell C</option>
                      <option value="Telkom">Telkom</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Mobile Phone Number</Form.Label>
                    <Form.Control
                      type="tel"
                      name="mobilephoneno"
                      value={policyMandateData.mobilephoneno}
                      onChange={handlePolicyMandateChange}
                      placeholder="Mobile number"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </>
          )}
        </Card.Body>
      </Card>
    </Col>
  </Row>
);

// Step 5: Beneficiaries Form
export const BeneficiariesStep = ({ beneficiariesData, handleBeneficiaryChange, addBeneficiary, removeBeneficiary }) => (
  <Row>
    <Col lg={12}>
      <Card className="mb-4">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">
            <FaUsers className="me-2" />
            Beneficiaries Information
          </h5>
          <Button variant="outline-primary" size="sm" onClick={addBeneficiary}>
            <FaPlus className="me-2" />
            Add Beneficiary
          </Button>
        </Card.Header>
        <Card.Body>
          {beneficiariesData.map((beneficiary, index) => (
            <div key={index} className="border rounded p-3 mb-3">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <h6 className="mb-0">Beneficiary {index + 1}</h6>
                {beneficiariesData.length > 1 && (
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => removeBeneficiary(index)}
                  >
                    <FaTrash />
                  </Button>
                )}
              </div>

              <Row>
                <Col md={2}>
                  <Form.Group className="mb-3">
                    <Form.Label>Title *</Form.Label>
                    <Form.Select
                      name="title"
                      value={beneficiary.title}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      required
                    >
                      <option value="">Select</option>
                      <option value="Mr">Mr</option>
                      <option value="Mrs">Mrs</option>
                      <option value="Ms">Ms</option>
                      <option value="Master">Master</option>
                      <option value="Miss">Miss</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={2}>
                  <Form.Group className="mb-3">
                    <Form.Label>Initials</Form.Label>
                    <Form.Control
                      type="text"
                      name="initials"
                      value={beneficiary.initials}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      placeholder="Initials"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>First Name *</Form.Label>
                    <Form.Control
                      type="text"
                      name="firstname"
                      value={beneficiary.firstname}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      required
                      placeholder="First name"
                    />
                  </Form.Group>
                </Col>
                <Col md={2}>
                  <Form.Group className="mb-3">
                    <Form.Label>Middle Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="middlename"
                      value={beneficiary.middlename}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      placeholder="Middle"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Surname *</Form.Label>
                    <Form.Control
                      type="text"
                      name="surname"
                      value={beneficiary.surname}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      required
                      placeholder="Surname"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Gender</Form.Label>
                    <Form.Select
                      name="gender"
                      value={beneficiary.gender}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                    >
                      <option value="">Select</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Birth Date</Form.Label>
                    <Form.Control
                      type="date"
                      name="birthdate"
                      value={beneficiary.birthdate}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Marital Status</Form.Label>
                    <Form.Select
                      name="maritalstatus"
                      value={beneficiary.maritalstatus}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                    >
                      <option value="">Select</option>
                      <option value="Single">Single</option>
                      <option value="Married">Married</option>
                      <option value="Divorced">Divorced</option>
                      <option value="Widowed">Widowed</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Member Type</Form.Label>
                    <Form.Select
                      name="membertype"
                      value={beneficiary.membertype}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                    >
                      <option value="">Select</option>
                      <option value="Family">Family</option>
                      <option value="Other">Other</option>
                      <option value="BFO">BFO</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Relationship *</Form.Label>
                    <Form.Select
                      name="relationship"
                      value={beneficiary.relationship}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      required
                    >
                      <option value="">Select</option>
                      <option value="Spouse">Spouse</option>
                      <option value="Child">Child</option>
                      <option value="Parent">Parent</option>
                      <option value="Sibling">Sibling</option>
                      <option value="Other">Other</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Ownership %</Form.Label>
                    <Form.Control
                      type="number"
                      name="benofownership"
                      value={beneficiary.benofownership}
                      onChange={(e) => handleBeneficiaryChange(index, e)}
                      placeholder="0"
                      min="0"
                      max="100"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Control
                      type="text"
                      name="status"
                      value="ACTIVE"
                      readOnly
                      className="bg-light"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </div>
          ))}
        </Card.Body>
      </Card>
    </Col>
  </Row>
);
