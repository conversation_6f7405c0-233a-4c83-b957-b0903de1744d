import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Alert, Spinner } from 'react-bootstrap';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { <PERSON>, Doughnut } from 'react-chartjs-2';
import apiService from '../../services/apiService';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const Dashboard = () => {
  const [metrics, setMetrics] = useState({
    totalPolicies: 0,
    activePolicies: 0,
    totalPremiums: 0,
    pendingPolicies: 0
  });
  const [recentPolicies, setRecentPolicies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch policies data
      const policiesResponse = await apiService.getPolicies(0, 100);
      const policies = policiesResponse.data;
      
      // Fetch policy summaries for premium data
      const summariesResponse = await apiService.getPolicySummaries(0, 100);
      const summaries = summariesResponse.data;
      
      // Calculate metrics
      const totalPolicies = policies.length;
      const activePolicies = policies.filter(p => p.status === 'Active').length;
      const pendingPolicies = policies.filter(p => p.status === 'Pending').length;
      
      // Calculate total premiums from summaries
      const totalPremiums = summaries.reduce((sum, summary) => {
        return sum + (parseFloat(summary.totalpremiumspaid) || 0);
      }, 0);
      
      setMetrics({
        totalPolicies,
        activePolicies,
        totalPremiums,
        pendingPolicies
      });
      
      // Get recent policies (last 5)
      setRecentPolicies(policies.slice(0, 5));
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const barChartData = {
    labels: ['Active', 'Pending', 'Expired', 'Cancelled'],
    datasets: [
      {
        label: 'Policies',
        data: [metrics.activePolicies, metrics.pendingPolicies, 15, 8],
        backgroundColor: [
          '#28a745',
          '#ffc107',
          '#dc3545',
          '#6c757d'
        ],
        borderColor: [
          '#1e7e34',
          '#e0a800',
          '#c82333',
          '#5a6268'
        ],
        borderWidth: 1
      }
    ]
  };

  const doughnutChartData = {
    labels: ['Life Insurance', 'Health Insurance', 'Property Insurance', 'Auto Insurance'],
    datasets: [
      {
        data: [45, 25, 20, 10],
        backgroundColor: [
          '#28a745',
          '#17a2b8',
          '#ffc107',
          '#dc3545'
        ],
        borderColor: '#fff',
        borderWidth: 2
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Policy Statistics'
      }
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" variant="primary" />
      </div>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0 text-gray-800">Dashboard</h1>
          <p className="text-muted">Welcome to your policy management dashboard</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Metrics Cards */}
      <Row className="mb-4">
        <Col xl={3} md={6} className="mb-4">
          <Card className="metric-card h-100">
            <Card.Body>
              <Row>
                <Col>
                  <div className="text-xs font-weight-bold text-uppercase mb-1">
                    Total Policies
                  </div>
                  <div className="h5 mb-0 font-weight-bold">
                    {metrics.totalPolicies.toLocaleString()}
                  </div>
                </Col>
                <Col xs="auto">
                  <i className="fas fa-file-contract fa-2x"></i>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>

        <Col xl={3} md={6} className="mb-4">
          <Card className="metric-card h-100">
            <Card.Body>
              <Row>
                <Col>
                  <div className="text-xs font-weight-bold text-uppercase mb-1">
                    Active Policies
                  </div>
                  <div className="h5 mb-0 font-weight-bold">
                    {metrics.activePolicies.toLocaleString()}
                  </div>
                </Col>
                <Col xs="auto">
                  <i className="fas fa-check-circle fa-2x"></i>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>

        <Col xl={3} md={6} className="mb-4">
          <Card className="metric-card h-100">
            <Card.Body>
              <Row>
                <Col>
                  <div className="text-xs font-weight-bold text-uppercase mb-1">
                    Total Premiums
                  </div>
                  <div className="h5 mb-0 font-weight-bold">
                    ${metrics.totalPremiums.toLocaleString()}
                  </div>
                </Col>
                <Col xs="auto">
                  <i className="fas fa-dollar-sign fa-2x"></i>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>

        <Col xl={3} md={6} className="mb-4">
          <Card className="metric-card h-100">
            <Card.Body>
              <Row>
                <Col>
                  <div className="text-xs font-weight-bold text-uppercase mb-1">
                    Pending Policies
                  </div>
                  <div className="h5 mb-0 font-weight-bold">
                    {metrics.pendingPolicies.toLocaleString()}
                  </div>
                </Col>
                <Col xs="auto">
                  <i className="fas fa-clock fa-2x"></i>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row className="mb-4">
        <Col lg={8}>
          <Card className="shadow mb-4">
            <Card.Header className="py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Policy Status Overview</h6>
            </Card.Header>
            <Card.Body>
              <Bar data={barChartData} options={chartOptions} />
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="shadow mb-4">
            <Card.Header className="py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Policy Types</h6>
            </Card.Header>
            <Card.Body>
              <Doughnut data={doughnutChartData} />
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recent Policies Table */}
      <Row>
        <Col>
          <Card className="shadow mb-4">
            <Card.Header className="py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Recent Policies</h6>
              <a href="/policies" className="btn btn-primary btn-sm">
                View All
              </a>
            </Card.Header>
            <Card.Body>
              <Table responsive striped>
                <thead>
                  <tr>
                    <th>Policy No</th>
                    <th>Agent</th>
                    <th>Package</th>
                    <th>Status</th>
                    <th>Date Created</th>
                  </tr>
                </thead>
                <tbody>
                  {recentPolicies.map((policy) => (
                    <tr key={policy.id}>
                      <td>{policy.policyno}</td>
                      <td>{policy.agentname}</td>
                      <td>{policy.packages}</td>
                      <td>
                        <span className={`badge ${
                          policy.status === 'Active' ? 'bg-success' : 
                          policy.status === 'Pending' ? 'bg-warning' : 'bg-secondary'
                        }`}>
                          {policy.status}
                        </span>
                      </td>
                      <td>{policy.datecreated}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Dashboard;
