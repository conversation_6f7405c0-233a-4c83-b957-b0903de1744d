import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Table, ProgressBar, Badge } from 'react-bootstrap';
import { FaUpload, FaFileExcel, FaCheck, FaExclamationTriangle, FaDownload } from 'react-icons/fa';
import apiService from '../../services/apiService';

const ImportData = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [importType, setImportType] = useState('');
  const [validationResult, setValidationResult] = useState(null);
  const [importResult, setImportResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: Select, 2: Validate, 3: Import, 4: Results
  const [validationError, setValidationError] = useState(null);
  const [importError, setImportError] = useState(null);
  const [downloadingTemplate, setDownloadingTemplate] = useState(false);

  const importTypes = [
    { value: 'policy', label: 'Policy Details', description: 'Import policy information including policy numbers, packages, and agent details' },
    { value: 'policyholder', label: 'Policy Holder', description: 'Import policy holder personal information and contact details' },
    { value: 'policymandate', label: 'Payment Mandate', description: 'Import payment mandate and banking information' },
    { value: 'beneficiaries', label: 'Beneficiaries', description: 'Import beneficiary information for policies' }
  ];

  const requiredColumns = {
    policy: ['policyno', 'packages', 'bus', 'pep', 'agentcode', 'agentname', 'applicantsigneddate', 'status'],
    policyholder: ['policyno', 'title', 'initials', 'firstname', 'surname', 'dateofbirth', 'gender', 'maritalstatus', 'capturedate'],
    policymandate: ['policyno', 'firstname', 'surname', 'mobilenumber', 'frequency', 'premium', 'modeofpayment', 'FirstDeductionStartDate', 'PaypointName'],
    beneficiaries: ['policyno', 'title', 'initials', 'firstname', 'surname', 'gender', 'birthdate', 'relationship', 'membertype']
  };

  // Optional columns that can be included but are not required
  const optionalColumns = {
    policy: ['schemecode', 'worksitecode', 'riskprofile'],
    policyholder: ['idnumber', 'idtype', 'occupation', 'mobilenumber', 'alternativenumber', 'postaladdressline1', 'postaladdressline2', 'postaladdressline3', 'postaladdressline4', 'postalcity', 'postalcountrycode', 'town', 'emailaddress', 'residentialaddressline1', 'residentialcountrycode', 'residentialdistrict', 'residentialvillage', 'traditionalauthority', 'contractsigneddate', 'residentialstatus'],
    policymandate: ['BankAccountNumber', 'BankAccountType', 'BranchName'],
    beneficiaries: ['idnumber']
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      const validTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
      if (validTypes.includes(file.type) || file.name.endsWith('.csv') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        setSelectedFile(file);
        setValidationResult(null);
        setImportResult(null);
        setStep(1);
      } else {
        alert('Please select a valid CSV or Excel file');
      }
    }
  };

  const handleValidateFile = async () => {
    if (!selectedFile || !importType) {
      setValidationError({
        title: 'Missing Information',
        message: 'Please select a file and import type before validating.',
        type: 'warning'
      });
      return;
    }

    console.log('=== FRONTEND VALIDATION DEBUG ===');
    console.log('Selected File:', selectedFile);
    console.log('File name:', selectedFile.name);
    console.log('File size:', selectedFile.size);
    console.log('File type:', selectedFile.type);
    console.log('Import type:', importType);

    setLoading(true);
    setValidationError(null);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('import_type', importType);

      console.log('Sending request to /imports/validate...');
      const response = await apiService.post('/imports/validate', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Validation response:', response.data);
      setValidationResult(response.data);

      // Check for missing columns
      if (response.data.missing_columns && response.data.missing_columns.length > 0) {
        setValidationError({
          type: 'warning',
          title: 'Missing Required Columns',
          message: `The following required columns are missing: ${response.data.missing_columns.join(', ')}`,
          technical: {
            missing_columns: response.data.missing_columns,
            found_columns: response.data.columns,
            required_columns: response.data.required_columns
          }
        });
      }

      // Check for data validation errors
      else if (response.data.validation_errors && response.data.validation_errors.length > 0) {
        const errorCount = response.data.validation_errors.length;
        const firstError = response.data.validation_errors[0];

        setValidationError({
          type: 'danger',
          title: `Data Validation Issues (${errorCount} rows affected)`,
          message: `Found ${errorCount} rows with data issues. First error: Row ${firstError.row} - ${firstError.errors.join(', ')}`,
          technical: {
            validation_errors: response.data.validation_errors,
            total_errors: errorCount,
            troubleshooting: [
              'Check boolean fields use "true" or "false"',
              'Verify packages field uses: Standard, Premier, or Lite',
              'Ensure all required fields have values',
              'Check data format matches expected types'
            ]
          }
        });
      }

      // If no errors, proceed to step 2
      else if (response.data.status === 'success') {
        setStep(2);
      }
    } catch (error) {
      console.error('=== VALIDATION ERROR DEBUG ===');
      console.error('Full error object:', error);
      console.error('Error response:', error.response);
      console.error('Error response data:', error.response?.data);
      console.error('Error response status:', error.response?.status);
      console.error('Error message:', error.message);

      const errorDetail = error.response?.data?.detail;
      const errorStatus = error.response?.status || 'Unknown';

      // Handle structured error response from backend
      if (typeof errorDetail === 'object' && errorDetail !== null) {
        setValidationError({
          title: `${errorDetail.error_category || 'Validation Failed'} (${errorStatus})`,
          message: errorDetail.error_message || 'Error validating file',
          type: 'danger',
          technical: {
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url,
            method: error.config?.method?.toUpperCase(),
            filename: selectedFile?.name,
            fileSize: selectedFile?.size,
            importType: importType,
            errorType: errorDetail.error_type,
            troubleshooting: errorDetail.troubleshooting
          }
        });
      } else {
        // Handle simple string error response
        setValidationError({
          title: `Validation Failed (${errorStatus})`,
          message: errorDetail || 'Error validating file',
          type: 'danger',
          technical: {
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url,
            method: error.config?.method?.toUpperCase(),
            filename: selectedFile?.name,
            fileSize: selectedFile?.size,
            importType: importType
          }
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleProcessImport = async () => {
    if (!selectedFile || !importType) {
      setImportError({
        title: 'Missing Information',
        message: 'Please select a file and import type before processing.',
        type: 'warning'
      });
      return;
    }

    setLoading(true);
    setStep(3);
    setImportError(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('import_type', importType);

      const response = await apiService.post('/imports/process', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setImportResult(response.data);
      setStep(4);
    } catch (error) {
      console.error('Import error:', error);

      const errorDetail = error.response?.data?.detail;
      const errorStatus = error.response?.status || 'Unknown';

      // Handle structured error response from backend
      if (typeof errorDetail === 'object' && errorDetail !== null) {
        setImportError({
          title: `${errorDetail.error_category || 'Import Processing Failed'} (${errorStatus})`,
          message: errorDetail.error_message || 'Error processing import',
          type: 'danger',
          technical: {
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url,
            method: error.config?.method?.toUpperCase(),
            filename: selectedFile?.name,
            fileSize: selectedFile?.size,
            importType: importType,
            errorType: errorDetail.error_type,
            troubleshooting: errorDetail.troubleshooting
          }
        });
      } else {
        // Handle simple string error response
        setImportError({
          title: `Import Processing Failed (${errorStatus})`,
          message: errorDetail || 'Error processing import',
          type: 'danger',
          technical: {
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url,
            method: error.config?.method?.toUpperCase(),
            filename: selectedFile?.name,
            fileSize: selectedFile?.size,
            importType: importType
          }
        });
      }
      setStep(2);
    } finally {
      setLoading(false);
    }
  };

  const resetImport = () => {
    setSelectedFile(null);
    setImportType('');
    setValidationResult(null);
    setImportResult(null);
    setValidationError(null);
    setImportError(null);
    setStep(1);
  };

  const downloadTemplate = async (type) => {
    try {
      setDownloadingTemplate(true);
      console.log('🔄 Attempting to download template for:', type);
      console.log('🔑 Token available:', !!localStorage.getItem('token'));
      console.log('🌐 API Base URL:', apiService.api.defaults.baseURL);

      const response = await apiService.get(`/imports/template/${type}`, {
        responseType: 'blob'
      });

      console.log('✅ Template download successful:', {
        status: response.status,
        size: response.data.size,
        type: response.data.type
      });

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${type}_import_template.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('❌ Error downloading template:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });

      console.warn('🔄 Falling back to client-side template generation');

      // Fallback to client-side generation
      const columns = requiredColumns[type];
      if (!columns) return;

      const csvContent = columns.join(',') + '\n';
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type}_template.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } finally {
      setDownloadingTemplate(false);
    }
  };

  return (
    <Container fluid className="p-4">
      <Row>
        <Col>
          <h2 className="mb-4">
            <FaUpload className="me-2" />
            Data Import
          </h2>
        </Col>
      </Row>

      {/* Step 1: File Selection */}
      <Row className="mb-4">
        <Col lg={8}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Step 1: Select Import Type and File</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Import Type *</Form.Label>
                    <Form.Select
                      value={importType}
                      onChange={(e) => setImportType(e.target.value)}
                      disabled={loading}
                    >
                      <option value="">Select import type...</option>
                      {importTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </Form.Select>
                    {importType && (
                      <Form.Text className="text-muted">
                        {importTypes.find(t => t.value === importType)?.description}
                      </Form.Text>
                    )}
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select File *</Form.Label>
                    <Form.Control
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileSelect}
                      disabled={loading}
                    />
                    <Form.Text className="text-muted">
                      Supported formats: CSV, Excel (.xlsx, .xls)
                    </Form.Text>
                  </Form.Group>
                </Col>
              </Row>

              {selectedFile && (
                <Alert variant="info" className="mb-3">
                  <FaFileExcel className="me-2" />
                  Selected file: <strong>{selectedFile.name}</strong> ({(selectedFile.size / 1024).toFixed(2)} KB)
                </Alert>
              )}

              {importType && (
                <div className="mb-3">
                  <h6>Required Columns for {importTypes.find(t => t.value === importType)?.label}:</h6>
                  <div className="d-flex flex-wrap gap-1 mb-2">
                    {requiredColumns[importType]?.map((col) => (
                      <Badge key={col} bg="primary">{col}</Badge>
                    ))}
                  </div>

                  {optionalColumns[importType]?.length > 0 && (
                    <>
                      <h6 className="text-muted">Optional Columns:</h6>
                      <div className="d-flex flex-wrap gap-1 mb-2">
                        {optionalColumns[importType]?.map((col) => (
                          <Badge key={col} bg="secondary">{col}</Badge>
                        ))}
                      </div>
                    </>
                  )}

                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="mt-2"
                    onClick={() => downloadTemplate(importType)}
                  >
                    <FaDownload className="me-1" />
                    Download Template
                  </Button>
                </div>
              )}

              <div className="d-flex gap-2">
                <Button
                  variant="primary"
                  onClick={handleValidateFile}
                  disabled={!selectedFile || !importType || loading}
                >
                  {loading ? 'Validating...' : 'Validate File'}
                </Button>
                <Button variant="outline-secondary" onClick={resetImport}>
                  Reset
                </Button>
              </div>

              {validationError && (
                <Alert variant={validationError.type} className="mt-3">
                  <div className="d-flex align-items-start">
                    <FaExclamationTriangle className="me-2 mt-1" />
                    <div className="flex-grow-1">
                      <h6 className="mb-1">{validationError.title}</h6>
                      <p className="mb-2">{validationError.message}</p>

                      {validationError.technical?.troubleshooting && (
                        <div className="mt-2">
                          <h6 className="text-info mb-2">💡 Troubleshooting Tips:</h6>
                          <ul className="mb-2">
                            {validationError.technical.troubleshooting.map((tip, index) => (
                              <li key={index}><small>{tip}</small></li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {validationError.technical && (
                        <details className="mt-2">
                          <summary className="text-muted" style={{cursor: 'pointer'}}>
                            <small>Technical Details</small>
                          </summary>
                          <div className="mt-2 p-2 bg-light rounded">
                            <small>
                              <strong>File:</strong> {validationError.technical.filename}<br/>
                              <strong>Size:</strong> {validationError.technical.fileSize} bytes<br/>
                              <strong>Import Type:</strong> {validationError.technical.importType}<br/>
                              {validationError.technical.errorType && (
                                <><strong>Error Type:</strong> {validationError.technical.errorType}<br/></>
                              )}
                              <strong>HTTP Status:</strong> {validationError.technical.status} {validationError.technical.statusText}<br/>
                              <strong>Request:</strong> {validationError.technical.method} {validationError.technical.url}
                            </small>
                          </div>
                        </details>
                      )}
                    </div>
                  </div>
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card>
            <Card.Header>
              <h6 className="mb-0">Import Progress</h6>
            </Card.Header>
            <Card.Body>
              <div className="d-flex align-items-center mb-2">
                <Badge bg={step >= 1 ? 'success' : 'secondary'} className="me-2">1</Badge>
                <span className={step >= 1 ? 'text-success' : 'text-muted'}>Select File & Type</span>
                {step >= 1 && <FaCheck className="ms-auto text-success" />}
              </div>
              <div className="d-flex align-items-center mb-2">
                <Badge bg={step >= 2 ? 'success' : 'secondary'} className="me-2">2</Badge>
                <span className={step >= 2 ? 'text-success' : 'text-muted'}>Validate Data</span>
                {step >= 2 && <FaCheck className="ms-auto text-success" />}
              </div>
              <div className="d-flex align-items-center mb-2">
                <Badge bg={step >= 3 ? 'success' : 'secondary'} className="me-2">3</Badge>
                <span className={step >= 3 ? 'text-success' : 'text-muted'}>Process Import</span>
                {step >= 3 && <FaCheck className="ms-auto text-success" />}
              </div>
              <div className="d-flex align-items-center">
                <Badge bg={step >= 4 ? 'success' : 'secondary'} className="me-2">4</Badge>
                <span className={step >= 4 ? 'text-success' : 'text-muted'}>View Results</span>
                {step >= 4 && <FaCheck className="ms-auto text-success" />}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Step 2: Validation Results */}
      {validationResult && step >= 2 && (
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0">Step 2: Validation Results</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Alert variant={validationResult.missing_columns.length > 0 ? 'warning' : 'success'}>
                      <div className="d-flex align-items-center">
                        {validationResult.missing_columns.length > 0 ? (
                          <FaExclamationTriangle className="me-2" />
                        ) : (
                          <FaCheck className="me-2" />
                        )}
                        <div>
                          <strong>File Status: </strong>
                          {validationResult.missing_columns.length > 0 ? 'Validation Issues' : 'Valid'}
                        </div>
                      </div>
                      <div className="mt-2">
                        <strong>Total Rows:</strong> {validationResult.total_rows}<br />
                        <strong>Columns Found:</strong> {validationResult.columns.length}
                      </div>
                      {validationResult.missing_columns.length > 0 && (
                        <div className="mt-2">
                          <strong>Missing Required Columns:</strong>
                          <div className="d-flex flex-wrap gap-1 mt-1">
                            {validationResult.missing_columns.map((col) => (
                              <Badge key={col} bg="danger">{col}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </Alert>
                  </Col>
                </Row>

                {validationResult.preview_data && validationResult.preview_data.length > 0 && (
                  <div className="mb-3">
                    <h6>Data Preview (First 5 rows):</h6>
                    <div className="table-responsive">
                      <Table striped bordered hover size="sm">
                        <thead>
                          <tr>
                            {validationResult.columns.map((col) => (
                              <th key={col}>{col}</th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {validationResult.preview_data.map((row, index) => (
                            <tr key={index}>
                              {validationResult.columns.map((col) => (
                                <td key={col}>{row[col] || ''}</td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </div>
                  </div>
                )}

                <div className="d-flex gap-2">
                  <Button
                    variant="success"
                    onClick={handleProcessImport}
                    disabled={validationResult.missing_columns.length > 0 || loading}
                  >
                    {loading ? 'Processing...' : 'Process Import'}
                  </Button>
                  <Button variant="outline-secondary" onClick={() => setStep(1)}>
                    Back to File Selection
                  </Button>
                </div>

                {importError && (
                  <Alert variant={importError.type} className="mt-3">
                    <div className="d-flex align-items-start">
                      <FaExclamationTriangle className="me-2 mt-1" />
                      <div className="flex-grow-1">
                        <h6 className="mb-1">{importError.title}</h6>
                        <p className="mb-2">{importError.message}</p>

                        {importError.technical?.troubleshooting && (
                          <div className="mt-2">
                            <h6 className="text-info mb-2">💡 Troubleshooting Tips:</h6>
                            <ul className="mb-2">
                              {importError.technical.troubleshooting.map((tip, index) => (
                                <li key={index}><small>{tip}</small></li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {importError.technical && (
                          <details className="mt-2">
                            <summary className="text-muted" style={{cursor: 'pointer'}}>
                              <small>Technical Details</small>
                            </summary>
                            <div className="mt-2 p-2 bg-light rounded">
                              <small>
                                <strong>File:</strong> {importError.technical.filename}<br/>
                                <strong>Size:</strong> {importError.technical.fileSize} bytes<br/>
                                <strong>Import Type:</strong> {importError.technical.importType}<br/>
                                {importError.technical.errorType && (
                                  <><strong>Error Type:</strong> {importError.technical.errorType}<br/></>
                                )}
                                <strong>HTTP Status:</strong> {importError.technical.status} {importError.technical.statusText}<br/>
                                <strong>Request:</strong> {importError.technical.method} {importError.technical.url}
                              </small>
                            </div>
                          </details>
                        )}
                      </div>
                    </div>
                  </Alert>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Step 3: Processing */}
      {step === 3 && loading && (
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0">Step 3: Processing Import</h5>
              </Card.Header>
              <Card.Body>
                <div className="text-center">
                  <ProgressBar animated now={100} className="mb-3" />
                  <p>Processing your import file... Please wait.</p>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Step 4: Import Results */}
      {importResult && step >= 4 && (
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0">Step 4: Import Results</h5>
              </Card.Header>
              <Card.Body>
                <Alert variant={importResult.error_count > 0 ? 'warning' : 'success'}>
                  <div className="d-flex align-items-center">
                    {importResult.error_count > 0 ? (
                      <FaExclamationTriangle className="me-2" />
                    ) : (
                      <FaCheck className="me-2" />
                    )}
                    <div>
                      <strong>Import Completed</strong>
                    </div>
                  </div>
                  <div className="mt-2">
                    <Row>
                      <Col md={3}>
                        <strong>Total Rows:</strong> {importResult.total_rows}
                      </Col>
                      <Col md={3}>
                        <strong>Successful:</strong> <span className="text-success">{importResult.success_count}</span>
                      </Col>
                      <Col md={3}>
                        <strong>Errors:</strong> <span className="text-danger">{importResult.error_count}</span>
                      </Col>
                      <Col md={3}>
                        <strong>Success Rate:</strong> {((importResult.success_count / importResult.total_rows) * 100).toFixed(1)}%
                      </Col>
                    </Row>
                  </div>
                </Alert>

                {importResult.errors && importResult.errors.length > 0 && (
                  <div className="mt-3">
                    <h6>Import Errors (showing first 10):</h6>
                    <div className="table-responsive">
                      <Table striped bordered hover size="sm">
                        <thead>
                          <tr>
                            <th>Row</th>
                            <th>Category</th>
                            <th>Error Details</th>
                            <th>Problematic Data</th>
                          </tr>
                        </thead>
                        <tbody>
                          {importResult.errors.map((error, index) => (
                            <tr key={index}>
                              <td>
                                <Badge bg="secondary">{error.row}</Badge>
                              </td>
                              <td>
                                <Badge bg={
                                  error.error_category === 'Duplicate Record' ? 'warning' :
                                  error.error_category === 'Missing Required Field' ? 'danger' :
                                  error.error_category === 'Invalid Data' ? 'info' :
                                  error.error_category === 'Missing Reference' ? 'dark' : 'secondary'
                                }>
                                  {error.error_category || 'Error'}
                                </Badge>
                              </td>
                              <td className="text-danger">
                                <div>
                                  <strong>{error.error_message || error.error}</strong>
                                  {error.error_type && (
                                    <div><small className="text-muted">Type: {error.error_type}</small></div>
                                  )}
                                </div>
                              </td>
                              <td>
                                <details>
                                  <summary className="text-muted" style={{cursor: 'pointer'}}>
                                    <small>View data</small>
                                  </summary>
                                  <pre className="mt-1" style={{fontSize: '0.75rem', maxHeight: '100px', overflow: 'auto'}}>
                                    {JSON.stringify(error.data, null, 2)}
                                  </pre>
                                </details>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </div>
                    {importResult.error_count > 10 && (
                      <Alert variant="info" className="mt-2">
                        <small>
                          Showing first 10 errors out of {importResult.error_count} total errors.
                          Please fix these issues and try importing again.
                        </small>
                      </Alert>
                    )}
                  </div>
                )}

                <div className="d-flex gap-2 mt-3">
                  <Button variant="primary" onClick={resetImport}>
                    Import Another File
                  </Button>
                  <Button variant="outline-secondary" onClick={() => window.location.href = '/policies'}>
                    View Policies
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}
    </Container>
  );
};

export default ImportData;
