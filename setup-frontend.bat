@echo off
echo ========================================
echo Mthunzi Policy Management Frontend Setup
echo ========================================
echo.

echo Checking if Node.js is installed...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js is installed.
echo.

echo Navigating to frontend directory...
cd frontend

echo Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Creating .env file from example...
if not exist .env (
    copy .env.example .env
    echo .env file created successfully
) else (
    echo .env file already exists
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To start the development server:
echo   cd frontend
echo   npm start
echo.
echo The application will be available at:
echo   http://localhost:3000
echo.
echo Make sure your backend API is running on:
echo   http://localhost:5000
echo.
pause
