<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Template Download</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Template Download</h1>
        
        <div class="form-group">
            <label for="token">JWT Token (get from browser localStorage):</label>
            <input type="text" id="token" placeholder="Paste your JWT token here">
            <small>Open browser dev tools → Application → Local Storage → token</small>
        </div>
        
        <div class="form-group">
            <label for="importType">Import Type:</label>
            <select id="importType">
                <option value="policy">Policy</option>
                <option value="policyholder">Policy Holder</option>
                <option value="policymandate">Policy Mandate</option>
                <option value="beneficiaries">Beneficiaries</option>
            </select>
        </div>
        
        <button onclick="downloadTemplate()">Download Template</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function downloadTemplate() {
            const token = document.getElementById('token').value;
            const importType = document.getElementById('importType').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">Please enter a JWT token</div>';
                return;
            }
            
            try {
                console.log('Downloading template for:', importType);
                
                const response = await fetch(`http://localhost:8000/imports/template/${importType}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', `${importType}_import_template.csv`);
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    window.URL.revokeObjectURL(url);
                    
                    // Show success message
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Template Downloaded Successfully!</h3>
                            <p><strong>File:</strong> ${importType}_import_template.csv</p>
                            <p><strong>Size:</strong> ${blob.size} bytes</p>
                            <p>Check your Downloads folder for the file.</p>
                        </div>
                    `;
                } else {
                    const errorData = await response.json();
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Download Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${errorData.detail || 'Unknown error'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
