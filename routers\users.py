from fastapi import <PERSON><PERSON><PERSON>, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import OAuth2Pass<PERSON><PERSON>ear<PERSON>, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from sqlalchemy import func, or_
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/users",
    tags=['Users'],
)

# Helper function to get current user
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# Helper function to check if user is admin
def check_admin_permission(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    # Get full user details from database
    user = db.query(models.User).filter(models.User.username == current_user['sub']).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found")

    # Check if user has admin permissions (string comparison)
    if (user.sysadmin or "").lower() != "yes" and (user.mimoadmin or "").lower() != "yes":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin permissions required")

    return user

# get all users with pagination (admin only)
@router.get("/", response_model=List[schema.UserBase])
def get_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), admin_user: models.User = Depends(check_admin_permission)):
    users = db.query(models.User).offset(skip).limit(limit).all()
    if not users:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No users found")
    return users

# Enhanced search users by multiple criteria
@router.get("/search", response_model=List[schema.UserBase])
def search_users(
    q: Optional[str] = None,
    username: Optional[str] = None,
    email: Optional[str] = None,
    status: Optional[str] = None,
    role: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    admin_user: models.User = Depends(check_admin_permission)
):
    """
    Search users by multiple criteria:
    - q: General search term (searches username and email)
    - username: Specific username search
    - email: Specific email search
    - status: User status filter
    - role: Role filter (sysadmin, mimoadmin, policyadmin, servicing)
    """
    query = db.query(models.User)

    if q:
        # General search across username and email
        search_term = f"%{q.lower()}%"
        query = query.filter(
            or_(
                func.lower(models.User.username).like(search_term),
                func.lower(models.User.email).like(search_term)
            )
        )

    if username:
        query = query.filter(func.lower(models.User.username).like(f"%{username.lower()}%"))

    if email:
        query = query.filter(func.lower(models.User.email).like(f"%{email.lower()}%"))

    if status:
        query = query.filter(func.lower(models.User.status) == status.lower())

    if role:
        role_lower = role.lower()
        if role_lower == "sysadmin":
            query = query.filter(func.lower(models.User.sysadmin) == "yes")
        elif role_lower == "mimoadmin":
            query = query.filter(func.lower(models.User.mimoadmin) == "yes")
        elif role_lower == "policyadmin":
            query = query.filter(func.lower(models.User.policyadmin) == "yes")
        elif role_lower == "servicing":
            query = query.filter(func.lower(models.User.servicing) == "yes")

    users = query.offset(skip).limit(limit).all()
    return users

# get user by ID (admin only)
@router.get("/{user_id}", response_model=schema.UserBase)
def get_user(user_id: int, db: Session = Depends(get_db), admin_user: models.User = Depends(check_admin_permission)):
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return user

# create user (admin only)
@router.post("/", response_model=schema.UserBase, status_code=status.HTTP_201_CREATED)
def create_user(user: schema.UserCreate, db: Session = Depends(get_db), admin_user: models.User = Depends(check_admin_permission)):
    # Check if the username already exists
    existing_user = db.query(models.User).filter(models.User.username == user.username).first()
    if existing_user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Username already exists")

    # Check if email already exists
    existing_email = db.query(models.User).filter(models.User.email == user.email).first()
    if existing_email:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already exists")

    # Hash the password
    hashed_password = hash_password(user.password)

    # Create the user with current timestamp
    user_data = models.User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        password=hashed_password,  # For backward compatibility
        status=user.status,
        viewonly=user.viewonly,
        policyadmin=user.policyadmin,
        servicing=user.servicing,
        mimoadmin=user.mimoadmin,
        sysadmin=user.sysadmin,
        createdby=admin_user.username,
        datecreated=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    )

    db.add(user_data)
    db.commit()
    db.refresh(user_data)
    return user_data

# update user (admin only)
@router.put("/{user_id}", response_model=schema.UserBase)
def update_user(user_id: int, user_update: schema.UserUpdate, db: Session = Depends(get_db), admin_user: models.User = Depends(check_admin_permission)):
    existing_user = db.query(models.User).filter(models.User.id == user_id).first()
    if not existing_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Check if username is being changed and if it already exists
    if user_update.username != existing_user.username:
        username_exists = db.query(models.User).filter(
            models.User.username == user_update.username,
            models.User.id != user_id
        ).first()
        if username_exists:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Username already exists")

    # Check if email is being changed and if it already exists
    if user_update.email != existing_user.email:
        email_exists = db.query(models.User).filter(
            models.User.email == user_update.email,
            models.User.id != user_id
        ).first()
        if email_exists:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already exists")

    # Update the user details
    update_data = user_update.model_dump()
    for key, value in update_data.items():
        if hasattr(existing_user, key):
            setattr(existing_user, key, value)

    db.commit()
    db.refresh(existing_user)
    return existing_user

# delete user (admin only)
@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(user_id: int, db: Session = Depends(get_db), admin_user: models.User = Depends(check_admin_permission)):
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Prevent admin from deleting themselves
    if user.username == admin_user.username:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Cannot delete your own account")

    db.delete(user)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)

# Change user password (admin only)
@router.put("/{user_id}/password")
def change_user_password(
    user_id: int,
    password_data: schema.PasswordChange,
    db: Session = Depends(get_db),
    admin_user: models.User = Depends(check_admin_permission)
):
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Hash the new password
    hashed_password = hash_password(password_data.new_password)
    user.hashed_password = hashed_password
    user.password = hashed_password  # For backward compatibility

    db.commit()
    return {"message": f"Password updated successfully for user {user.username}"}
