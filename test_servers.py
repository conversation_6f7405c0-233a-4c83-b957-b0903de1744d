#!/usr/bin/env python3
"""
Test if servers are running
"""

import requests
import time

def test_backend():
    """Test if backend is running"""
    try:
        response = requests.get('http://localhost:8000/docs', timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running on http://localhost:8000")
            print("   API docs available at: http://localhost:8000/docs")
            return True
        else:
            print(f"❌ Backend responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend is not responding: {e}")
        return False

def test_frontend():
    """Test if frontend is running"""
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running on http://localhost:3000")
            return True
        else:
            print(f"❌ Frontend responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend is not responding: {e}")
        return False

def main():
    print("Testing servers...")
    print("=" * 50)
    
    backend_ok = test_backend()
    frontend_ok = test_frontend()
    
    print("\n" + "=" * 50)
    if backend_ok and frontend_ok:
        print("🎉 Both servers are running!")
        print("\nYou can access:")
        print("- Frontend: http://localhost:3000")
        print("- Backend API: http://localhost:8000")
        print("- API Documentation: http://localhost:8000/docs")
    elif backend_ok:
        print("⚠️  Backend is running but frontend is not")
        print("Try running: npm start (in the frontend directory)")
    elif frontend_ok:
        print("⚠️  Frontend is running but backend is not")
        print("Try running: uvicorn main:app --reload --port 8000")
    else:
        print("❌ Neither server is running")
        print("Start backend: uvicorn main:app --reload --port 8000")
        print("Start frontend: npm start (in frontend directory)")

if __name__ == "__main__":
    main()
