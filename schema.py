from pydantic import BaseModel
# from sqlalchemy
import uuid
from typing import Optional, List

# Generate a random UUID (Version 4)
generated_uuid = uuid.uuid4()

# Print the UUID object
# print(generated_uuid)

# Convert the UUID object to a string
uuid_string = str(generated_uuid)
# print(f"UUID as string: {uuid_string}")

# Access the hexadecimal representation without hyphens
uuid_hex = generated_uuid.hex

# UserBase schema
class UserBase(BaseModel):
    id: Optional[int] = None
    username: str
    email: str
    status: str
    viewonly: Optional[str] = None
    policyadmin: Optional[str] = None
    servicing: Optional[str] = None
    mimoadmin: Optional[str] = None
    sysadmin: Optional[str] = None

# UserCreate schema 
class UserCreate(BaseModel):
    id: Optional[int] = None
    username: str
    email: str
    password: str
    status: str
    viewonly: Optional[str] = None
    policyadmin: Optional[str] = None
    servicing: Optional[str] = None
    mimoadmin: Optional[str] = None
    sysadmin: Optional[str] = None

class Userlogin(BaseModel):
    username: str
    password: str

# Policy
class Policy(BaseModel):
    id: Optional[int] = None
    policyno: str
    packages: str
    bus: bool
    policypayer: str
    agentcode: str
    agentname: str
    pep: bool
    aml: bool
    applicantsigneddate: str
    agentsigneddate: str
    applicationform: bool
    kycform: bool
    mandateform: bool
    schemecode: str
    worksitecode: str
    riskprofile: str
    status: str
    datecreated: str
    createdby: int

    class Config:
        orm_mode = True


# policyholder
# id,policyno,title,initials,middlename,surname,previousname,gender,dateofbirth,countryofbirth,maritalstatus,residentialstatus,primaryid,primaryidexpirydate,secondaryid,secondaryidexpirydate
# ,expatriate,workpermit,workpermitexpirydate,sourceofincome,netmonthlyincome,specifyincome,proofofsourceoffunds,specifyproofofsourceoffunds,occupation,industry,employer,other,commpreference
# ,emailaddress,phoneno,altmobileno,physicaladdressline1,physicaladdressline2,physicaladdressline3,physicaladdressline4,physicaladdressline5city,physicaladdressline6country
# ,permanentaddressvillage,permanentaddressta,permanentaddressdistrict
# ,postaladdressline1,postaladdressline2,postaladdressline3,postaladdressline4,postaladdressline5city,postaladdressline6country
class PolicyHolder(BaseModel):
    id: Optional[int] = None
    policyno: Optional[str] = None
    title: Optional[str] = None
    initials: Optional[str] = None
    firstname: Optional[str] = None  # Added missing field
    middlename: Optional[str] = None
    surname: Optional[str] = None
    previousname: Optional[str] = None
    gender: Optional[str] = None
    dateofbirth: Optional[str] = None
    countryofbirth: Optional[str] = None
    maritalstatus: Optional[str] = None
    residentialstatus: Optional[str] = None
    primaryid: Optional[str] = None
    primaryidexpirydate: Optional[str] = None
    secondaryid: Optional[str] = None
    secondaryidexpirydate: Optional[str] = None
    expatriate: Optional[str] = None
    workpermit: Optional[str] = None
    workpermitexpirydate: Optional[str] = None
    sourceofincome: Optional[str] = None
    netmonthlyincome: Optional[str] = None
    specifyincome: Optional[str] = None
    proofofsourceoffunds: Optional[str] = None
    specifyproofofsourceoffunds: Optional[str] = None
    occupation: Optional[str] = None
    industry: Optional[str] = None
    employer: Optional[str] = None
    other: Optional[str] = None
    commpreference: Optional[str] = None
    emailaddress: Optional[str] = None
    phoneno: Optional[str] = None
    altmobileno: Optional[str] = None
    physicaladdressline1: Optional[str] = None
    physicaladdressline2: Optional[str] = None
    physicaladdressline3: Optional[str] = None
    physicaladdressline4: Optional[str] = None
    physicaladdressline5city: Optional[str] = None
    physicaladdressline6country: Optional[str] = None
    permanentaddressvillage: Optional[str] = None
    permanentaddressta: Optional[str] = None
    permanentaddressdistrict: Optional[str] = None
    postaladdressline1: Optional[str] = None
    postaladdressline2: Optional[str] = None
    postaladdressline3: Optional[str] = None
    postaladdressline4: Optional[str] = None
    postaladdressline5city: Optional[str] = None
    postaladdressline6country: Optional[str] = None
    datecreated: Optional[str] = None  # Added missing field
    createdby: Optional[int] = None   # Added missing field
    capturedate: Optional[str] = None  # Added missing field
    idnumber: Optional[str] = None     # Added missing field
    idtype: Optional[str] = None       # Added missing field
    mobilenumber: Optional[str] = None # Added missing field
    alternativenumber: Optional[str] = None # Added missing field
    postalcity: Optional[str] = None   # Added missing field
    postalcountrycode: Optional[str] = None # Added missing field
    town: Optional[str] = None         # Added missing field
    residentialaddressline1: Optional[str] = None # Added missing field
    residentialcountrycode: Optional[str] = None  # Added missing field
    residentialdistrict: Optional[str] = None     # Added missing field
    residentialvillage: Optional[str] = None      # Added missing field
    traditionalauthority: Optional[str] = None    # Added missing field
    contractsigneddate: Optional[str] = None      # Added missing field

    class Config:
        orm_mode = True

# PremiumPayer schema
class PremiumPayer(BaseModel):
    id: Optional[int] = None
    policyno: str
    title: str
    initials: str
    middlename: Optional[str] = None
    surname: str
    previousname: Optional[str] = None
    gender: str
    dateofbirth: str
    countryofbirth: str
    maritalstatus: str
    residentialstatus: str
    primaryid: str
    primaryidexpirydate: str
    secondaryid: Optional[str] = None
    secondaryidexpirydate: Optional[str] = None
    expatriate: Optional[str] = None
    workpermit: Optional[str] = None
    workpermitexpirydate: Optional[str] = None
    sourceofincome: Optional[str] = None
    netmonthlyincome: Optional[str] = None
    specifyincome: Optional[str] = None
    proofofsourceoffunds: Optional[str] = None
    specifyproofofsourceoffunds: Optional[str] = None
    occupation: Optional[str] = None
    industry: Optional[str] = None
    employer: Optional[str] = None
    other: Optional[str] = None
    commpreference: Optional[str] = None
    emailaddress: Optional[str] = None
    phoneno: Optional[str] = None
    altmobileno: Optional[str] = None
    physicaladdressline1: Optional[str] = None
    physicaladdressline2: Optional[str] = None
    physicaladdressline3: Optional[str] = None
    physicaladdressline4: Optional[str] = None
    physicaladdressline5city: Optional[str] = None
    physicaladdressline6country: Optional[str] = None
    permanentaddressvillage: Optional[str] = None
    permanentaddressta: Optional[str] = None
    permanentaddressdistrict: Optional[str] = None
    postaladdressline1: Optional[str] = None
    postaladdressline2: Optional[str] = None
    postaladdressline3: Optional[str] = None
    postaladdressline4: Optional[str] = None
    postaladdressline5city: Optional[str] = None
    postaladdressline6country: Optional[str] = None

    class Config:
        orm_mode = True

# PolicyMandate schema
class PolicyMandate(BaseModel):
    id: Optional[int] = None
    policyno: str
    modeofpayment: str
    frequency: str
    premium: str
    firstdeductiondate: str
    debitorderFirstname: Optional[str] = None
    debitorderLastname: Optional[str] = None
    debitorderNameofbank: Optional[str] = None
    debitorderBranch: Optional[str] = None
    debitorderAccountno: Optional[str] = None
    debitorderTypeofaccount: Optional[str] = None
    debitorderSalaryfundingdate: Optional[str] = None
    debitorderPremiumonapplication: Optional[str] = None
    stoporderEmployeeid: Optional[str] = None
    stoporderEmployeename: Optional[str] = None
    stoporderEmployername: Optional[str] = None
    stoporderEmployeraddress: Optional[str] = None
    mobileoperator: Optional[str] = None
    mobilephoneno: Optional[str] = None

    class Config:
        orm_mode = True

# PolicySummary schema
class PolicySummary(BaseModel):
    id: Optional[int] = None
    policyno: str
    premiumsdue: str
    premiumspaid: str
    totalpremiumspaid: str
    outstanding: str

    class Config:
        orm_mode = True

# PolicyActivities schema
class PolicyActivities(BaseModel):
    id: Optional[int] = None
    policyid: str
    activitydesc: str
    remarks: Optional[str] = None
    status: str
    datecreated: str
    createdby: str
    activitydetails: Optional[str] = None
    effectivedate: Optional[str] = None

    class Config:
        orm_mode = True


# Beneficiaries schema
class BeneficiariesBase(BaseModel):
    id: Optional[int] = None
    policyid: Optional[str] = None
    policyno: Optional[str] = None  # Added missing policyno field
    title: Optional[str] = None
    initials: Optional[str] = None
    firstname: Optional[str] = None
    middlename: Optional[str] = None
    surname: Optional[str] = None
    gender: Optional[str] = None
    birthdate: Optional[str] = None
    maritalstatus: Optional[str] = None
    membertype: Optional[str] = None
    benofownership: Optional[str] = None
    relationship: Optional[str] = None
    status: Optional[str] = None
    datecreated: Optional[str] = None
    createdby: Optional[int] = None
    idnumber: Optional[str] = None  # Added missing idnumber field

class UserInDB(UserCreate):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None
    class Config:
        # orm_mode = True
        from_attributes = True

class PasswordChange(BaseModel):
    new_password: str

class UserUpdate(BaseModel):
    username: str
    email: str
    status: str
    viewonly: Optional[str] = None
    policyadmin: Optional[str] = None
    servicing: Optional[str] = None
    mimoadmin: Optional[str] = None
    sysadmin: Optional[str] = None