import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, Table } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import apiService from '../../services/apiService';

const PolicyDetails = () => {
  const [policy, setPolicy] = useState(null);
  const [policyholder, setPolicyholder] = useState(null);
  const [policyMandate, setPolicyMandate] = useState(null);
  const [policySummary, setPolicySummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    fetchPolicyDetails();
  }, [id]);

  const fetchPolicyDetails = async () => {
    try {
      setLoading(true);
      
      // Fetch policy details
      const policyResponse = await apiService.getPolicy(id);
      setPolicy(policyResponse.data);
      
      // Try to fetch related data
      try {
        const policyholderResponse = await apiService.searchPolicyholders(policyResponse.data.policyno);
        if (policyholderResponse.data.length > 0) {
          setPolicyholder(policyholderResponse.data[0]);
        }
      } catch (err) {
        console.log('No policyholder found');
      }
      
      // Fetch other related data similarly...
      
    } catch (err) {
      console.error('Error fetching policy details:', err);
      setError('Failed to load policy details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'Active': 'success',
      'Pending': 'warning',
      'Expired': 'danger',
      'Cancelled': 'secondary'
    };
    return statusMap[status] || 'secondary';
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" variant="primary" />
      </div>
    );
  }

  if (error) {
    return (
      <Container fluid>
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      </Container>
    );
  }

  if (!policy) {
    return (
      <Container fluid>
        <Alert variant="warning">
          <i className="fas fa-info-circle me-2"></i>
          Policy not found
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-0 text-gray-800">Policy Details</h1>
              <p className="text-muted">Policy Number: {policy.policyno}</p>
            </div>
            <div className="d-flex gap-2">
              <Button 
                variant="primary" 
                onClick={() => navigate(`/policies/${id}/edit`)}
              >
                <i className="fas fa-edit me-2"></i>
                Edit Policy
              </Button>
              <Button 
                variant="outline-secondary" 
                onClick={() => navigate('/policies')}
              >
                <i className="fas fa-arrow-left me-2"></i>
                Back to Policies
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      <Row>
        <Col lg={8}>
          {/* Policy Information */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-file-contract me-2"></i>
                Policy Information
              </h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <Table borderless>
                    <tbody>
                      <tr>
                        <td className="fw-bold">Policy Number:</td>
                        <td>{policy.policyno}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Package:</td>
                        <td>{policy.packages}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Business Unit:</td>
                        <td>{policy.bus}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Status:</td>
                        <td>
                          <Badge bg={getStatusBadge(policy.status)}>
                            {policy.status}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Date Created:</td>
                        <td>{policy.datecreated}</td>
                      </tr>
                    </tbody>
                  </Table>
                </Col>
                <Col md={6}>
                  <Table borderless>
                    <tbody>
                      <tr>
                        <td className="fw-bold">Agent Code:</td>
                        <td>{policy.agentcode}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Agent Name:</td>
                        <td>{policy.agentname}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Scheme Code:</td>
                        <td>{policy.schemecode || 'N/A'}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Worksite Code:</td>
                        <td>{policy.worksitecode || 'N/A'}</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Risk Profile:</td>
                        <td>{policy.riskprofile || 'N/A'}</td>
                      </tr>
                    </tbody>
                  </Table>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Policyholder Information */}
          {policyholder && (
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">
                  <i className="fas fa-user me-2"></i>
                  Policyholder Information
                </h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Table borderless>
                      <tbody>
                        <tr>
                          <td className="fw-bold">Title:</td>
                          <td>{policyholder.title}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Full Name:</td>
                          <td>{`${policyholder.initials} ${policyholder.surname}`}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Gender:</td>
                          <td>{policyholder.gender}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Date of Birth:</td>
                          <td>{policyholder.dateofbirth}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Country of Birth:</td>
                          <td>{policyholder.countryofbirth}</td>
                        </tr>
                      </tbody>
                    </Table>
                  </Col>
                  <Col md={6}>
                    <Table borderless>
                      <tbody>
                        <tr>
                          <td className="fw-bold">Marital Status:</td>
                          <td>{policyholder.maritalstatus}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Primary ID:</td>
                          <td>{policyholder.primaryid}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Email:</td>
                          <td>{policyholder.emailaddress || 'N/A'}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Phone:</td>
                          <td>{policyholder.phoneno || 'N/A'}</td>
                        </tr>
                        <tr>
                          <td className="fw-bold">Occupation:</td>
                          <td>{policyholder.occupation || 'N/A'}</td>
                        </tr>
                      </tbody>
                    </Table>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}
        </Col>

        <Col lg={4}>
          {/* Quick Actions */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-bolt me-2"></i>
                Quick Actions
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button variant="primary" onClick={() => navigate(`/policies/${id}/edit`)}>
                  <i className="fas fa-edit me-2"></i>
                  Edit Policy
                </Button>
                <Button variant="outline-info">
                  <i className="fas fa-print me-2"></i>
                  Print Policy
                </Button>
                <Button variant="outline-success">
                  <i className="fas fa-download me-2"></i>
                  Download PDF
                </Button>
                <Button variant="outline-warning">
                  <i className="fas fa-envelope me-2"></i>
                  Send Email
                </Button>
              </div>
            </Card.Body>
          </Card>

          {/* Policy Summary */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-chart-line me-2"></i>
                Policy Summary
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="text-center">
                <div className="mb-3">
                  <h3 className="text-primary">
                    <Badge bg={getStatusBadge(policy.status)} className="fs-6">
                      {policy.status}
                    </Badge>
                  </h3>
                </div>
                <div className="row text-center">
                  <div className="col-6">
                    <div className="border-end">
                      <div className="h5 mb-0">0</div>
                      <small className="text-muted">Claims</small>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="h5 mb-0">$0</div>
                    <small className="text-muted">Premium Paid</small>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Recent Activities */}
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-history me-2"></i>
                Recent Activities
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="text-center text-muted py-3">
                <i className="fas fa-clock fa-2x mb-2"></i>
                <p>No recent activities</p>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default PolicyDetails;
