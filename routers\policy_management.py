from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

import models, schema, auth
from db import get_db
from auth import get_current_user

router = APIRouter(prefix="/policy-management", tags=["Policy Management"])
security = HTTPBearer()

# Pydantic models for comprehensive policy creation
class PolicyDetailsCreate(BaseModel):
    policyno: str
    packages: str
    bus: bool
    pep: bool
    agentcode: str
    agentname: str
    status: str
    schemecode: Optional[str] = None
    worksitecode: Optional[str] = None
    riskprofile: Optional[str] = None
    # Optional fields with defaults
    applicantsigneddate: Optional[str] = None
    agentsigneddate: Optional[str] = None
    applicationform: Optional[bool] = True
    kycform: Optional[bool] = True
    mandateform: Optional[bool] = True
    aml: Optional[bool] = False
    policypayer: Optional[str] = None

class PolicyHolderCreate(BaseModel):
    title: str
    initials: str
    firstname: str
    middlename: Optional[str] = None
    surname: str
    gender: str
    dateofbirth: str
    maritalstatus: str
    capturedate: str
    # Optional fields
    idnumber: Optional[str] = None
    idtype: Optional[str] = None
    occupation: Optional[str] = None
    mobilenumber: Optional[str] = None
    alternativenumber: Optional[str] = None
    emailaddress: Optional[str] = None
    town: Optional[str] = None
    residentialstatus: Optional[str] = None

class PaymentMandateCreate(BaseModel):
    firstname: str
    surname: str
    mobilenumber: str
    frequency: str
    premium: str
    modeofpayment: str
    firstdeductiondate: str
    paypointname: str
    # Optional fields
    bankaccountnumber: Optional[str] = None
    bankaccounttype: Optional[str] = None
    branchname: Optional[str] = None

class BeneficiaryCreate(BaseModel):
    title: str
    initials: str
    firstname: str
    surname: str
    gender: str
    birthdate: str
    relationship: str
    membertype: str
    # Optional fields
    idnumber: Optional[str] = None

class ComprehensivePolicyCreate(BaseModel):
    policy_details: PolicyDetailsCreate
    policy_holder: PolicyHolderCreate
    payment_mandate: PaymentMandateCreate
    beneficiaries: List[BeneficiaryCreate]

@router.post("/comprehensive")
async def create_comprehensive_policy(
    policy_data: ComprehensivePolicyCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a comprehensive policy with all related data"""
    
    try:
        # Check if policy number already exists
        existing_policy = db.query(models.Policy).filter(
            models.Policy.policyno == policy_data.policy_details.policyno
        ).first()
        
        if existing_policy:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Policy number {policy_data.policy_details.policyno} already exists"
            )
        
        # 1. Create Policy
        policy_dict = policy_data.policy_details.dict()
        policy_dict.update({
            "datecreated": datetime.now().strftime('%Y-%m-%d'),
            "createdby": int(current_user.get('id', 1)),
            # Add required fields with default values
            "applicantsigneddate": datetime.now().strftime('%Y-%m-%d'),
            "agentsigneddate": datetime.now().strftime('%Y-%m-%d'),
            "applicationform": True,
            "kycform": True,
            "mandateform": True,
            "aml": False,
            "policypayer": ""
        })
        
        policy = models.Policy(**policy_dict)
        db.add(policy)
        db.flush()  # Get the policy ID
        
        # 2. Create PolicyHolder
        holder_dict = {
            "policyno": policy_data.policy_details.policyno,
            "title": policy_data.policy_holder.title,
            "initials": policy_data.policy_holder.initials,
            "firstname": policy_data.policy_holder.firstname,
            "middlename": policy_data.policy_holder.middlename or "",
            "surname": policy_data.policy_holder.surname,
            "gender": policy_data.policy_holder.gender,
            "dateofbirth": policy_data.policy_holder.dateofbirth,
            "maritalstatus": policy_data.policy_holder.maritalstatus,
            "phoneno": policy_data.policy_holder.mobilenumber or "",  # Map mobilenumber to phoneno
            "emailaddress": policy_data.policy_holder.emailaddress or "",
            "occupation": policy_data.policy_holder.occupation or "",
            "residentialstatus": policy_data.policy_holder.residentialstatus or "",
            "primaryid": policy_data.policy_holder.idnumber or "",
            "datecreated": datetime.now().strftime('%Y-%m-%d'),
            "createdby": int(current_user.get('id', 1)),
            # Set default values for other required fields
            "previousname": "",
            "countryofbirth": "",
            "primaryidexpirydate": "",
            "secondaryid": "",
            "secondaryidexpirydate": "",
            "expatriate": "",
            "workpermit": "",
            "workpermitexpirydate": "",
            "sourceofincome": "",
            "netmonthlyincome": "",
            "specifyincome": "",
            "proofofsourceoffunds": "",
            "specifyproofofsourceoffunds": "",
            "industry": "",
            "employer": "",
            "other": "",
            "commpreference": "",
            "altmobileno": policy_data.policy_holder.alternativenumber or "",
            "physicaladdressline1": "",
            "physicaladdressline2": "",
            "physicaladdressline3": "",
            "physicaladdressline4": "",
            "physicaladdressline5city": policy_data.policy_holder.town or "",
            "physicaladdressline6country": "",
            "permanentaddressvillage": "",
            "permanentaddressta": "",
            "permanentaddressdistrict": "",
            "postaladdressline1": "",
            "postaladdressline2": "",
            "postaladdressline3": "",
            "postaladdressline4": "",
            "postaladdressline5city": "",
            "postaladdressline6country": ""
        }
        
        policy_holder = models.PolicyHolder(**holder_dict)
        db.add(policy_holder)
        
        # 3. Create PaymentMandate
        mandate_dict = {
            "policyno": policy_data.policy_details.policyno,
            "frequency": policy_data.payment_mandate.frequency,
            "premium": policy_data.payment_mandate.premium,
            "modeofpayment": policy_data.payment_mandate.modeofpayment,
            "firstdeductiondate": policy_data.payment_mandate.firstdeductiondate,
            "debitorderFirstname": policy_data.payment_mandate.firstname,
            "debitorderLastname": policy_data.payment_mandate.surname,
            "mobilephoneno": policy_data.payment_mandate.mobilenumber,
            "debitorderAccountno": policy_data.payment_mandate.bankaccountnumber or "",
            "debitorderTypeofaccount": policy_data.payment_mandate.bankaccounttype or "",
            "debitorderBranch": policy_data.payment_mandate.branchname or "",
            "stoporderEmployername": policy_data.payment_mandate.paypointname,
            "mobileoperator": policy_data.payment_mandate.paypointname,
            "debitorderNameofbank": policy_data.payment_mandate.paypointname,
            "datecreated": datetime.now().strftime('%Y-%m-%d'),
            "createdby": int(current_user.get('id', 1))
        }
        
        payment_mandate = models.PolicyMandate(**mandate_dict)
        db.add(payment_mandate)
        
        # 4. Create Beneficiaries
        for beneficiary_data in policy_data.beneficiaries:
            beneficiary_dict = beneficiary_data.dict()
            beneficiary_dict.update({
                "policyid": str(policy.id),
                "policyno": policy_data.policy_details.policyno,
                "status": "ACTIVE",
                "datecreated": datetime.now().strftime('%Y-%m-%d'),
                "createdby": int(current_user.get('id', 1))
            })
            
            # Handle optional fields
            for key, value in beneficiary_dict.items():
                if value is None:
                    beneficiary_dict[key] = ""
            
            beneficiary = models.Beneficiaries(**beneficiary_dict)
            db.add(beneficiary)
        
        # Commit all changes
        db.commit()
        
        return {
            "status": "success",
            "message": f"Policy {policy_data.policy_details.policyno} created successfully",
            "policy_id": policy.id,
            "policy_number": policy_data.policy_details.policyno
        }
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating comprehensive policy: {str(e)}"
        )

# Policy Search and CRUD Operations

@router.get("/search")
async def search_policies(
    policy_number: Optional[str] = Query(None, description="Policy number"),
    first_name: Optional[str] = Query(None, description="Policy holder first name"),
    surname: Optional[str] = Query(None, description="Policy holder surname"),
    status: Optional[str] = Query(None, description="Policy status"),
    package: Optional[str] = Query(None, description="Policy package"),
    mode_of_payment: Optional[str] = Query(None, description="Mode of payment"),
    frequency: Optional[str] = Query(None, description="Payment frequency"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search policies with multiple filters"""

    try:
        # Build the query with joins
        query = db.query(
            models.Policy,
            models.PolicyHolder,
            models.PolicyMandate
        ).outerjoin(
            models.PolicyHolder, models.Policy.policyno == models.PolicyHolder.policyno
        ).outerjoin(
            models.PolicyMandate, models.Policy.policyno == models.PolicyMandate.policyno
        )

        # Apply filters
        if policy_number:
            query = query.filter(models.Policy.policyno.ilike(f"%{policy_number}%"))

        if first_name:
            query = query.filter(models.PolicyHolder.firstname.ilike(f"%{first_name}%"))

        if surname:
            query = query.filter(models.PolicyHolder.surname.ilike(f"%{surname}%"))

        if status:
            query = query.filter(models.Policy.status.ilike(f"%{status}%"))

        if package:
            query = query.filter(models.Policy.packages.ilike(f"%{package}%"))

        if mode_of_payment:
            query = query.filter(models.PolicyMandate.modeofpayment.ilike(f"%{mode_of_payment}%"))

        if frequency:
            query = query.filter(models.PolicyMandate.frequency.ilike(f"%{frequency}%"))

        # Get total count
        total_count = query.count()

        # Apply pagination
        offset = (page - 1) * limit
        results = query.offset(offset).limit(limit).all()

        # Format results
        policies = []
        for policy, holder, mandate in results:
            policy_data = {
                "id": policy.id,
                "policyno": policy.policyno,
                "packages": policy.packages,
                "status": policy.status,
                "agentcode": policy.agentcode,
                "agentname": policy.agentname,
                "applicantsigneddate": policy.applicantsigneddate,
                "datecreated": policy.datecreated,
                "holder": {
                    "title": holder.title if holder else "",
                    "firstname": holder.firstname if holder else "",
                    "surname": holder.surname if holder else "",
                    "gender": holder.gender if holder else "",
                    "dateofbirth": holder.dateofbirth if holder else "",
                    "mobilenumber": holder.mobilenumber if holder else "",
                    "emailaddress": holder.emailaddress if holder else ""
                } if holder else None,
                "mandate": {
                    "frequency": mandate.frequency if mandate else "",
                    "premium": mandate.premium if mandate else "",
                    "modeofpayment": mandate.modeofpayment if mandate else "",
                    "firstdeductiondate": mandate.firstdeductiondate if mandate else ""
                } if mandate else None
            }
            policies.append(policy_data)

        return {
            "status": "success",
            "data": policies,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": (total_count + limit - 1) // limit
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching policies: {str(e)}"
        )

@router.get("/{policy_id}")
async def get_policy_details(
    policy_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive policy details by ID"""

    try:
        # Get policy
        policy = db.query(models.Policy).filter(models.Policy.id == policy_id).first()
        if not policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Policy with ID {policy_id} not found"
            )

        # Get related data
        holder = db.query(models.PolicyHolder).filter(
            models.PolicyHolder.policyno == policy.policyno
        ).first()

        mandate = db.query(models.PolicyMandate).filter(
            models.PolicyMandate.policyno == policy.policyno
        ).first()

        beneficiaries = db.query(models.Beneficiaries).filter(
            models.Beneficiaries.policyno == policy.policyno
        ).all()

        # Format response
        policy_details = {
            "policy": {
                "id": policy.id,
                "policyno": policy.policyno,
                "packages": policy.packages,
                "bus": policy.bus,
                "pep": policy.pep,
                "agentcode": policy.agentcode,
                "agentname": policy.agentname,
                "applicantsigneddate": policy.applicantsigneddate,
                "status": policy.status,
                "schemecode": policy.schemecode,
                "worksitecode": policy.worksitecode,
                "riskprofile": policy.riskprofile,
                "datecreated": policy.datecreated
            },
            "holder": holder.__dict__ if holder else None,
            "mandate": mandate.__dict__ if mandate else None,
            "beneficiaries": [beneficiary.__dict__ for beneficiary in beneficiaries]
        }

        return {
            "status": "success",
            "data": policy_details
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving policy details: {str(e)}"
        )

@router.put("/{policy_id}")
async def update_policy(
    policy_id: int,
    policy_data: ComprehensivePolicyCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update comprehensive policy details"""

    try:
        # Get existing policy
        policy = db.query(models.Policy).filter(models.Policy.id == policy_id).first()
        if not policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Policy with ID {policy_id} not found"
            )

        # Update policy
        for key, value in policy_data.policy_details.dict().items():
            setattr(policy, key, value)

        # Update policy holder with proper field mapping
        holder = db.query(models.PolicyHolder).filter(
            models.PolicyHolder.policyno == policy.policyno
        ).first()

        if holder:
            # Map fields properly from API to database model
            holder_updates = {
                "title": policy_data.policy_holder.title,
                "initials": policy_data.policy_holder.initials,
                "firstname": policy_data.policy_holder.firstname,
                "middlename": policy_data.policy_holder.middlename or "",
                "surname": policy_data.policy_holder.surname,
                "gender": policy_data.policy_holder.gender,
                "dateofbirth": policy_data.policy_holder.dateofbirth,
                "maritalstatus": policy_data.policy_holder.maritalstatus,
                "phoneno": policy_data.policy_holder.mobilenumber or "",  # Map mobilenumber to phoneno
                "emailaddress": policy_data.policy_holder.emailaddress or "",
                "occupation": policy_data.policy_holder.occupation or "",
                "residentialstatus": policy_data.policy_holder.residentialstatus or "",
                "primaryid": policy_data.policy_holder.idnumber or "",
                "altmobileno": policy_data.policy_holder.alternativenumber or "",
                "physicaladdressline5city": policy_data.policy_holder.town or ""
            }

            for key, value in holder_updates.items():
                setattr(holder, key, value)

        # Update payment mandate
        mandate = db.query(models.PolicyMandate).filter(
            models.PolicyMandate.policyno == policy.policyno
        ).first()

        if mandate:
            mandate_dict = {
                "frequency": policy_data.payment_mandate.frequency,
                "premium": policy_data.payment_mandate.premium,
                "modeofpayment": policy_data.payment_mandate.modeofpayment,
                "firstdeductiondate": policy_data.payment_mandate.firstdeductiondate,
                "debitorderFirstname": policy_data.payment_mandate.firstname,
                "debitorderLastname": policy_data.payment_mandate.surname,
                "mobilephoneno": policy_data.payment_mandate.mobilenumber,
                "debitorderAccountno": policy_data.payment_mandate.bankaccountnumber or "",
                "debitorderTypeofaccount": policy_data.payment_mandate.bankaccounttype or "",
                "debitorderBranch": policy_data.payment_mandate.branchname or "",
                "stoporderEmployername": policy_data.payment_mandate.paypointname,
                "mobileoperator": policy_data.payment_mandate.paypointname,
                "debitorderNameofbank": policy_data.payment_mandate.paypointname
            }

            for key, value in mandate_dict.items():
                setattr(mandate, key, value)

        # Update beneficiaries - delete existing and create new ones
        # Delete existing beneficiaries
        db.query(models.Beneficiaries).filter(
            models.Beneficiaries.policyno == policy.policyno
        ).delete()

        # Create new beneficiaries
        for beneficiary_data in policy_data.beneficiaries:
            beneficiary_dict = {
                "policyid": str(policy.id),
                "policyno": policy.policyno,
                "title": beneficiary_data.title,
                "initials": beneficiary_data.initials,
                "firstname": beneficiary_data.firstname,
                "surname": beneficiary_data.surname,
                "gender": beneficiary_data.gender,
                "birthdate": beneficiary_data.birthdate,
                "relationship": beneficiary_data.relationship,
                "membertype": beneficiary_data.membertype,
                "idnumber": beneficiary_data.idnumber or "",
                "status": "ACTIVE",
                "datecreated": datetime.now().strftime('%Y-%m-%d'),
                "createdby": int(current_user.get('id', 1)),
                # Set default values for other fields
                "middlename": "",
                "maritalstatus": "",
                "benofownership": ""
            }

            beneficiary = models.Beneficiaries(**beneficiary_dict)
            db.add(beneficiary)

        db.commit()

        return {
            "status": "success",
            "message": f"Policy {policy.policyno} updated successfully"
        }

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating policy: {str(e)}"
        )

@router.delete("/{policy_id}")
async def delete_policy(
    policy_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete policy and all related data"""

    try:
        # Get policy
        policy = db.query(models.Policy).filter(models.Policy.id == policy_id).first()
        if not policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Policy with ID {policy_id} not found"
            )

        # Delete related data
        db.query(models.Beneficiaries).filter(
            models.Beneficiaries.policyno == policy.policyno
        ).delete()

        db.query(models.PolicyMandate).filter(
            models.PolicyMandate.policyno == policy.policyno
        ).delete()

        db.query(models.PolicyHolder).filter(
            models.PolicyHolder.policyno == policy.policyno
        ).delete()

        # Delete policy
        db.delete(policy)
        db.commit()

        return {
            "status": "success",
            "message": f"Policy {policy.policyno} deleted successfully"
        }

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting policy: {str(e)}"
        )
