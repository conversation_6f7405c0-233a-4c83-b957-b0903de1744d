import axios from 'axios';

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token) {
    if (token) {
      this.api.defaults.headers.Authorization = `Bear<PERSON> ${token}`;
    } else {
      delete this.api.defaults.headers.Authorization;
    }
  }

  // Auth endpoints
  async login(username, password) {
    return this.api.post('/login', { username, password });
  }

  async signup(userData) {
    return this.api.post('/signup', userData);
  }

  async checkToken() {
    return this.api.get('/checktoken/');
  }

  // Policy endpoints
  async getPolicies(skip = 0, limit = 100) {
    return this.api.get(`/policies/?skip=${skip}&limit=${limit}`);
  }

  async getPolicy(id) {
    return this.api.get(`/policies/${id}`);
  }

  async createPolicy(policyData) {
    return this.api.post('/policies/', policyData);
  }

  async updatePolicy(id, policyData) {
    return this.api.put(`/policies/${id}`, policyData);
  }

  async deletePolicy(id) {
    return this.api.delete(`/policies/${id}`);
  }

  async searchPolicies(searchTerm) {
    return this.api.get(`/policies/search/${searchTerm}`);
  }

  // Policyholder endpoints
  async getPolicyholders(skip = 0, limit = 100) {
    return this.api.get(`/policyholder/?skip=${skip}&limit=${limit}`);
  }

  async getPolicyholder(id) {
    return this.api.get(`/policyholder/${id}`);
  }

  async createPolicyholder(policyholderData) {
    return this.api.post('/policyholder/', policyholderData);
  }

  async updatePolicyholder(id, policyholderData) {
    return this.api.put(`/policyholder/${id}`, policyholderData);
  }

  async searchPolicyholders(policyNo) {
    return this.api.get(`/policyholder/search/${policyNo}`);
  }

  // Policy Summary endpoints
  async getPolicySummaries(skip = 0, limit = 100) {
    return this.api.get(`/policysummary/?skip=${skip}&limit=${limit}`);
  }

  async getPolicySummary(id) {
    return this.api.get(`/policysummary/${id}`);
  }

  // Policy Mandate endpoints
  async getPolicyMandates(skip = 0, limit = 100) {
    return this.api.get(`/policymandate/?skip=${skip}&limit=${limit}`);
  }

  async getPolicyMandate(id) {
    return this.api.get(`/policymandate/${id}`);
  }

  // Premium Payer endpoints
  async getPremiumPayers(skip = 0, limit = 100) {
    return this.api.get(`/payer/?skip=${skip}&limit=${limit}`);
  }

  async getPremiumPayer(id) {
    return this.api.get(`/payer/${id}`);
  }

  // Beneficiaries endpoints
  async getBeneficiaries(skip = 0, limit = 100) {
    return this.api.get(`/beneficiaries/?skip=${skip}&limit=${limit}`);
  }

  async getBeneficiary(id) {
    return this.api.get(`/beneficiaries/${id}`);
  }

  // Activities endpoints
  async getActivities(skip = 0, limit = 100) {
    return this.api.get(`/activities/?skip=${skip}&limit=${limit}`);
  }

  async getActivity(id) {
    return this.api.get(`/activities/${id}`);
  }

  // Users endpoints
  async getUsers(skip = 0, limit = 100) {
    return this.api.get(`/users/?skip=${skip}&limit=${limit}`);
  }

  async getUser(id) {
    return this.api.get(`/users/${id}`);
  }

  async createUser(userData) {
    return this.api.post('/users/', userData);
  }

  async updateUser(id, userData) {
    return this.api.put(`/users/${id}`, userData);
  }

  async deleteUser(id) {
    return this.api.delete(`/users/${id}`);
  }

  async changeUserPassword(id, passwordData) {
    return this.api.put(`/users/${id}/password`, passwordData);
  }

  async searchUsers(params) {
    return this.api.get('/users/search', { params });
  }

  // Import endpoints
  async validateImport(formData) {
    return this.api.post('/imports/validate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async processImport(formData) {
    return this.api.post('/imports/process', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async downloadTemplate(importType) {
    return this.api.get(`/imports/template/${importType}`, {
      responseType: 'blob'
    });
  }

  // Generic GET method for any endpoint
  async get(url, config = {}) {
    return this.api.get(url, config);
  }

  // Generic POST method for any endpoint
  async post(url, data, config = {}) {
    return this.api.post(url, data, config);
  }
}

const apiService = new ApiService();
export default apiService;
