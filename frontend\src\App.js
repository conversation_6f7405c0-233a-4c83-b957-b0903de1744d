import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './contexts/AuthContext';
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import Login from './components/Auth/Login';
import Dashboard from './components/Dashboard/Dashboard';
import PolicyList from './components/Policy/PolicyList';
import PolicyForm from './components/Policy/PolicyForm';
import PolicyDetails from './components/Policy/PolicyDetails';
// New comprehensive policy management components
import PolicySearch from './components/Policy/PolicySearch';
import NewPolicyCapture from './components/Policy/NewPolicyCapture';
import ComprehensivePolicyDetails from './components/Policy/ComprehensivePolicyDetails';
import PolicyEdit from './components/Policy/PolicyEdit';
import Search from './components/Search/Search';
import Reports from './components/Reports/Reports';
import UserManagement from './components/Users/<USER>';
import ImportData from './components/Import/ImportData';
import { Container, Row, Col } from 'react-bootstrap';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user } = useAuth();
  return user ? children : <Navigate to="/login" />;
};

// Main App Layout
const AppLayout = ({ children }) => {
  return (
    <>
      <Navbar />
      <Container fluid>
        <Row>
          <Col md={2} className="p-0">
            <Sidebar />
          </Col>
          <Col md={10} className="main-content p-4">
            {children}
          </Col>
        </Row>
      </Container>
    </>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <Dashboard />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <Dashboard />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policies"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <PolicyList />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policies/new"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <PolicyForm />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policies/:id"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <PolicyDetails />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policies/:id/edit"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <PolicyForm />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/search"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <Search />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <Reports />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/users"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <UserManagement />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/import"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <ImportData />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            {/* New Comprehensive Policy Management Routes */}
            <Route
              path="/policy-search"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <PolicySearch />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policy-capture"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <NewPolicyCapture />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policy-management/:id"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <ComprehensivePolicyDetails />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/policy-management/:id/edit"
              element={
                <ProtectedRoute>
                  <AppLayout>
                    <PolicyEdit />
                  </AppLayout>
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
