from fastapi import FastAP<PERSON>, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import <PERSON>A<PERSON>2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/beneficiaries",
    tags=['Beneficiaries'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# get all beneficiaries
@router.get("/", response_model=List[schema.BeneficiariesBase])
def get_beneficiaries(skip: int = 0, limit: int = 100, db: Session = Depends(get_db),current_user: dict = Depends(get_current_user)):
    beneficiaries = db.query(models.Beneficiaries).offset(skip).limit(limit).all()
    if not beneficiaries:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No beneficiaries found")
    return beneficiaries

# get beneficiary
@router.get("/{beneficiary_id}", response_model=schema.BeneficiariesBase)
def get_beneficiary(beneficiary_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    beneficiary = db.query(models.Beneficiaries).filter(models.Beneficiaries.id == beneficiary_id).first()
    if not beneficiary:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Beneficiary not found")
    return beneficiary

# create beneficiary
@router.post("/", response_model=schema.BeneficiariesBase, status_code=status.HTTP_201_CREATED)
def create_beneficiary(beneficiary: schema.BeneficiariesBase, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    # Check if the policy ID exists
    policy_exists = db.query(models.Policy).filter(models.Policy.id == beneficiary.policyid).first()
    if not policy_exists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    
    # Create the beneficiary
    beneficiary_data = models.Beneficiaries(**beneficiary.model_dump(), createdby=current_user['id'])
    db.add(beneficiary_data)
    db.commit()
    db.refresh(beneficiary_data)
    return beneficiary_data

# update beneficiary
@router.put("/{beneficiary_id}", response_model=schema.BeneficiariesBase)
def update_beneficiary(beneficiary_id: int, beneficiary: schema.BeneficiariesBase, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_beneficiary = db.query(models.Beneficiaries).filter(models.Beneficiaries.id == beneficiary_id).first()
    if not existing_beneficiary:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Beneficiary not found")
    
    # Check if the policy ID exists
    policy_exists = db.query(models.Policy).filter(models.Policy.id == beneficiary.policyid).first()
    if not policy_exists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    
    for key, value in beneficiary.model_dump().items():
        setattr(existing_beneficiary, key, value)
    
    db.commit()
    db.refresh(existing_beneficiary)
    return existing_beneficiary


# delete beneficiary
@router.delete("/{beneficiary_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_beneficiary(beneficiary_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_beneficiary = db.query(models.Beneficiaries).filter(models.Beneficiaries.id == beneficiary_id).first()
    if not existing_beneficiary:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Beneficiary not found")
    
    db.delete(existing_beneficiary)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)

# search beneficiary by policy number
@router.get("/search/policyno/{policy_no}", response_model=List[schema.BeneficiariesBase])
def search_beneficiary_by_policy_no(policy_no: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    beneficiaries = db.query(models.Beneficiaries).filter(models.Beneficiaries.policyno == policy_no).offset(skip).limit(limit).all()
    if not beneficiaries:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No beneficiaries found for the specified policy number")
    return beneficiaries

# search beneficiary by date created
@router.get("/search/datecreated/{date_created}", response_model=List[schema.BeneficiariesBase])
def search_beneficiary_by_date_created(date_created: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    beneficiaries = db.query(models.Beneficiaries).filter(models.Beneficiaries.datecreated == date_created).offset(skip).limit(limit).all()
    if not beneficiaries:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No beneficiaries found with the specified date created")
    return beneficiaries
