"""Add datecreated and createdby fields to PolicyMandate model

Revision ID: 5fa5d09864b6
Revises: c86b6e9ecb27
Create Date: 2025-08-05 01:54:01.777851

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5fa5d09864b6'
down_revision: Union[str, Sequence[str], None] = 'c86b6e9ecb27'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('policymandate', sa.Column('datecreated', sa.String(), nullable=True))
    op.add_column('policymandate', sa.Column('createdby', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_policymandate_createdby'), 'policymandate', ['createdby'], unique=False)
    op.create_index(op.f('ix_policymandate_datecreated'), 'policymandate', ['datecreated'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_policymandate_datecreated'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_createdby'), table_name='policymandate')
    op.drop_column('policymandate', 'createdby')
    op.drop_column('policymandate', 'datecreated')
    # ### end Alembic commands ###
