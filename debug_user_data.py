#!/usr/bin/env python3
"""
Debug script to help identify issues with user import data
"""

import pandas as pd
import sys
import os
from datetime import datetime

def get_required_columns(import_type):
    """Get required columns for each import type"""
    columns = {
        "policy": [
            "policyno", "packages", "bus", "pep", "agentcode",
            "agentname", "applicantsigneddate", "status"
            # Optional: schemecode, worksitecode, riskprofile, agentsigneddate
        ],
        "policyholder": [
            "policyno", "title", "initials", "firstname", "surname", "dateofbirth",
            "gender", "maritalstatus", "capturedate"
            # Optional: idnumber, idtype, occupation, mobilenumber, alternativenumber,
            # postaladdressline1-4, postalcity, postalcountrycode, town, emailaddress,
            # residentialaddressline1, residentialcountrycode, residentialdistrict,
            # residentialvillage, traditionalauthority, contractsigneddate, residentialstatus
        ],
        "policymandate": [
            "policyno", "firstname", "surname", "mobilenumber", "frequency",
            "premium", "modeofpayment", "FirstDeductionStartDate", "PaypointName"
            # Optional: BankAccountNumber, BankAccountType, BranchName
        ],
        "beneficiaries": [
            "policyno", "title", "initials", "firstname", "surname", "gender",
            "birthdate", "relationship", "membertype"
            # Optional: idnumber
        ]
    }
    return columns.get(import_type, [])

def validate_data_types(df, import_type):
    """Validate data types and formats"""
    issues = []
    
    if import_type == "policy":
        # Check boolean fields
        for col in ["bus", "pep"]:
            if col in df.columns:
                invalid_bools = df[~df[col].astype(str).str.lower().isin(['true', 'false', '1', '0', 'yes', 'no'])]
                if not invalid_bools.empty:
                    issues.append(f"Column '{col}' has invalid boolean values. Use 'true' or 'false'. Found: {invalid_bools[col].unique()}")
        
        # Check packages
        if "packages" in df.columns:
            valid_packages = ["Standard", "Premier", "Lite"]
            invalid_packages = df[~df["packages"].isin(valid_packages)]
            if not invalid_packages.empty:
                issues.append(f"Column 'packages' has invalid values. Use: {valid_packages}. Found: {invalid_packages['packages'].unique()}")
    
    elif import_type in ["policyholder", "beneficiaries"]:
        # Check date formats
        date_col = "dateofbirth" if import_type == "policyholder" else "birthdate"
        if date_col in df.columns:
            for idx, date_val in df[date_col].items():
                if pd.notna(date_val):
                    try:
                        # Try to parse as date
                        pd.to_datetime(str(date_val))
                    except:
                        issues.append(f"Row {idx+2}: Invalid date format in '{date_col}': '{date_val}'. Use YYYY-MM-DD format.")
        
        # Check gender
        if "gender" in df.columns:
            valid_genders = ["Male", "Female"]
            invalid_genders = df[~df["gender"].isin(valid_genders)]
            if not invalid_genders.empty:
                issues.append(f"Column 'gender' has invalid values. Use: {valid_genders}. Found: {invalid_genders['gender'].unique()}")
    
    elif import_type == "policymandate":
        # Check date format
        if "firstdeductiondate" in df.columns:
            for idx, date_val in df["firstdeductiondate"].items():
                if pd.notna(date_val):
                    try:
                        pd.to_datetime(str(date_val))
                    except:
                        issues.append(f"Row {idx+2}: Invalid date format in 'firstdeductiondate': '{date_val}'. Use YYYY-MM-DD format.")
        
        # Check premium is numeric
        if "premium" in df.columns:
            non_numeric = df[pd.to_numeric(df["premium"], errors='coerce').isna()]
            if not non_numeric.empty:
                issues.append(f"Column 'premium' has non-numeric values: {non_numeric['premium'].unique()}")
    
    return issues

def analyze_file(file_path, import_type):
    """Analyze a CSV/Excel file for import issues"""
    print(f"\n🔍 ANALYZING FILE: {file_path}")
    print(f"📋 IMPORT TYPE: {import_type}")
    print("=" * 60)
    
    try:
        # Read file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        print(f"✅ File loaded successfully")
        print(f"📊 Shape: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Check required columns
        required_columns = get_required_columns(import_type)
        file_columns = [col.strip().lower() for col in df.columns]
        required_columns_lower = [col.lower() for col in required_columns]
        missing_columns = [col for col in required_columns if col.lower() not in file_columns]
        
        if missing_columns:
            print(f"\n❌ MISSING REQUIRED COLUMNS:")
            for col in missing_columns:
                print(f"   - {col}")
        else:
            print(f"\n✅ All required columns present")
        
        # Check for empty rows
        empty_rows = df.isna().all(axis=1).sum()
        if empty_rows > 0:
            print(f"\n⚠️  WARNING: {empty_rows} completely empty rows found")
        
        # Check for empty columns
        empty_columns = [col for col in df.columns if df[col].isna().all()]
        if empty_columns:
            print(f"\n⚠️  WARNING: Empty columns found: {empty_columns}")
        
        # Check data types and formats
        print(f"\n🔍 VALIDATING DATA FORMATS...")
        data_issues = validate_data_types(df, import_type)
        
        if data_issues:
            print(f"\n❌ DATA FORMAT ISSUES:")
            for issue in data_issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ All data formats look good")
        
        # Show sample data
        print(f"\n📋 SAMPLE DATA (first 3 rows):")
        print(df.head(3).to_string())
        
        # Check for duplicates
        if "policyno" in df.columns:
            duplicates = df[df.duplicated(subset=['policyno'], keep=False)]
            if not duplicates.empty:
                print(f"\n⚠️  WARNING: Duplicate policy numbers found:")
                print(duplicates[['policyno']].to_string())
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"   - Total rows: {len(df)}")
        print(f"   - Missing columns: {len(missing_columns)}")
        print(f"   - Data format issues: {len(data_issues)}")
        print(f"   - Empty rows: {empty_rows}")
        print(f"   - Empty columns: {len(empty_columns)}")
        
        if missing_columns or data_issues:
            print(f"\n❌ FILE HAS ISSUES - Fix these before importing")
            return False
        else:
            print(f"\n✅ FILE LOOKS GOOD - Should validate successfully")
            return True
            
    except Exception as e:
        print(f"\n❌ ERROR READING FILE: {e}")
        print(f"   - Check file format (CSV/Excel)")
        print(f"   - Check file encoding (should be UTF-8 for CSV)")
        print(f"   - Check file is not corrupted")
        return False

def main():
    """Main function"""
    print("🔧 USER DATA VALIDATION DEBUGGER")
    print("=" * 60)
    
    if len(sys.argv) != 3:
        print("Usage: python debug_user_data.py <file_path> <import_type>")
        print("Import types: policy, policyholder, policymandate, beneficiaries")
        print("\nExample:")
        print("  python debug_user_data.py my_policies.csv policy")
        return
    
    file_path = sys.argv[1]
    import_type = sys.argv[2]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    if import_type not in ["policy", "policyholder", "policymandate", "beneficiaries"]:
        print(f"❌ Invalid import type: {import_type}")
        print("Valid types: policy, policyholder, policymandate, beneficiaries")
        return
    
    success = analyze_file(file_path, import_type)
    
    if success:
        print(f"\n🎉 Your file should work! Try uploading it to the import system.")
    else:
        print(f"\n🔧 Fix the issues above and try again.")

if __name__ == "__main__":
    main()
