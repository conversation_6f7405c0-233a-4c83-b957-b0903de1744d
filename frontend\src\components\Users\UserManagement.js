import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Table, Button, Modal, Form, Al<PERSON>, Spinner, Badge, InputGroup } from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaSearch, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/fa';
import apiService from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  const [selectedUser, setSelectedUser] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchFilters, setSearchFilters] = useState({
    status: '',
    role: ''
  });

  const { user: currentUser } = useAuth();

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    status: 'active',
    viewonly: 'no',
    policyadmin: 'no',
    servicing: 'no',
    mimoadmin: 'no',
    sysadmin: 'no'
  });

  const [passwordData, setPasswordData] = useState({
    new_password: ''
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await apiService.getUsers();
      setUsers(response.data);
      setError('');
    } catch (err) {
      setError('Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (searchTerm) params.append('q', searchTerm);
      if (searchFilters.status) params.append('status', searchFilters.status);
      if (searchFilters.role) params.append('role', searchFilters.role);

      const response = await apiService.api.get(`/users/search?${params.toString()}`);
      setUsers(response.data);
      setError('');
    } catch (err) {
      setError('Failed to search users');
      console.error('Error searching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchTerm || searchFilters.status || searchFilters.role) {
      searchUsers();
    } else {
      fetchUsers();
    }
  };

  const clearSearch = () => {
    setSearchTerm('');
    setSearchFilters({ status: '', role: '' });
    fetchUsers();
  };

  const openModal = (mode, user = null) => {
    setModalMode(mode);
    setSelectedUser(user);
    
    if (mode === 'create') {
      setFormData({
        username: '',
        email: '',
        password: '',
        status: 'active',
        viewonly: 'no',
        policyadmin: 'no',
        servicing: 'no',
        mimoadmin: 'no',
        sysadmin: 'no'
      });
    } else if (mode === 'edit' && user) {
      setFormData({
        username: user.username,
        email: user.email,
        password: '', // Don't populate password for security
        status: user.status,
        viewonly: user.viewonly || 'no',
        policyadmin: user.policyadmin || 'no',
        servicing: user.servicing || 'no',
        mimoadmin: user.mimoadmin || 'no',
        sysadmin: user.sysadmin || 'no'
      });
    }
    
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedUser(null);
    setError('');
    setSuccess('');
  };

  const openPasswordModal = (user) => {
    setSelectedUser(user);
    setPasswordData({ new_password: '' });
    setShowPasswordModal(true);
  };

  const closePasswordModal = () => {
    setShowPasswordModal(false);
    setSelectedUser(null);
    setPasswordData({ new_password: '' });
    setError('');
    setSuccess('');
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      if (modalMode === 'create') {
        await apiService.api.post('/users/', formData);
        setSuccess('User created successfully');
      } else if (modalMode === 'edit') {
        await apiService.api.put(`/users/${selectedUser.id}`, formData);
        setSuccess('User updated successfully');
      }
      
      fetchUsers();
      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (err) {
      setError(err.response?.data?.detail || 'Operation failed');
    }
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      await apiService.api.put(`/users/${selectedUser.id}/password`, passwordData);
      setSuccess('Password updated successfully');
      setTimeout(() => {
        closePasswordModal();
      }, 1500);
    } catch (err) {
      setError(err.response?.data?.detail || 'Password change failed');
    }
  };

  const handleDelete = async (user) => {
    if (window.confirm(`Are you sure you want to delete user "${user.username}"?`)) {
      try {
        await apiService.api.delete(`/users/${user.id}`);
        setSuccess('User deleted successfully');
        fetchUsers();
        setTimeout(() => setSuccess(''), 3000);
      } catch (err) {
        setError(err.response?.data?.detail || 'Delete failed');
        setTimeout(() => setError(''), 3000);
      }
    }
  };

  const getRoleBadges = (user) => {
    const roles = [];
    if (user.sysadmin === 'yes') roles.push('System Admin');
    if (user.mimoadmin === 'yes') roles.push('MIMO Admin');
    if (user.policyadmin === 'yes') roles.push('Policy Admin');
    if (user.servicing === 'yes') roles.push('Servicing');
    if (user.viewonly === 'yes') roles.push('View Only');
    
    return roles.map((role, index) => (
      <Badge key={index} bg="secondary" className="me-1 mb-1">
        {role}
      </Badge>
    ));
  };

  const getStatusBadge = (status) => {
    const variant = status === 'active' ? 'success' : 'danger';
    return <Badge bg={variant}>{status}</Badge>;
  };

  // Check if current user has admin permissions
  const isAdmin = currentUser && (
    currentUser.sysadmin === 'yes' || 
    currentUser.mimoadmin === 'yes'
  );

  if (!isAdmin) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          <h4>Access Denied</h4>
          <p>You don't have permission to access user management. Admin privileges required.</p>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="mt-4">
      <Row>
        <Col>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h4 className="mb-0">User Management</h4>
              <Button variant="primary" onClick={() => openModal('create')}>
                <FaPlus className="me-2" />
                Add User
              </Button>
            </Card.Header>
            <Card.Body>
              {/* Search and Filter Section */}
              <Form onSubmit={handleSearch} className="mb-4">
                <Row>
                  <Col md={4}>
                    <InputGroup>
                      <Form.Control
                        type="text"
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <Button variant="outline-secondary" type="submit">
                        <FaSearch />
                      </Button>
                    </InputGroup>
                  </Col>
                  <Col md={2}>
                    <Form.Select
                      value={searchFilters.status}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, status: e.target.value }))}
                    >
                      <option value="">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </Form.Select>
                  </Col>
                  <Col md={2}>
                    <Form.Select
                      value={searchFilters.role}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, role: e.target.value }))}
                    >
                      <option value="">All Roles</option>
                      <option value="sysadmin">System Admin</option>
                      <option value="mimoadmin">MIMO Admin</option>
                      <option value="policyadmin">Policy Admin</option>
                      <option value="servicing">Servicing</option>
                    </Form.Select>
                  </Col>
                  <Col md={2}>
                    <Button variant="secondary" onClick={clearSearch} className="w-100">
                      Clear
                    </Button>
                  </Col>
                </Row>
              </Form>

              {/* Alerts */}
              {error && <Alert variant="danger">{error}</Alert>}
              {success && <Alert variant="success">{success}</Alert>}

              {/* Users Table */}
              {loading ? (
                <div className="text-center">
                  <Spinner animation="border" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </Spinner>
                </div>
              ) : (
                <Table responsive striped hover>
                  <thead>
                    <tr>
                      <th>Username</th>
                      <th>Email</th>
                      <th>Status</th>
                      <th>Roles</th>
                      <th>Created By</th>
                      <th>Date Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.length === 0 ? (
                      <tr>
                        <td colSpan="7" className="text-center">No users found</td>
                      </tr>
                    ) : (
                      users.map((user) => (
                        <tr key={user.id}>
                          <td><strong>{user.username}</strong></td>
                          <td>{user.email}</td>
                          <td>{getStatusBadge(user.status)}</td>
                          <td>{getRoleBadges(user)}</td>
                          <td>{user.createdby}</td>
                          <td>{user.datecreated}</td>
                          <td>
                            <Button
                              variant="outline-info"
                              size="sm"
                              className="me-1"
                              onClick={() => openModal('view', user)}
                            >
                              <FaEye />
                            </Button>
                            <Button
                              variant="outline-primary"
                              size="sm"
                              className="me-1"
                              onClick={() => openModal('edit', user)}
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              variant="outline-warning"
                              size="sm"
                              className="me-1"
                              onClick={() => openPasswordModal(user)}
                            >
                              <FaKey />
                            </Button>
                            {user.username !== currentUser?.username && (
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleDelete(user)}
                              >
                                <FaTrash />
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* User Modal */}
      <Modal show={showModal} onHide={closeModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {modalMode === 'create' && 'Create New User'}
            {modalMode === 'edit' && 'Edit User'}
            {modalMode === 'view' && 'View User Details'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          
          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Username</Form.Label>
                  <Form.Control
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    required
                    disabled={modalMode === 'view'}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={modalMode === 'view'}
                  />
                </Form.Group>
              </Col>
            </Row>

            {modalMode === 'create' && (
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
            )}

            {modalMode !== 'create' && (
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      disabled={modalMode === 'view'}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
            )}

            <h6>Permissions</h6>
            <Row>
              <Col md={6}>
                <Form.Check
                  type="checkbox"
                  label="View Only"
                  checked={formData.viewonly === 'yes'}
                  onChange={(e) => setFormData(prev => ({ ...prev, viewonly: e.target.checked ? 'yes' : 'no' }))}
                  disabled={modalMode === 'view'}
                />
                <Form.Check
                  type="checkbox"
                  label="Policy Admin"
                  checked={formData.policyadmin === 'yes'}
                  onChange={(e) => setFormData(prev => ({ ...prev, policyadmin: e.target.checked ? 'yes' : 'no' }))}
                  disabled={modalMode === 'view'}
                />
                <Form.Check
                  type="checkbox"
                  label="Servicing"
                  checked={formData.servicing === 'yes'}
                  onChange={(e) => setFormData(prev => ({ ...prev, servicing: e.target.checked ? 'yes' : 'no' }))}
                  disabled={modalMode === 'view'}
                />
              </Col>
              <Col md={6}>
                <Form.Check
                  type="checkbox"
                  label="MIMO Admin"
                  checked={formData.mimoadmin === 'yes'}
                  onChange={(e) => setFormData(prev => ({ ...prev, mimoadmin: e.target.checked ? 'yes' : 'no' }))}
                  disabled={modalMode === 'view'}
                />
                <Form.Check
                  type="checkbox"
                  label="System Admin"
                  checked={formData.sysadmin === 'yes'}
                  onChange={(e) => setFormData(prev => ({ ...prev, sysadmin: e.target.checked ? 'yes' : 'no' }))}
                  disabled={modalMode === 'view'}
                />
              </Col>
            </Row>

            {modalMode !== 'view' && (
              <div className="mt-3">
                <Button type="submit" variant="primary" className="me-2">
                  {modalMode === 'create' ? 'Create User' : 'Update User'}
                </Button>
                <Button variant="secondary" onClick={closeModal}>
                  Cancel
                </Button>
              </div>
            )}
          </Form>
        </Modal.Body>
      </Modal>

      {/* Password Change Modal */}
      <Modal show={showPasswordModal} onHide={closePasswordModal}>
        <Modal.Header closeButton>
          <Modal.Title>Change Password for {selectedUser?.username}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          
          <Form onSubmit={handlePasswordChange}>
            <Form.Group className="mb-3">
              <Form.Label>New Password</Form.Label>
              <Form.Control
                type="password"
                value={passwordData.new_password}
                onChange={(e) => setPasswordData({ new_password: e.target.value })}
                required
                minLength={6}
              />
              <Form.Text className="text-muted">
                Password must be at least 6 characters long.
              </Form.Text>
            </Form.Group>

            <Button type="submit" variant="primary" className="me-2">
              Change Password
            </Button>
            <Button variant="secondary" onClick={closePasswordModal}>
              Cancel
            </Button>
          </Form>
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default UserManagement;
