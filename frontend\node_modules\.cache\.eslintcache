[{"C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\index.js": "1", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\App.js": "2", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Auth\\Login.js": "4", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Layout\\Navbar.js": "5", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyList.js": "6", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Reports\\Reports.js": "7", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Layout\\Sidebar.js": "8", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Dashboard\\Dashboard.js": "9", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Search\\Search.js": "10", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyDetails.js": "11", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyForm.js": "12", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\services\\apiService.js": "13", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Users\\UserManagement.js": "14", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicySteps.js": "15", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Import\\ImportData.js": "16", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicySearch.js": "17", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\NewPolicyCapture.js": "18", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\ComprehensivePolicyDetails.js": "19", "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyEdit.js": "20"}, {"size": 301, "mtime": 1753371532510, "results": "21", "hashOfConfig": "22"}, {"size": 5861, "mtime": 1754402412548, "results": "23", "hashOfConfig": "22"}, {"size": 3037, "mtime": 1753371583122, "results": "24", "hashOfConfig": "22"}, {"size": 4302, "mtime": 1753371664474, "results": "25", "hashOfConfig": "22"}, {"size": 2424, "mtime": 1753371627300, "results": "26", "hashOfConfig": "22"}, {"size": 9837, "mtime": 1754428843581, "results": "27", "hashOfConfig": "22"}, {"size": 17101, "mtime": 1754428918076, "results": "28", "hashOfConfig": "22"}, {"size": 4416, "mtime": 1754404177474, "results": "29", "hashOfConfig": "22"}, {"size": 9238, "mtime": 1754428881867, "results": "30", "hashOfConfig": "22"}, {"size": 10546, "mtime": 1754428772420, "results": "31", "hashOfConfig": "22"}, {"size": 11893, "mtime": 1754428640204, "results": "32", "hashOfConfig": "22"}, {"size": 30073, "mtime": 1754252052942, "results": "33", "hashOfConfig": "22"}, {"size": 5259, "mtime": 1754297471451, "results": "34", "hashOfConfig": "22"}, {"size": 19539, "mtime": 1753377897390, "results": "35", "hashOfConfig": "22"}, {"size": 70933, "mtime": 1754257771369, "results": "36", "hashOfConfig": "22"}, {"size": 32649, "mtime": 1754417437373, "results": "37", "hashOfConfig": "22"}, {"size": 14511, "mtime": 1754428725961, "results": "38", "hashOfConfig": "22"}, {"size": 24283, "mtime": 1754391747690, "results": "39", "hashOfConfig": "22"}, {"size": 41488, "mtime": 1754414775784, "results": "40", "hashOfConfig": "22"}, {"size": 22843, "mtime": 1754406153193, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1oneuxx", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\index.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\App.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Auth\\Login.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Layout\\Navbar.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyList.js", ["102"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Reports\\Reports.js", ["103"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Layout\\Sidebar.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Dashboard\\Dashboard.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Search\\Search.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyDetails.js", ["104", "105", "106", "107", "108"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyForm.js", ["109", "110"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\services\\apiService.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Users\\UserManagement.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicySteps.js", [], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Import\\ImportData.js", ["111"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicySearch.js", ["112"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\NewPolicyCapture.js", ["113"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\ComprehensivePolicyDetails.js", ["114"], [], "C:\\xampp\\htdocs\\mthunzibackend\\frontend\\src\\components\\Policy\\PolicyEdit.js", ["115", "116", "117", "118", "119", "120"], [], {"ruleId": "121", "severity": 1, "message": "122", "line": 23, "column": 6, "nodeType": "123", "endLine": 23, "endColumn": 68, "suggestions": "124"}, {"ruleId": "121", "severity": 1, "message": "125", "line": 35, "column": 6, "nodeType": "123", "endLine": 35, "endColumn": 8, "suggestions": "126"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 9, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 9, "endColumn": 23}, {"ruleId": "127", "severity": 1, "message": "131", "line": 9, "column": 25, "nodeType": "129", "messageId": "130", "endLine": 9, "endColumn": 41}, {"ruleId": "127", "severity": 1, "message": "132", "line": 10, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 10, "endColumn": 23}, {"ruleId": "127", "severity": 1, "message": "133", "line": 10, "column": 25, "nodeType": "129", "messageId": "130", "endLine": 10, "endColumn": 41}, {"ruleId": "121", "severity": 1, "message": "134", "line": 18, "column": 6, "nodeType": "123", "endLine": 18, "endColumn": 10, "suggestions": "135"}, {"ruleId": "121", "severity": 1, "message": "136", "line": 199, "column": 6, "nodeType": "123", "endLine": 199, "endColumn": 18, "suggestions": "137"}, {"ruleId": "121", "severity": 1, "message": "138", "line": 342, "column": 6, "nodeType": "123", "endLine": 342, "endColumn": 44, "suggestions": "139"}, {"ruleId": "127", "severity": 1, "message": "140", "line": 15, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 15, "endColumn": 29}, {"ruleId": "121", "severity": 1, "message": "141", "line": 37, "column": 6, "nodeType": "123", "endLine": 37, "endColumn": 20, "suggestions": "142"}, {"ruleId": "127", "severity": 1, "message": "143", "line": 88, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 88, "endColumn": 26}, {"ruleId": "121", "severity": 1, "message": "134", "line": 16, "column": 6, "nodeType": "123", "endLine": 16, "endColumn": 10, "suggestions": "144"}, {"ruleId": "127", "severity": 1, "message": "145", "line": 2, "column": 37, "nodeType": "129", "messageId": "130", "endLine": 2, "endColumn": 41}, {"ruleId": "127", "severity": 1, "message": "146", "line": 4, "column": 66, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 87}, {"ruleId": "121", "severity": 1, "message": "147", "line": 199, "column": 6, "nodeType": "123", "endLine": 199, "endColumn": 10, "suggestions": "148"}, {"ruleId": "127", "severity": 1, "message": "149", "line": 202, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 202, "endColumn": 27}, {"ruleId": "127", "severity": 1, "message": "150", "line": 413, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 413, "endColumn": 39}, {"ruleId": "127", "severity": 1, "message": "151", "line": 505, "column": 13, "nodeType": "129", "messageId": "130", "endLine": 505, "endColumn": 21}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterAndSortPolicies'. Either include it or remove the dependency array.", "ArrayExpression", ["152"], "React Hook useEffect has a missing dependency: 'generateReport'. Either include it or remove the dependency array.", ["153"], "no-unused-vars", "'policyMandate' is assigned a value but never used.", "Identifier", "unusedVar", "'setPolicyMandate' is assigned a value but never used.", "'policySummary' is assigned a value but never used.", "'setPolicySummary' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPolicyDetails'. Either include it or remove the dependency array.", ["154"], "React Hook useEffect has a missing dependency: 'fetchPolicy'. Either include it or remove the dependency array.", ["155"], "React Hook useEffect has a missing dependency: 'copyPolicyHolderToPremiumPayer'. Either include it or remove the dependency array.", ["156"], "'downloadingTemplate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'searchPolicies'. Either include it or remove the dependency array.", ["157"], "'memberTypeOptions' is assigned a value but never used.", ["158"], "'Form' is defined but never used.", "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPolicyData'. Either include it or remove the dependency array.", ["159"], "'handlePolicyChange' is assigned a value but never used.", "'copyPolicyHolderToPremiumPayer' is assigned a value but never used.", "'response' is assigned a value but never used.", {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, {"desc": "164", "fix": "172"}, {"desc": "173", "fix": "174"}, "Update the dependencies array to be: [policies, searchTerm, statusFilter, sortField, sortDirection, filterAndSortPolicies]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [generateReport]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [fetchPolicyDetails, id]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [fetchPolicy, id, isEdit]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [sameAsPolicyHolder, policyHolderData, copyPolicyHolderToPremiumPayer]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [filters.page, searchPolicies]", {"range": "185", "text": "186"}, {"range": "187", "text": "180"}, "Update the dependencies array to be: [fetchPolicyData, id]", {"range": "188", "text": "189"}, [871, 933], "[policies, searchTerm, statusFilter, sortField, sortDirection, filterAndSortPolicies]", [1062, 1064], "[generateReport]", [723, 727], "[fetchPolicyDetails, id]", [5484, 5496], "[fetchPolicy, id, isEdit]", [9157, 9195], "[sameAsPolicyHolder, policyHolderData, copyPolicyHolderToPremiumPayer]", [1331, 1345], "[filters.page, searchPolicies]", [622, 626], [5599, 5603], "[fetchPolicyData, id]"]