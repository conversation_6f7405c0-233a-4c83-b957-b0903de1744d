#!/usr/bin/env python3
"""
Force reset database script - drops everything and recreates
"""

from db import engine
from sqlalchemy import text

def force_cleanup():
    """Force cleanup of all database objects"""
    try:
        with engine.connect() as conn:
            # Drop all indexes first
            print("Dropping all indexes...")
            result = conn.execute(text("""
                SELECT 'DROP INDEX IF EXISTS ' || indexname || ' CASCADE;' as drop_cmd
                FROM pg_indexes 
                WHERE schemaname = 'public' 
                AND indexname NOT LIKE 'pg_%'
            """))
            
            for row in result:
                try:
                    conn.execute(text(row[0]))
                    print(f"Executed: {row[0]}")
                except Exception as e:
                    print(f"Error: {e}")
            
            # Drop all tables
            print("\nDropping all tables...")
            result = conn.execute(text("""
                SELECT 'DROP TABLE IF EXISTS ' || tablename || ' CASCADE;' as drop_cmd
                FROM pg_tables 
                WHERE schemaname = 'public'
            """))
            
            for row in result:
                try:
                    conn.execute(text(row[0]))
                    print(f"Executed: {row[0]}")
                except Exception as e:
                    print(f"Error: {e}")
            
            conn.commit()
            print("\nForce cleanup completed")
            
    except Exception as e:
        print(f"Error in force cleanup: {e}")

def create_tables_simple():
    """Create all necessary tables"""
    try:
        from models import Base

        # Use SQLAlchemy to create all tables
        Base.metadata.create_all(bind=engine, checkfirst=True)
        print("All tables created successfully using SQLAlchemy")
        return True

    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

if __name__ == "__main__":
    print("Force resetting database...")
    print("=" * 50)
    
    force_cleanup()
    
    print("\nCreating basic tables...")
    if create_tables_simple():
        print("\n✅ Database force reset completed!")
    else:
        print("\n❌ Database force reset failed!")
