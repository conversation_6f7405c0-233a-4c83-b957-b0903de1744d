"""Update policy bus to boolean and add policypayer field

Revision ID: ecf2b0c50255
Revises: b4871751ed3c
Create Date: 2025-08-03 22:08:04.947465

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ecf2b0c50255'
down_revision: Union[str, Sequence[str], None] = 'b4871751ed3c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('policy', sa.Column('policypayer', sa.String(), nullable=True))
    op.drop_index(op.f('ix_policy_bus'), table_name='policy')
    # Convert VARCHAR to Boolean with proper casting
    op.execute("ALTER TABLE policy ALTER COLUMN bus TYPE BOOLEAN USING CASE WHEN bus = 'true' OR bus = '1' THEN TRUE ELSE FALSE END")
    op.create_index(op.f('ix_policy_policypayer'), 'policy', ['policypayer'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_policy_policypayer'), table_name='policy')
    op.create_index(op.f('ix_policy_bus'), 'policy', ['bus'], unique=False)
    op.alter_column('policy', 'bus',
               existing_type=sa.Boolean(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.drop_column('policy', 'policypayer')
    # ### end Alembic commands ###
