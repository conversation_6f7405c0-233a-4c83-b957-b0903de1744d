from fastapi import FastAP<PERSON>, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import <PERSON>A<PERSON>2P<PERSON><PERSON><PERSON>ear<PERSON>, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/policies",
    tags=['Policies'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# get all policies
@router.get("/", response_model=List[schema.Policy])
def get_policies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policies = db.query(models.Policy).offset(skip).limit(limit).all()
    if not policies:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policies found")
    return policies

# get policy
@router.get("/{policy_id}", response_model=schema.Policy)
def get_policy(policy_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policy = db.query(models.Policy).filter(models.Policy.id == policy_id).first()
    if not policy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    return policy

# search policy by status with pagination
@router.get("/search/status/{policy_status}", response_model=List[schema.Policy])
def search_policy_by_status(policy_status: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policies = db.query(models.Policy).filter(func.lower(models.Policy.status) == func.lower(policy_status)).offset(skip).limit(limit).all()
    if not policies:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policies found with the specified status")
    return policies


# @router.get("/search/status/{policy_status}", response_model=List[schema.Policy])
# def search_policy_by_status(policy_status: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
#     policies = db.query(models.Policy).filter(func.lower(models.Policy.status) == func.lower(policy_status)).all()
#     if not policies:
#         raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policies found with the specified status")
#     return policies

# search policy by policy number with pagination
@router.get("/search/{policy_no}", response_model=List[schema.Policy])
def search_policy(policy_no: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policies = db.query(models.Policy).filter(models.Policy.policyno == policy_no).offset(skip).limit(limit).all()
    if not policies:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    return policies

# search policy by datecreated with pagination
@router.get("/search/datecreated/{date_created}", response_model=List[schema.Policy])
def search_policy_by_datecreated(date_created: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policies = db.query(models.Policy).filter(models.Policy.datecreated == date_created).offset(skip).limit(limit).all()
    if not policies:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policies found with the specified date created")
    return policies


# @router.get("/search/{policy_no}", response_model=List[schema.Policy])
# def search_policy(policy_no: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
#     policies = db.query(models.Policy).filter(models.Policy.policyno == policy_no).all()
#     if not policies:
#         raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
#     return policies

# create policy
@router.post("/", response_model=schema.Policy, status_code=status.HTTP_201_CREATED)
def create_policy(policy: schema.Policy, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    # Check if the policy number already exists
    existing_policy = db.query(models.Policy).filter(models.Policy.policyno == policy.policyno).first()
    if existing_policy:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Policy number already exists")
    
    # Create the policy
    policy_data = models.Policy(**policy.model_dump(), createdby=current_user['id'])
    db.add(policy_data)
    db.commit()
    db.refresh(policy_data)
    return policy_data

# update policy
@router.put("/{policy_id}", response_model=schema.Policy)
def update_policy(policy_id: int, policy: schema.Policy, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policy = db.query(models.Policy).filter(models.Policy.id == policy_id).first()
    if not existing_policy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    
    # Update the policy
    for key, value in policy.model_dump().items():
        setattr(existing_policy, key, value)
    db.commit()
    db.refresh(existing_policy)
    return existing_policy

# delete policy
@router.delete("/{policy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_policy(policy_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policy = db.query(models.Policy).filter(models.Policy.id == policy_id).first()
    if not existing_policy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policy not found")
    
    db.delete(existing_policy)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)