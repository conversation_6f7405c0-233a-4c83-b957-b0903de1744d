{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\mthunzibackend\\\\frontend\\\\src\\\\components\\\\Dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Alert, Spinner } from 'react-bootstrap';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\nimport apiService from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\nconst Dashboard = () => {\n  _s();\n  const [metrics, setMetrics] = useState({\n    totalPolicies: 0,\n    activePolicies: 0,\n    totalPremiums: 0,\n    pendingPolicies: 0\n  });\n  const [recentPolicies, setRecentPolicies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch policies data\n      const policiesResponse = await apiService.getPolicies(0, 100);\n      const policies = policiesResponse.data;\n\n      // Fetch policy summaries for premium data\n      const summariesResponse = await apiService.getPolicySummaries(0, 100);\n      const summaries = summariesResponse.data;\n\n      // Calculate metrics\n      const totalPolicies = policies.length;\n      const activePolicies = policies.filter(p => p.status === 'Active').length;\n      const pendingPolicies = policies.filter(p => p.status === 'Pending').length;\n\n      // Calculate total premiums from summaries\n      const totalPremiums = summaries.reduce((sum, summary) => {\n        return sum + (parseFloat(summary.totalpremiumspaid) || 0);\n      }, 0);\n      setMetrics({\n        totalPolicies,\n        activePolicies,\n        totalPremiums,\n        pendingPolicies\n      });\n\n      // Get recent policies (last 5)\n      setRecentPolicies(policies.slice(0, 5));\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const barChartData = {\n    labels: ['Active', 'Pending', 'Expired', 'Cancelled'],\n    datasets: [{\n      label: 'Policies',\n      data: [metrics.activePolicies, metrics.pendingPolicies, 15, 8],\n      backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#6c757d'],\n      borderColor: ['#1e7e34', '#e0a800', '#c82333', '#5a6268'],\n      borderWidth: 1\n    }]\n  };\n  const doughnutChartData = {\n    labels: ['Life Insurance', 'Health Insurance', 'Property Insurance', 'Auto Insurance'],\n    datasets: [{\n      data: [45, 25, 20, 10],\n      backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#dc3545'],\n      borderColor: '#fff',\n      borderWidth: 2\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      title: {\n        display: true,\n        text: 'Policy Statistics'\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        variant: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"h3 mb-0 text-gray-800\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Welcome to your policy management dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-exclamation-triangle me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xl: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                  children: \"Total Policies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h5 mb-0 font-weight-bold\",\n                  children: metrics.totalPolicies.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: \"auto\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-contract fa-2x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xl: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                  children: \"Active Policies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h5 mb-0 font-weight-bold\",\n                  children: metrics.activePolicies.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: \"auto\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-check-circle fa-2x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xl: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                  children: \"Total Premiums\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h5 mb-0 font-weight-bold\",\n                  children: [\"$\", metrics.totalPremiums.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: \"auto\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-dollar-sign fa-2x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xl: 3,\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                  children: \"Pending Policies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h5 mb-0 font-weight-bold\",\n                  children: metrics.pendingPolicies.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: \"auto\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-clock fa-2x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"py-3 d-flex flex-row align-items-center justify-content-between\",\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"m-0 font-weight-bold text-primary\",\n              children: \"Policy Status Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Bar, {\n              data: barChartData,\n              options: chartOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"py-3 d-flex flex-row align-items-center justify-content-between\",\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"m-0 font-weight-bold text-primary\",\n              children: \"Policy Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Doughnut, {\n              data: doughnutChartData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"py-3 d-flex flex-row align-items-center justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"m-0 font-weight-bold text-primary\",\n              children: \"Recent Policies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/policies\",\n              className: \"btn btn-primary btn-sm\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              striped: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Policy No\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Agent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Package\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Applicant Signed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date Created\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentPolicies.map(policy => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: policy.policyno\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: policy.agentname\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: policy.packages\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `badge ${policy.status === 'Active' ? 'bg-success' : policy.status === 'Pending' ? 'bg-warning' : 'bg-secondary'}`,\n                      children: policy.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: policy.datecreated\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this)]\n                }, policy.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"3Z0IRMn+f2+VuGBljoOpXUDdpZY=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Spinner", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Bar", "Doughnut", "apiService", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "metrics", "setMetrics", "totalPolicies", "activePolicies", "totalPremiums", "pendingPolicies", "recentPolicies", "setRecentPolicies", "loading", "setLoading", "error", "setError", "fetchDashboardData", "policiesResponse", "getPolicies", "policies", "data", "summariesResponse", "getPolicySummaries", "summaries", "length", "filter", "p", "status", "reduce", "sum", "summary", "parseFloat", "totalpremiumspaid", "slice", "err", "console", "barChartData", "labels", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "doughnutChartData", "chartOptions", "responsive", "plugins", "legend", "position", "title", "display", "text", "className", "style", "height", "children", "animation", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fluid", "xl", "md", "Body", "toLocaleString", "xs", "lg", "Header", "options", "href", "striped", "map", "policy", "policyno", "agentname", "packages", "datecreated", "id", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/mthunzibackend/frontend/src/components/Dashboard/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Alert, Spinner } from 'react-bootstrap';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { <PERSON>, Doughnut } from 'react-chartjs-2';\nimport apiService from '../../services/apiService';\n\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);\n\nconst Dashboard = () => {\n  const [metrics, setMetrics] = useState({\n    totalPolicies: 0,\n    activePolicies: 0,\n    totalPremiums: 0,\n    pendingPolicies: 0\n  });\n  const [recentPolicies, setRecentPolicies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch policies data\n      const policiesResponse = await apiService.getPolicies(0, 100);\n      const policies = policiesResponse.data;\n      \n      // Fetch policy summaries for premium data\n      const summariesResponse = await apiService.getPolicySummaries(0, 100);\n      const summaries = summariesResponse.data;\n      \n      // Calculate metrics\n      const totalPolicies = policies.length;\n      const activePolicies = policies.filter(p => p.status === 'Active').length;\n      const pendingPolicies = policies.filter(p => p.status === 'Pending').length;\n      \n      // Calculate total premiums from summaries\n      const totalPremiums = summaries.reduce((sum, summary) => {\n        return sum + (parseFloat(summary.totalpremiumspaid) || 0);\n      }, 0);\n      \n      setMetrics({\n        totalPolicies,\n        activePolicies,\n        totalPremiums,\n        pendingPolicies\n      });\n      \n      // Get recent policies (last 5)\n      setRecentPolicies(policies.slice(0, 5));\n      \n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const barChartData = {\n    labels: ['Active', 'Pending', 'Expired', 'Cancelled'],\n    datasets: [\n      {\n        label: 'Policies',\n        data: [metrics.activePolicies, metrics.pendingPolicies, 15, 8],\n        backgroundColor: [\n          '#28a745',\n          '#ffc107',\n          '#dc3545',\n          '#6c757d'\n        ],\n        borderColor: [\n          '#1e7e34',\n          '#e0a800',\n          '#c82333',\n          '#5a6268'\n        ],\n        borderWidth: 1\n      }\n    ]\n  };\n\n  const doughnutChartData = {\n    labels: ['Life Insurance', 'Health Insurance', 'Property Insurance', 'Auto Insurance'],\n    datasets: [\n      {\n        data: [45, 25, 20, 10],\n        backgroundColor: [\n          '#28a745',\n          '#17a2b8',\n          '#ffc107',\n          '#dc3545'\n        ],\n        borderColor: '#fff',\n        borderWidth: 2\n      }\n    ]\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n      title: {\n        display: true,\n        text: 'Policy Statistics'\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\n        <Spinner animation=\"border\" variant=\"primary\" />\n      </div>\n    );\n  }\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h1 className=\"h3 mb-0 text-gray-800\">Dashboard</h1>\n          <p className=\"text-muted\">Welcome to your policy management dashboard</p>\n        </Col>\n      </Row>\n\n      {error && (\n        <Alert variant=\"danger\" className=\"mb-4\">\n          <i className=\"fas fa-exclamation-triangle me-2\"></i>\n          {error}\n        </Alert>\n      )}\n\n      {/* Metrics Cards */}\n      <Row className=\"mb-4\">\n        <Col xl={3} md={6} className=\"mb-4\">\n          <Card className=\"metric-card h-100\">\n            <Card.Body>\n              <Row>\n                <Col>\n                  <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                    Total Policies\n                  </div>\n                  <div className=\"h5 mb-0 font-weight-bold\">\n                    {metrics.totalPolicies.toLocaleString()}\n                  </div>\n                </Col>\n                <Col xs=\"auto\">\n                  <i className=\"fas fa-file-contract fa-2x\"></i>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col xl={3} md={6} className=\"mb-4\">\n          <Card className=\"metric-card h-100\">\n            <Card.Body>\n              <Row>\n                <Col>\n                  <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                    Active Policies\n                  </div>\n                  <div className=\"h5 mb-0 font-weight-bold\">\n                    {metrics.activePolicies.toLocaleString()}\n                  </div>\n                </Col>\n                <Col xs=\"auto\">\n                  <i className=\"fas fa-check-circle fa-2x\"></i>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col xl={3} md={6} className=\"mb-4\">\n          <Card className=\"metric-card h-100\">\n            <Card.Body>\n              <Row>\n                <Col>\n                  <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                    Total Premiums\n                  </div>\n                  <div className=\"h5 mb-0 font-weight-bold\">\n                    ${metrics.totalPremiums.toLocaleString()}\n                  </div>\n                </Col>\n                <Col xs=\"auto\">\n                  <i className=\"fas fa-dollar-sign fa-2x\"></i>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col xl={3} md={6} className=\"mb-4\">\n          <Card className=\"metric-card h-100\">\n            <Card.Body>\n              <Row>\n                <Col>\n                  <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                    Pending Policies\n                  </div>\n                  <div className=\"h5 mb-0 font-weight-bold\">\n                    {metrics.pendingPolicies.toLocaleString()}\n                  </div>\n                </Col>\n                <Col xs=\"auto\">\n                  <i className=\"fas fa-clock fa-2x\"></i>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Charts Row */}\n      <Row className=\"mb-4\">\n        <Col lg={8}>\n          <Card className=\"shadow mb-4\">\n            <Card.Header className=\"py-3 d-flex flex-row align-items-center justify-content-between\">\n              <h6 className=\"m-0 font-weight-bold text-primary\">Policy Status Overview</h6>\n            </Card.Header>\n            <Card.Body>\n              <Bar data={barChartData} options={chartOptions} />\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col lg={4}>\n          <Card className=\"shadow mb-4\">\n            <Card.Header className=\"py-3 d-flex flex-row align-items-center justify-content-between\">\n              <h6 className=\"m-0 font-weight-bold text-primary\">Policy Types</h6>\n            </Card.Header>\n            <Card.Body>\n              <Doughnut data={doughnutChartData} />\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Recent Policies Table */}\n      <Row>\n        <Col>\n          <Card className=\"shadow mb-4\">\n            <Card.Header className=\"py-3 d-flex flex-row align-items-center justify-content-between\">\n              <h6 className=\"m-0 font-weight-bold text-primary\">Recent Policies</h6>\n              <a href=\"/policies\" className=\"btn btn-primary btn-sm\">\n                View All\n              </a>\n            </Card.Header>\n            <Card.Body>\n              <Table responsive striped>\n                <thead>\n                  <tr>\n                    <th>Policy No</th>\n                    <th>Agent</th>\n                    <th>Package</th>\n                    <th>Status</th>\n                    <th>Applicant Signed</th>\n                    <th>Date Created</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {recentPolicies.map((policy) => (\n                    <tr key={policy.id}>\n                      <td>{policy.policyno}</td>\n                      <td>{policy.agentname}</td>\n                      <td>{policy.packages}</td>\n                      <td>\n                        <span className={`badge ${\n                          policy.status === 'Active' ? 'bg-success' : \n                          policy.status === 'Pending' ? 'bg-warning' : 'bg-secondary'\n                        }`}>\n                          {policy.status}\n                        </span>\n                      </td>\n                      <td>{policy.datecreated}</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAClF,SAASC,KAAK,IAAIC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,UAAU;AACvH,SAASC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC/C,OAAOC,UAAU,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnDZ,OAAO,CAACa,QAAQ,CAACZ,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,CAAC;AAE5F,MAAMO,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC;IACrC4B,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdqC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMI,gBAAgB,GAAG,MAAMnB,UAAU,CAACoB,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC;MAC7D,MAAMC,QAAQ,GAAGF,gBAAgB,CAACG,IAAI;;MAEtC;MACA,MAAMC,iBAAiB,GAAG,MAAMvB,UAAU,CAACwB,kBAAkB,CAAC,CAAC,EAAE,GAAG,CAAC;MACrE,MAAMC,SAAS,GAAGF,iBAAiB,CAACD,IAAI;;MAExC;MACA,MAAMd,aAAa,GAAGa,QAAQ,CAACK,MAAM;MACrC,MAAMjB,cAAc,GAAGY,QAAQ,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;MACzE,MAAMf,eAAe,GAAGU,QAAQ,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACH,MAAM;;MAE3E;MACA,MAAMhB,aAAa,GAAGe,SAAS,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;QACvD,OAAOD,GAAG,IAAIE,UAAU,CAACD,OAAO,CAACE,iBAAiB,CAAC,IAAI,CAAC,CAAC;MAC3D,CAAC,EAAE,CAAC,CAAC;MAEL3B,UAAU,CAAC;QACTC,aAAa;QACbC,cAAc;QACdC,aAAa;QACbC;MACF,CAAC,CAAC;;MAEF;MACAE,iBAAiB,CAACQ,QAAQ,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,gCAAgC,EAAEoB,GAAG,CAAC;MACpDnB,QAAQ,CAAC,+BAA+B,CAAC;IAC3C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,YAAY,GAAG;IACnBC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;IACrDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,UAAU;MACjBnB,IAAI,EAAE,CAAChB,OAAO,CAACG,cAAc,EAAEH,OAAO,CAACK,eAAe,EAAE,EAAE,EAAE,CAAC,CAAC;MAC9D+B,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,iBAAiB,GAAG;IACxBN,MAAM,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,gBAAgB,CAAC;IACtFC,QAAQ,EAAE,CACR;MACElB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACtBoB,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAME,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;MACR;IACF;EACF,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKoD,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC3FvD,OAAA,CAACd,OAAO;QAACsE,SAAS,EAAC,QAAQ;QAACC,OAAO,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,oBACE7D,OAAA,CAACpB,SAAS;IAACkF,KAAK;IAAAP,QAAA,gBACdvD,OAAA,CAACnB,GAAG;MAACuE,SAAS,EAAC,MAAM;MAAAG,QAAA,eACnBvD,OAAA,CAAClB,GAAG;QAAAyE,QAAA,gBACFvD,OAAA;UAAIoD,SAAS,EAAC,uBAAuB;UAAAG,QAAA,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD7D,OAAA;UAAGoD,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAA2C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL/C,KAAK,iBACJd,OAAA,CAACf,KAAK;MAACwE,OAAO,EAAC,QAAQ;MAACL,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACtCvD,OAAA;QAAGoD,SAAS,EAAC;MAAkC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACnD/C,KAAK;IAAA;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD7D,OAAA,CAACnB,GAAG;MAACuE,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBvD,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACZ,SAAS,EAAC,MAAM;QAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,mBAAmB;UAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAACnB,GAAG;cAAA0E,QAAA,gBACFvD,OAAA,CAAClB,GAAG;gBAAAyE,QAAA,gBACFvD,OAAA;kBAAKoD,SAAS,EAAC,8CAA8C;kBAAAG,QAAA,EAAC;gBAE9D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7D,OAAA;kBAAKoD,SAAS,EAAC,0BAA0B;kBAAAG,QAAA,EACtCnD,OAAO,CAACE,aAAa,CAAC4D,cAAc,CAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA,CAAClB,GAAG;gBAACqF,EAAE,EAAC,MAAM;gBAAAZ,QAAA,eACZvD,OAAA;kBAAGoD,SAAS,EAAC;gBAA4B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN7D,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACZ,SAAS,EAAC,MAAM;QAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,mBAAmB;UAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAACnB,GAAG;cAAA0E,QAAA,gBACFvD,OAAA,CAAClB,GAAG;gBAAAyE,QAAA,gBACFvD,OAAA;kBAAKoD,SAAS,EAAC,8CAA8C;kBAAAG,QAAA,EAAC;gBAE9D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7D,OAAA;kBAAKoD,SAAS,EAAC,0BAA0B;kBAAAG,QAAA,EACtCnD,OAAO,CAACG,cAAc,CAAC2D,cAAc,CAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA,CAAClB,GAAG;gBAACqF,EAAE,EAAC,MAAM;gBAAAZ,QAAA,eACZvD,OAAA;kBAAGoD,SAAS,EAAC;gBAA2B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN7D,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACZ,SAAS,EAAC,MAAM;QAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,mBAAmB;UAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAACnB,GAAG;cAAA0E,QAAA,gBACFvD,OAAA,CAAClB,GAAG;gBAAAyE,QAAA,gBACFvD,OAAA;kBAAKoD,SAAS,EAAC,8CAA8C;kBAAAG,QAAA,EAAC;gBAE9D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7D,OAAA;kBAAKoD,SAAS,EAAC,0BAA0B;kBAAAG,QAAA,GAAC,GACvC,EAACnD,OAAO,CAACI,aAAa,CAAC0D,cAAc,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA,CAAClB,GAAG;gBAACqF,EAAE,EAAC,MAAM;gBAAAZ,QAAA,eACZvD,OAAA;kBAAGoD,SAAS,EAAC;gBAA0B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN7D,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACZ,SAAS,EAAC,MAAM;QAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,mBAAmB;UAAAG,QAAA,eACjCvD,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAACnB,GAAG;cAAA0E,QAAA,gBACFvD,OAAA,CAAClB,GAAG;gBAAAyE,QAAA,gBACFvD,OAAA;kBAAKoD,SAAS,EAAC,8CAA8C;kBAAAG,QAAA,EAAC;gBAE9D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7D,OAAA;kBAAKoD,SAAS,EAAC,0BAA0B;kBAAAG,QAAA,EACtCnD,OAAO,CAACK,eAAe,CAACyD,cAAc,CAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA,CAAClB,GAAG;gBAACqF,EAAE,EAAC,MAAM;gBAAAZ,QAAA,eACZvD,OAAA;kBAAGoD,SAAS,EAAC;gBAAoB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACnB,GAAG;MAACuE,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBvD,OAAA,CAAClB,GAAG;QAACsF,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC3BvD,OAAA,CAACjB,IAAI,CAACsF,MAAM;YAACjB,SAAS,EAAC,iEAAiE;YAAAG,QAAA,eACtFvD,OAAA;cAAIoD,SAAS,EAAC,mCAAmC;cAAAG,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACd7D,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAACJ,GAAG;cAACwB,IAAI,EAAEgB,YAAa;cAACkC,OAAO,EAAE1B;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN7D,OAAA,CAAClB,GAAG;QAACsF,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC3BvD,OAAA,CAACjB,IAAI,CAACsF,MAAM;YAACjB,SAAS,EAAC,iEAAiE;YAAAG,QAAA,eACtFvD,OAAA;cAAIoD,SAAS,EAAC,mCAAmC;cAAAG,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACd7D,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAACH,QAAQ;cAACuB,IAAI,EAAEuB;YAAkB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACnB,GAAG;MAAA0E,QAAA,eACFvD,OAAA,CAAClB,GAAG;QAAAyE,QAAA,eACFvD,OAAA,CAACjB,IAAI;UAACqE,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC3BvD,OAAA,CAACjB,IAAI,CAACsF,MAAM;YAACjB,SAAS,EAAC,iEAAiE;YAAAG,QAAA,gBACtFvD,OAAA;cAAIoD,SAAS,EAAC,mCAAmC;cAAAG,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE7D,OAAA;cAAGuE,IAAI,EAAC,WAAW;cAACnB,SAAS,EAAC,wBAAwB;cAAAG,QAAA,EAAC;YAEvD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACd7D,OAAA,CAACjB,IAAI,CAACkF,IAAI;YAAAV,QAAA,eACRvD,OAAA,CAAChB,KAAK;cAAC6D,UAAU;cAAC2B,OAAO;cAAAjB,QAAA,gBACvBvD,OAAA;gBAAAuD,QAAA,eACEvD,OAAA;kBAAAuD,QAAA,gBACEvD,OAAA;oBAAAuD,QAAA,EAAI;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB7D,OAAA;oBAAAuD,QAAA,EAAI;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd7D,OAAA;oBAAAuD,QAAA,EAAI;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB7D,OAAA;oBAAAuD,QAAA,EAAI;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf7D,OAAA;oBAAAuD,QAAA,EAAI;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzB7D,OAAA;oBAAAuD,QAAA,EAAI;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR7D,OAAA;gBAAAuD,QAAA,EACG7C,cAAc,CAAC+D,GAAG,CAAEC,MAAM,iBACzB1E,OAAA;kBAAAuD,QAAA,gBACEvD,OAAA;oBAAAuD,QAAA,EAAKmB,MAAM,CAACC;kBAAQ;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1B7D,OAAA;oBAAAuD,QAAA,EAAKmB,MAAM,CAACE;kBAAS;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3B7D,OAAA;oBAAAuD,QAAA,EAAKmB,MAAM,CAACG;kBAAQ;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1B7D,OAAA;oBAAAuD,QAAA,eACEvD,OAAA;sBAAMoD,SAAS,EAAE,SACfsB,MAAM,CAAC/C,MAAM,KAAK,QAAQ,GAAG,YAAY,GACzC+C,MAAM,CAAC/C,MAAM,KAAK,SAAS,GAAG,YAAY,GAAG,cAAc,EAC1D;sBAAA4B,QAAA,EACAmB,MAAM,CAAC/C;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL7D,OAAA;oBAAAuD,QAAA,EAAKmB,MAAM,CAACI;kBAAW;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAZtBa,MAAM,CAACK,EAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAad,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC1D,EAAA,CA/RID,SAAS;AAAA8E,EAAA,GAAT9E,SAAS;AAiSf,eAAeA,SAAS;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}