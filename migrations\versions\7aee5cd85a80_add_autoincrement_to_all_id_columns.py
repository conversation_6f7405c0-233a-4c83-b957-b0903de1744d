"""Add autoincrement to all ID columns

Revision ID: 7aee5cd85a80
Revises: ecf2b0c50255
Create Date: 2025-08-04 19:07:00.344027

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7aee5cd85a80'
down_revision: Union[str, Sequence[str], None] = 'ecf2b0c50255'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
