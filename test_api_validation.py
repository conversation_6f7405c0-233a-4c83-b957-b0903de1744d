#!/usr/bin/env python3

import requests
import json

def test_validation_api():
    """Test the validation API endpoint"""

    print("=== TESTING VALIDATION API ===")

    # First, let's test if the server is running
    try:
        response = requests.get("http://localhost:8000/")
        print(f"✅ Server is running. Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False

    # First, get an authentication token
    try:
        login_data = {
            "username": "admin",
            "password": "password"
        }

        print("Getting authentication token...")
        auth_response = requests.post(
            "http://localhost:8000/login",
            json=login_data
        )

        if auth_response.status_code != 200:
            print(f"❌ Authentication failed: {auth_response.status_code}")
            print(f"Response: {auth_response.text}")
            return False

        auth_result = auth_response.json()
        token = auth_result.get('access_token')

        if not token:
            print(f"❌ No token received")
            return False

        print(f"✅ Authentication successful")

    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False

    # Test the validation endpoint
    try:
        # Create test file content
        test_csv_content = """policyno,packages,bus,pep,agentcode,agentname,status
POL001,Standard,true,false,AG001,John Agent,Active
POL002,InvalidPackage,maybe,false,AG002,Jane Agent,Active
POL003,,true,invalid_bool,AG003,,Pending
POL004,Premier,false,true,,Bob Agent,Active"""
        
        # Prepare the request
        files = {
            'file': ('test.csv', test_csv_content, 'text/csv')
        }
        data = {
            'import_type': 'policy'
        }
        
        print(f"Sending validation request...")
        headers = {
            'Authorization': f'Bearer {token}'
        }

        response = requests.post(
            "http://localhost:8000/imports/validate",
            files=files,
            data=data,
            headers=headers
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Validation successful!")
            print(f"Status: {result.get('status')}")
            print(f"Total rows: {result.get('total_rows')}")
            print(f"Missing columns: {result.get('missing_columns')}")
            print(f"Validation errors: {len(result.get('validation_errors', []))}")
            
            if result.get('validation_errors'):
                print(f"\nValidation errors found:")
                for error in result['validation_errors'][:3]:  # Show first 3
                    print(f"  Row {error['row']}: {error['errors']}")
            
            return True
        else:
            print(f"❌ Validation failed with status {response.status_code}")
            print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_validation_api()
