<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Template Download</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .debug { background: #f8f9fa; border: 1px solid #dee2e6; color: #495057; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Template Download</h1>
        
        <div class="form-group">
            <label for="token">JWT Token:</label>
            <input type="text" id="token" placeholder="Get token from React app localStorage">
            <small>In React app: Open Dev Tools → Application → Local Storage → copy 'token' value</small>
        </div>
        
        <div class="form-group">
            <label for="importType">Import Type:</label>
            <select id="importType">
                <option value="policy">Policy</option>
                <option value="policyholder">Policy Holder</option>
                <option value="policymandate">Policy Mandate</option>
                <option value="beneficiaries">Beneficiaries</option>
            </select>
        </div>
        
        <button onclick="debugDownload()">Debug Download (Show Content)</button>
        <button onclick="actualDownload()">Actual Download (Save File)</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function debugDownload() {
            const token = document.getElementById('token').value;
            const importType = document.getElementById('importType').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">Please enter a JWT token</div>';
                return;
            }
            
            try {
                console.log('=== DEBUG DOWNLOAD ===');
                console.log('Import Type:', importType);
                console.log('Token:', token.substring(0, 20) + '...');
                
                const response = await fetch(`http://localhost:8000/imports/template/${importType}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                console.log('Response Status:', response.status);
                console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
                
                if (response.ok) {
                    const text = await response.text();
                    const lines = text.split('\\n').filter(line => line.trim() !== '');
                    
                    console.log('Response Text Length:', text.length);
                    console.log('Number of Lines:', lines.length);
                    console.log('Raw Text:', text);
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Template Content Retrieved</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Content Length:</strong> ${text.length} characters</p>
                            <p><strong>Number of Lines:</strong> ${lines.length}</p>
                            <p><strong>Content Type:</strong> ${response.headers.get('content-type')}</p>
                            
                            <h4>Raw Content:</h4>
                            <pre>${text}</pre>
                            
                            <h4>Lines Breakdown:</h4>
                            <pre>${lines.map((line, i) => `Line ${i + 1}: ${line}`).join('\\n')}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Download Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${errorData}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function actualDownload() {
            const token = document.getElementById('token').value;
            const importType = document.getElementById('importType').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">Please enter a JWT token</div>';
                return;
            }
            
            try {
                const response = await fetch(`http://localhost:8000/imports/template/${importType}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', `${importType}_template_debug.csv`);
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    window.URL.revokeObjectURL(url);
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ File Downloaded</h3>
                            <p><strong>File:</strong> ${importType}_template_debug.csv</p>
                            <p><strong>Size:</strong> ${blob.size} bytes</p>
                            <p>Check your Downloads folder and open the file to verify content.</p>
                        </div>
                    `;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Download Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${errorData}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
