"""Add new fields to PolicyHolder for updated import requirements

Revision ID: c86b6e9ecb27
Revises: fca72f1a1fdd
Create Date: 2025-08-04 20:09:05.105878

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c86b6e9ecb27'
down_revision: Union[str, Sequence[str], None] = 'fca72f1a1fdd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('policyholder', sa.Column('capturedate', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('idnumber', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('idtype', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('mobilenumber', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('alternativenumber', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('postalcity', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('postalcountrycode', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('town', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('residentialaddressline1', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('residentialcountrycode', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('residentialdistrict', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('residentialvillage', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('traditionalauthority', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('contractsigneddate', sa.String(), nullable=True))
    op.create_index(op.f('ix_policyholder_alternativenumber'), 'policyholder', ['alternativenumber'], unique=False)
    op.create_index(op.f('ix_policyholder_capturedate'), 'policyholder', ['capturedate'], unique=False)
    op.create_index(op.f('ix_policyholder_contractsigneddate'), 'policyholder', ['contractsigneddate'], unique=False)
    op.create_index(op.f('ix_policyholder_idnumber'), 'policyholder', ['idnumber'], unique=False)
    op.create_index(op.f('ix_policyholder_idtype'), 'policyholder', ['idtype'], unique=False)
    op.create_index(op.f('ix_policyholder_mobilenumber'), 'policyholder', ['mobilenumber'], unique=False)
    op.create_index(op.f('ix_policyholder_postalcity'), 'policyholder', ['postalcity'], unique=False)
    op.create_index(op.f('ix_policyholder_postalcountrycode'), 'policyholder', ['postalcountrycode'], unique=False)
    op.create_index(op.f('ix_policyholder_residentialaddressline1'), 'policyholder', ['residentialaddressline1'], unique=False)
    op.create_index(op.f('ix_policyholder_residentialcountrycode'), 'policyholder', ['residentialcountrycode'], unique=False)
    op.create_index(op.f('ix_policyholder_residentialdistrict'), 'policyholder', ['residentialdistrict'], unique=False)
    op.create_index(op.f('ix_policyholder_residentialvillage'), 'policyholder', ['residentialvillage'], unique=False)
    op.create_index(op.f('ix_policyholder_town'), 'policyholder', ['town'], unique=False)
    op.create_index(op.f('ix_policyholder_traditionalauthority'), 'policyholder', ['traditionalauthority'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_policyholder_traditionalauthority'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_town'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_residentialvillage'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_residentialdistrict'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_residentialcountrycode'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_residentialaddressline1'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postalcountrycode'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postalcity'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_mobilenumber'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_idtype'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_idnumber'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_contractsigneddate'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_capturedate'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_alternativenumber'), table_name='policyholder')
    op.drop_column('policyholder', 'contractsigneddate')
    op.drop_column('policyholder', 'traditionalauthority')
    op.drop_column('policyholder', 'residentialvillage')
    op.drop_column('policyholder', 'residentialdistrict')
    op.drop_column('policyholder', 'residentialcountrycode')
    op.drop_column('policyholder', 'residentialaddressline1')
    op.drop_column('policyholder', 'town')
    op.drop_column('policyholder', 'postalcountrycode')
    op.drop_column('policyholder', 'postalcity')
    op.drop_column('policyholder', 'alternativenumber')
    op.drop_column('policyholder', 'mobilenumber')
    op.drop_column('policyholder', 'idtype')
    op.drop_column('policyholder', 'idnumber')
    op.drop_column('policyholder', 'capturedate')
    # ### end Alembic commands ###
