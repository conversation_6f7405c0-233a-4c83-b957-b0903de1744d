import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { FaUser, FaCreditCard, FaFileContract, FaUsers, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import apiService from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';
import { PolicyHolderStep, PremiumPayerStep, PolicyMandateStep, BeneficiariesStep } from './PolicySteps';

const PolicyForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  const { user } = useAuth();

  // Generate policy number for new policies
  const generatePolicyNumber = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `POL${timestamp}${random}`;
  };

  const [policyNumber] = useState(() => isEdit ? '' : generatePolicyNumber());

  // Policy form data
  const [policyData, setPolicyData] = useState({
    policyno: policyNumber,
    packages: '',
    bus: false,
    policypayer: '',
    agentcode: '',
    agentname: '',
    pep: false,
    aml: false,
    applicantsigneddate: '',
    agentsigneddate: '',
    applicationform: false,
    kycform: false,
    mandateform: false,
    schemecode: '',
    worksitecode: '',
    riskprofile: '',
    status: 'Pending',
    datecreated: new Date().toISOString().split('T')[0],
    createdby: user?.id || 1
  });

  // PolicyHolder form data
  const [policyHolderData, setPolicyHolderData] = useState({
    policyno: policyNumber,
    title: '',
    initials: '',
    middlename: '',
    surname: '',
    previousname: '',
    gender: '',
    dateofbirth: '',
    countryofbirth: '',
    maritalstatus: '',
    residentialstatus: '',
    primaryid: '',
    primaryidexpirydate: '',
    secondaryid: '',
    secondaryidexpirydate: '',
    expatriate: '',
    workpermit: '',
    workpermitexpirydate: '',
    sourceofincome: '',
    netmonthlyincome: '',
    specifyincome: '',
    proofofsourceoffunds: '',
    specifyproofofsourceoffunds: '',
    occupation: '',
    industry: '',
    employer: '',
    other: '',
    commpreference: '',
    emailaddress: '',
    phoneno: '',
    altmobileno: '',
    physicaladdressline1: '',
    physicaladdressline2: '',
    physicaladdressline3: '',
    physicaladdressline4: '',
    physicaladdressline5city: '',
    physicaladdressline6country: '',
    permanentaddressvillage: '',
    permanentaddressta: '',
    permanentaddressdistrict: '',
    postaladdressline1: '',
    postaladdressline2: '',
    postaladdressline3: '',
    postaladdressline4: '',
    postaladdressline5city: '',
    postaladdressline6country: ''
  });

  // PremiumPayer form data
  const [premiumPayerData, setPremiumPayerData] = useState({
    policyno: policyNumber,
    title: '',
    initials: '',
    middlename: '',
    surname: '',
    previousname: '',
    gender: '',
    dateofbirth: '',
    countryofbirth: '',
    maritalstatus: '',
    residentialstatus: '',
    primaryid: '',
    primaryidexpirydate: '',
    secondaryid: '',
    secondaryidexpirydate: '',
    expatriate: '',
    workpermit: '',
    workpermitexpirydate: '',
    sourceofincome: '',
    netmonthlyincome: '',
    specifyincome: '',
    proofofsourceoffunds: '',
    specifyproofofsourceoffunds: '',
    occupation: '',
    industry: '',
    employer: '',
    other: '',
    commpreference: '',
    emailaddress: '',
    phoneno: '',
    altmobileno: '',
    physicaladdressline1: '',
    physicaladdressline2: '',
    physicaladdressline3: '',
    physicaladdressline4: '',
    physicaladdressline5city: '',
    physicaladdressline6country: '',
    permanentaddressvillage: '',
    permanentaddressta: '',
    permanentaddressdistrict: '',
    postaladdressline1: '',
    postaladdressline2: '',
    postaladdressline3: '',
    postaladdressline4: '',
    postaladdressline5city: '',
    postaladdressline6country: ''
  });

  // PolicyMandate form data
  const [policyMandateData, setPolicyMandateData] = useState({
    policyno: policyNumber,
    modeofpayment: '',
    frequency: '',
    premium: '',
    firstdeductiondate: '',
    debitorderFirstname: '',
    debitorderLastname: '',
    debitorderNameofbank: '',
    debitorderBranch: '',
    debitorderAccountno: '',
    debitorderTypeofaccount: '',
    debitorderSalaryfundingdate: '',
    debitorderPremiumonapplication: '',
    stoporderEmployeeid: '',
    stoporderEmployeename: '',
    stoporderEmployername: '',
    stoporderEmployeraddress: '',
    mobileoperator: '',
    mobilephoneno: ''
  });

  // Beneficiaries form data
  const [beneficiariesData, setBeneficiariesData] = useState([{
    policyid: policyNumber,
    title: '',
    initials: '',
    firstname: '',
    middlename: '',
    surname: '',
    gender: '',
    birthdate: '',
    maritalstatus: '',
    membertype: '',
    benofownership: '',
    relationship: '',
    status: 'ACTIVE',
    datecreated: new Date().toISOString().split('T')[0],
    createdby: user?.id || 1
  }]);

  const [sameAsPolicyHolder, setSameAsPolicyHolder] = useState(false);

  useEffect(() => {
    if (isEdit) {
      fetchPolicy();
    }
  }, [id, isEdit]);

  const fetchPolicy = async () => {
    try {
      setLoading(true);
      const response = await apiService.getPolicy(id);
      setPolicyData(response.data);
    } catch (err) {
      console.error('Error fetching policy:', err);
      setError('Failed to load policy data');
    } finally {
      setLoading(false);
    }
  };

  // Handle form changes for different sections
  const handlePolicyChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPolicyData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handlePolicyHolderChange = (e) => {
    const { name, value } = e.target;
    setPolicyHolderData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePremiumPayerChange = (e) => {
    const { name, value } = e.target;
    setPremiumPayerData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePolicyMandateChange = (e) => {
    const { name, value } = e.target;
    setPolicyMandateData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBeneficiaryChange = (index, e) => {
    const { name, value } = e.target;
    setBeneficiariesData(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [name]: value };
      return updated;
    });
  };

  const addBeneficiary = () => {
    setBeneficiariesData(prev => [...prev, {
      policyid: policyNumber,
      title: '',
      initials: '',
      firstname: '',
      middlename: '',
      surname: '',
      gender: '',
      birthdate: '',
      maritalstatus: '',
      membertype: '',
      benofownership: '',
      relationship: '',
      status: 'ACTIVE',
      datecreated: new Date().toISOString().split('T')[0],
      createdby: user?.id || 1
    }]);
  };

  const removeBeneficiary = (index) => {
    setBeneficiariesData(prev => prev.filter((_, i) => i !== index));
  };

  // Copy policy holder data to premium payer
  const copyPolicyHolderToPremiumPayer = () => {
    if (sameAsPolicyHolder) {
      setPremiumPayerData(prev => ({
        ...prev,
        ...policyHolderData,
        policyno: policyNumber
      }));
    } else {
      setPremiumPayerData(prev => ({
        policyno: policyNumber,
        title: '',
        initials: '',
        middlename: '',
        surname: '',
        previousname: '',
        gender: '',
        dateofbirth: '',
        countryofbirth: '',
        maritalstatus: '',
        residentialstatus: '',
        primaryid: '',
        primaryidexpirydate: '',
        secondaryid: '',
        secondaryidexpirydate: '',
        expatriate: '',
        workpermit: '',
        workpermitexpirydate: '',
        sourceofincome: '',
        netmonthlyincome: '',
        specifyincome: '',
        proofofsourceoffunds: '',
        specifyproofofsourceoffunds: '',
        occupation: '',
        industry: '',
        employer: '',
        other: '',
        commpreference: '',
        emailaddress: '',
        phoneno: '',
        altmobileno: '',
        physicaladdressline1: '',
        physicaladdressline2: '',
        physicaladdressline3: '',
        physicaladdressline4: '',
        physicaladdressline5city: '',
        physicaladdressline6country: '',
        permanentaddressvillage: '',
        permanentaddressta: '',
        permanentaddressdistrict: '',
        postaladdressline1: '',
        postaladdressline2: '',
        postaladdressline3: '',
        postaladdressline4: '',
        postaladdressline5city: '',
        postaladdressline6country: ''
      }));
    }
  };

  useEffect(() => {
    copyPolicyHolderToPremiumPayer();
  }, [sameAsPolicyHolder, policyHolderData]);

  // Step navigation
  const nextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const goToStep = (step) => {
    setCurrentStep(step);
  };

  // Validation functions
  const validateStep = (step) => {
    switch (step) {
      case 1:
        return policyData.policyno && policyData.packages && policyData.bus && policyData.agentcode && policyData.agentname;
      case 2:
        return policyHolderData.title && policyHolderData.initials && policyHolderData.surname &&
               policyHolderData.gender && policyHolderData.dateofbirth && policyHolderData.primaryid;
      case 3:
        return premiumPayerData.title && premiumPayerData.initials && premiumPayerData.surname;
      case 4:
        return policyMandateData.modeofpayment && policyMandateData.frequency && policyMandateData.premium;
      case 5:
        return beneficiariesData.length > 0 && beneficiariesData.every(b =>
          b.title && b.firstname && b.surname && b.relationship);
      default:
        return true;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      if (isEdit) {
        await apiService.updatePolicy(id, policyData);
        setSuccess('Policy updated successfully!');
      } else {
        // Create all related entities
        const policyResponse = await apiService.createPolicy(policyData);
        console.log('Policy created:', policyResponse);

        // Create PolicyHolder
        await apiService.api.post('/policyholder/', policyHolderData);
        console.log('PolicyHolder created');

        // Create PremiumPayer
        await apiService.api.post('/payer/', premiumPayerData);
        console.log('PremiumPayer created');

        // Create PolicyMandate
        await apiService.api.post('/policymandate/', policyMandateData);
        console.log('PolicyMandate created');

        // Create Beneficiaries
        for (const beneficiary of beneficiariesData) {
          await apiService.api.post('/beneficiaries/', beneficiary);
        }
        console.log('Beneficiaries created');

        setSuccess('Complete policy package created successfully!');
      }

      setTimeout(() => {
        navigate('/policies');
      }, 2000);
    } catch (err) {
      console.error('Error saving policy:', err);
      setError(err.response?.data?.detail || 'Failed to save policy');
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEdit) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" variant="primary" />
      </div>
    );
  }

  const steps = [
    { number: 1, title: 'Policy Details', icon: FaFileContract, description: 'Basic policy information' },
    { number: 2, title: 'Policy Holder', icon: FaUser, description: 'Policy holder details' },
    { number: 3, title: 'Premium Payer', icon: FaCreditCard, description: 'Premium payer information' },
    { number: 4, title: 'Payment Mandate', icon: FaFileContract, description: 'Payment and mandate details' },
    { number: 5, title: 'Beneficiaries', icon: FaUsers, description: 'Beneficiary information' }
  ];

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-0 text-gray-800">
                {isEdit ? 'Edit Policy' : 'Create New Policy'}
              </h1>
              <p className="text-muted">
                {isEdit ? 'Update policy information' : `Complete policy package - Policy #${policyNumber}`}
              </p>
            </div>
            <Button
              variant="outline-secondary"
              onClick={() => navigate('/policies')}
            >
              <i className="fas fa-arrow-left me-2"></i>
              Back to Policies
            </Button>
          </div>
        </Col>
      </Row>

      {/* Progress Steps */}
      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Body className="py-3">
              <div className="d-flex justify-content-between align-items-center">
                {steps.map((step, index) => (
                  <div key={step.number} className="d-flex align-items-center">
                    <div
                      className={`d-flex align-items-center justify-content-center rounded-circle me-3 ${
                        currentStep === step.number
                          ? 'bg-primary text-white'
                          : currentStep > step.number
                            ? 'bg-success text-white'
                            : 'bg-light text-muted'
                      }`}
                      style={{ width: '40px', height: '40px', cursor: 'pointer' }}
                      onClick={() => goToStep(step.number)}
                    >
                      {currentStep > step.number ? (
                        <FaCheck />
                      ) : (
                        <step.icon />
                      )}
                    </div>
                    <div className="d-none d-md-block">
                      <div className={`fw-bold ${currentStep === step.number ? 'text-primary' : ''}`}>
                        {step.title}
                      </div>
                      <small className="text-muted">{step.description}</small>
                    </div>
                    {index < steps.length - 1 && (
                      <div
                        className={`mx-3 ${
                          currentStep > step.number ? 'border-success' : 'border-light'
                        }`}
                        style={{
                          width: '50px',
                          height: '2px',
                          backgroundColor: currentStep > step.number ? '#28a745' : '#dee2e6'
                        }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FaExclamationTriangle className="me-2" />
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          <FaCheck className="me-2" />
          {success}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        {/* Step 1: Policy Details */}
        {currentStep === 1 && (
          <Row>
            <Col lg={8}>
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <FaFileContract className="me-2" />
                    Policy Information
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Policy Number *</Form.Label>
                        <Form.Control
                          type="text"
                          name="policyno"
                          value={policyData.policyno}
                          onChange={handlePolicyChange}
                          required
                          readOnly={!isEdit}
                          placeholder="Auto-generated"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Package *</Form.Label>
                        <Form.Select
                          name="packages"
                          value={policyData.packages}
                          onChange={handlePolicyChange}
                          required
                        >
                          <option value="">Select package</option>
                          <option value="Lite">Lite</option>
                          <option value="Standard">Standard</option>
                          <option value="Premier">Premier</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={4}>
                      <Form.Check
                        type="checkbox"
                        label="Bus"
                        name="bus"
                        checked={policyData.bus}
                        onChange={handlePolicyChange}
                        className="mb-3"
                      />
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>Policy Payer *</Form.Label>
                        <Form.Select
                          name="policypayer"
                          value={policyData.policypayer}
                          onChange={handlePolicyChange}
                          required
                        >
                          <option value="">Select policy payer</option>
                          <option value="Policy Holder">Policy Holder</option>
                          <option value="Other">Other</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>Status *</Form.Label>
                        <Form.Select
                          name="status"
                          value={policyData.status}
                          onChange={handlePolicyChange}
                          required
                        >
                          <option value="Pending">Pending</option>
                          <option value="Active">Active</option>
                          <option value="Expired">Expired</option>
                          <option value="Cancelled">Cancelled</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Agent Code *</Form.Label>
                        <Form.Control
                          type="text"
                          name="agentcode"
                          value={policyData.agentcode}
                          onChange={handlePolicyChange}
                          required
                          placeholder="Enter agent code"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Agent Name *</Form.Label>
                        <Form.Control
                          type="text"
                          name="agentname"
                          value={policyData.agentname}
                          onChange={handlePolicyChange}
                          required
                          placeholder="Enter agent name"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Scheme Code</Form.Label>
                        <Form.Control
                          type="text"
                          name="schemecode"
                          value={policyData.schemecode}
                          onChange={handlePolicyChange}
                          placeholder="Enter scheme code"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Worksite Code</Form.Label>
                        <Form.Control
                          type="text"
                          name="worksitecode"
                          value={policyData.worksitecode}
                          onChange={handlePolicyChange}
                          placeholder="Enter worksite code"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Risk Profile</Form.Label>
                        <Form.Select
                          name="riskprofile"
                          value={policyData.riskprofile}
                          onChange={handlePolicyChange}
                        >
                          <option value="">Select risk profile</option>
                          <option value="Low">Low</option>
                          <option value="Medium">Medium</option>
                          <option value="High">High</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Date Created</Form.Label>
                        <Form.Control
                          type="date"
                          name="datecreated"
                          value={policyData.datecreated}
                          readOnly
                          className="bg-light"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Applicant Signed Date *</Form.Label>
                        <Form.Control
                          type="date"
                          name="applicantsigneddate"
                          value={policyData.applicantsigneddate}
                          onChange={handlePolicyChange}
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Agent Signed Date *</Form.Label>
                        <Form.Control
                          type="date"
                          name="agentsigneddate"
                          value={policyData.agentsigneddate}
                          onChange={handlePolicyChange}
                          required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Check
                        type="checkbox"
                        label="PEP (Politically Exposed Person)"
                        name="pep"
                        checked={policyData.pep}
                        onChange={handlePolicyChange}
                        className="mb-3"
                      />
                    </Col>
                    <Col md={6}>
                      <Form.Check
                        type="checkbox"
                        label="AML (Anti-Money Laundering)"
                        name="aml"
                        checked={policyData.aml}
                        onChange={handlePolicyChange}
                        className="mb-3"
                      />
                    </Col>
                  </Row>

                  <Row>
                    <Col md={4}>
                      <Form.Check
                        type="checkbox"
                        label="Application Form Completed"
                        name="applicationform"
                        checked={policyData.applicationform}
                        onChange={handlePolicyChange}
                        className="mb-3"
                      />
                    </Col>
                    <Col md={4}>
                      <Form.Check
                        type="checkbox"
                        label="KYC Form Completed"
                        name="kycform"
                        checked={policyData.kycform}
                        onChange={handlePolicyChange}
                        className="mb-3"
                      />
                    </Col>
                    <Col md={4}>
                      <Form.Check
                        type="checkbox"
                        label="Mandate Form Completed"
                        name="mandateform"
                        checked={policyData.mandateform}
                        onChange={handlePolicyChange}
                        className="mb-3"
                      />
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
        {/* Step 2: Policy Holder */}
        {currentStep === 2 && (
          <PolicyHolderStep
            policyHolderData={policyHolderData}
            handlePolicyHolderChange={handlePolicyHolderChange}
          />
        )}

        {/* Step 3: Premium Payer */}
        {currentStep === 3 && (
          <PremiumPayerStep
            premiumPayerData={premiumPayerData}
            handlePremiumPayerChange={handlePremiumPayerChange}
            sameAsPolicyHolder={sameAsPolicyHolder}
            setSameAsPolicyHolder={setSameAsPolicyHolder}
          />
        )}

        {/* Step 4: Policy Mandate */}
        {currentStep === 4 && (
          <PolicyMandateStep
            policyMandateData={policyMandateData}
            handlePolicyMandateChange={handlePolicyMandateChange}
          />
        )}

        {/* Step 5: Beneficiaries */}
        {currentStep === 5 && (
          <BeneficiariesStep
            beneficiariesData={beneficiariesData}
            handleBeneficiaryChange={handleBeneficiaryChange}
            addBeneficiary={addBeneficiary}
            removeBeneficiary={removeBeneficiary}
          />
        )}

        {/* Navigation Buttons */}
        <Row className="mt-4">
          <Col>
            <div className="d-flex justify-content-between">
              <div>
                <Button
                  variant="outline-secondary"
                  onClick={() => navigate('/policies')}
                  disabled={loading}
                  className="me-2"
                >
                  Cancel
                </Button>
                {currentStep > 1 && (
                  <Button
                    variant="outline-primary"
                    onClick={prevStep}
                    disabled={loading}
                  >
                    Previous
                  </Button>
                )}
              </div>

              <div>
                {currentStep < 5 ? (
                  <Button
                    variant="primary"
                    onClick={nextStep}
                    disabled={loading || !validateStep(currentStep)}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    variant="success"
                    disabled={loading || !validateStep(currentStep)}
                  >
                    {loading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        {isEdit ? 'Updating...' : 'Creating Policy Package...'}
                      </>
                    ) : (
                      <>
                        <FaCheck className="me-2" />
                        {isEdit ? 'Update Policy' : 'Create Complete Policy Package'}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </Col>
        </Row>

        {/* Step Summary */}
        <Row className="mt-3">
          <Col>
            <div className="text-center">
              <small className="text-muted">
                Step {currentStep} of 5 - {steps[currentStep - 1]?.title}
                {!validateStep(currentStep) && (
                  <span className="text-danger ms-2">
                    <FaExclamationTriangle className="me-1" />
                    Please complete required fields
                  </span>
                )}
              </small>
            </div>
          </Col>
        </Row>
      </Form>
    </Container>
  );
};

export default PolicyForm;
