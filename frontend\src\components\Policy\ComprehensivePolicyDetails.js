import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Alert, Badge, Table } from 'react-bootstrap';
import { FaEdit, FaArrowLeft, FaPrint } from 'react-icons/fa';
import { useParams, useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';

const ComprehensivePolicyDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [policyData, setPolicyData] = useState(null);

  useEffect(() => {
    fetchPolicyDetails();
  }, [id]);

  const fetchPolicyDetails = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/policy-management/${id}`);
      setPolicyData(response.data.data);
    } catch (err) {
      setError(err.response?.data?.detail || 'Error fetching policy details');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container fluid className="p-4">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading policy details...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="p-4">
        <Alert variant="danger">{error}</Alert>
        <Button variant="secondary" onClick={() => navigate('/policy-search')}>
          <FaArrowLeft className="me-2" />
          Back to Policy Search
        </Button>
      </Container>
    );
  }

  if (!policyData) {
    return (
      <Container fluid className="p-4">
        <Alert variant="warning">Policy not found</Alert>
        <Button variant="secondary" onClick={() => navigate('/policy-search')}>
          <FaArrowLeft className="me-2" />
          Back to Policy Search
        </Button>
      </Container>
    );
  }

  const { policy, holder, mandate, beneficiaries } = policyData;

  return (
    <Container fluid className="p-4">
      <Row>
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h2>Policy Details - {policy.policyno}</h2>
            <div className="d-flex gap-2">
              <Button variant="secondary" onClick={() => navigate('/policy-search')}>
                <FaArrowLeft className="me-2" />
                Back to Policy Search
              </Button>
              <Button variant="outline-primary">
                <FaPrint className="me-2" />
                Print
              </Button>
              <Button variant="primary" onClick={() => navigate(`/policy-management/${id}/edit`)}>
                <FaEdit className="me-2" />
                Edit Policy
              </Button>
            </div>
          </div>

          {/* Policy Summary Card */}
          {policy && (
            <Card className="mb-4 border-primary">
              <Card.Header className="bg-primary text-white">
                <Row className="align-items-center">
                  <Col>
                    <h4 className="mb-0">Policy Summary</h4>
                  </Col>
                  <Col xs="auto">
                    <Badge bg={policy.status === 'Active' ? 'success' : 'warning'} className="fs-6">
                      {policy.status}
                    </Badge>
                  </Col>
                </Row>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={2}>
                    <div className="text-center p-2 bg-light rounded">
                      <div className="fw-bold text-primary mb-1" style={{ fontSize: '0.9rem', lineHeight: '1.2', wordBreak: 'break-all' }}>
                        {policy.policyno}
                      </div>
                      <div className="text-muted small">Policy Number</div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center p-2 bg-light rounded">
                      <div className="fw-bold text-success mb-1" style={{ fontSize: '0.9rem' }}>
                        {policy.packages}
                      </div>
                      <div className="text-muted small">Package</div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center p-2 bg-light rounded">
                      <div className="fw-bold text-info mb-1" style={{ fontSize: '0.9rem' }}>
                        {mandate ? mandate.premium : 'N/A'}
                      </div>
                      <div className="text-muted small">Premium</div>
                      <div className="text-muted" style={{ fontSize: '0.7rem' }}>
                        {mandate ? mandate.frequency : 'N/A'}
                      </div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center p-2 bg-light rounded">
                      <div className="h4 text-warning mb-1 fw-bold">
                        {beneficiaries ? beneficiaries.length : 0}
                      </div>
                      <div className="text-muted small">Beneficiaries</div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center p-2 bg-light rounded">
                      <div className={`h4 mb-1 ${policy.bus ? 'text-success' : 'text-secondary'}`}>
                        <i className={`fas ${policy.bus ? 'fa-bus' : 'fa-times-circle'}`}></i>
                      </div>
                      <div className="text-muted small">Bus Service</div>
                      <div className="mt-1">
                        <Badge bg={policy.bus ? 'success' : 'secondary'} className="small">
                          {policy.bus ? 'Included' : 'Not Included'}
                        </Badge>
                      </div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center p-2 bg-light rounded">
                      <div className={`h4 mb-1 ${policy.pep ? 'text-warning' : 'text-success'}`}>
                        <i className={`fas ${policy.pep ? 'fa-exclamation-triangle' : 'fa-check-circle'}`}></i>
                      </div>
                      <div className="text-muted small">PEP Status</div>
                      <div className="mt-1">
                        <Badge bg={policy.pep ? 'warning' : 'success'} className="small">
                          {policy.pep ? 'PEP' : 'Non-PEP'}
                        </Badge>
                      </div>
                    </div>
                  </Col>
                </Row>
                <Row className="mt-3">
                  <Col md={6}>
                    <div className="d-flex align-items-center">
                      <strong className="me-2">Policy Holder:</strong>
                      <span>
                        {holder ? `${holder.title} ${holder.firstname} ${holder.surname}` : 'Not available'}
                      </span>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="d-flex align-items-center">
                      <strong className="me-2">Agent:</strong>
                      <span>{policy.agentname} ({policy.agentcode})</span>
                    </div>
                  </Col>
                </Row>
                <Row className="mt-2">
                  <Col md={6}>
                    <div className="d-flex align-items-center">
                      <strong className="me-2">Payment Mode:</strong>
                      <span>
                        {mandate ? mandate.modeofpayment : 'Not available'}
                      </span>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="d-flex align-items-center">
                      <strong className="me-2">Date Created:</strong>
                      <span>{policy.datecreated}</span>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}

          {/* Policy Information - Comprehensive */}
          <Card className="mb-4">
            <Card.Header>
              <h5>Policy Information</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={4}>
                  <h6 className="text-primary mb-3">Basic Information</h6>
                  <table className="table table-borderless table-sm">
                    <tbody>
                      <tr>
                        <td><strong>Policy Number:</strong></td>
                        <td>{policy.policyno}</td>
                      </tr>
                      <tr>
                        <td><strong>Package:</strong></td>
                        <td><Badge bg="secondary">{policy.packages}</Badge></td>
                      </tr>
                      <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                          <Badge bg={policy.status === 'Active' ? 'success' : 'warning'}>
                            {policy.status}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td><strong>Policy Payer:</strong></td>
                        <td>{policy.policypayer || '-'}</td>
                      </tr>
                      <tr>
                        <td><strong>Risk Profile:</strong></td>
                        <td>{policy.riskprofile || '-'}</td>
                      </tr>
                    </tbody>
                  </table>
                </Col>
                <Col md={4}>
                  <h6 className="text-primary mb-3">Agent & Location</h6>
                  <table className="table table-borderless table-sm">
                    <tbody>
                      <tr>
                        <td><strong>Agent Code:</strong></td>
                        <td>{policy.agentcode}</td>
                      </tr>
                      <tr>
                        <td><strong>Agent Name:</strong></td>
                        <td>{policy.agentname}</td>
                      </tr>
                      <tr>
                        <td><strong>Scheme Code:</strong></td>
                        <td>{policy.schemecode || '-'}</td>
                      </tr>
                      <tr>
                        <td><strong>Worksite Code:</strong></td>
                        <td>{policy.worksitecode || '-'}</td>
                      </tr>
                    </tbody>
                  </table>
                </Col>
                <Col md={4}>
                  <h6 className="text-primary mb-3">Compliance & Forms</h6>
                  <table className="table table-borderless table-sm">
                    <tbody>
                      <tr>
                        <td><strong>Bus:</strong></td>
                        <td>
                          <Badge bg={policy.bus ? 'success' : 'secondary'}>
                            {policy.bus ? 'Included' : 'Not Included'}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td><strong>PEP:</strong></td>
                        <td>
                          <Badge bg={policy.pep ? 'warning' : 'secondary'}>
                            {policy.pep ? 'Yes' : 'No'}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td><strong>AML:</strong></td>
                        <td>
                          <Badge bg={policy.aml ? 'warning' : 'secondary'}>
                            {policy.aml ? 'Yes' : 'No'}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td><strong>Application Form:</strong></td>
                        <td>
                          <Badge bg={policy.applicationform ? 'success' : 'danger'}>
                            {policy.applicationform ? 'Complete' : 'Pending'}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td><strong>KYC Form:</strong></td>
                        <td>
                          <Badge bg={policy.kycform ? 'success' : 'danger'}>
                            {policy.kycform ? 'Complete' : 'Pending'}
                          </Badge>
                        </td>
                      </tr>
                      <tr>
                        <td><strong>Mandate Form:</strong></td>
                        <td>
                          <Badge bg={policy.mandateform ? 'success' : 'danger'}>
                            {policy.mandateform ? 'Complete' : 'Pending'}
                          </Badge>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </Col>
              </Row>
              <Row className="mt-3">
                <Col md={6}>
                  <h6 className="text-primary mb-3">Signature Dates</h6>
                  <table className="table table-borderless table-sm">
                    <tbody>
                      <tr>
                        <td><strong>Applicant Signed Date:</strong></td>
                        <td>{policy.applicantsigneddate || '-'}</td>
                      </tr>
                      <tr>
                        <td><strong>Agent Signed Date:</strong></td>
                        <td>{policy.agentsigneddate || '-'}</td>
                      </tr>
                    </tbody>
                  </table>
                </Col>
                <Col md={6}>
                  <h6 className="text-primary mb-3">System Information</h6>
                  <table className="table table-borderless table-sm">
                    <tbody>
                      <tr>
                        <td><strong>Date Created:</strong></td>
                        <td>{policy.datecreated}</td>
                      </tr>
                      <tr>
                        <td><strong>Created By:</strong></td>
                        <td>User ID: {policy.createdby}</td>
                      </tr>
                    </tbody>
                  </table>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Policy Holder Information - Comprehensive */}
          {holder && (
            <Card className="mb-4">
              <Card.Header>
                <h5>Policy Holder Information</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={4}>
                    <h6 className="text-primary mb-3">Personal Information</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Full Name:</strong></td>
                          <td>{holder.title} {holder.initials} {holder.firstname} {holder.middlename} {holder.surname}</td>
                        </tr>
                        <tr>
                          <td><strong>Previous Name:</strong></td>
                          <td>{holder.previousname || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Gender:</strong></td>
                          <td>
                            <Badge bg={holder.gender === 'Male' ? 'primary' : 'info'}>
                              {holder.gender}
                            </Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Date of Birth:</strong></td>
                          <td>{holder.dateofbirth}</td>
                        </tr>
                        <tr>
                          <td><strong>Country of Birth:</strong></td>
                          <td>{holder.countryofbirth || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Marital Status:</strong></td>
                          <td>
                            <Badge bg="secondary">{holder.maritalstatus}</Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Residential Status:</strong></td>
                          <td>{holder.residentialstatus || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </Col>
                  <Col md={4}>
                    <h6 className="text-primary mb-3">Identification & Documents</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Primary ID:</strong></td>
                          <td>{holder.primaryid || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Primary ID Expiry:</strong></td>
                          <td>{holder.primaryidexpirydate || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Secondary ID:</strong></td>
                          <td>{holder.secondaryid || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Secondary ID Expiry:</strong></td>
                          <td>{holder.secondaryidexpirydate || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Expatriate:</strong></td>
                          <td>
                            <Badge bg={holder.expatriate === 'Yes' ? 'warning' : 'secondary'}>
                              {holder.expatriate || 'No'}
                            </Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Work Permit:</strong></td>
                          <td>{holder.workpermit || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Work Permit Expiry:</strong></td>
                          <td>{holder.workpermitexpirydate || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </Col>
                  <Col md={4}>
                    <h6 className="text-primary mb-3">Contact Information</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Email Address:</strong></td>
                          <td>{holder.emailaddress || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Mobile Number:</strong></td>
                          <td>{holder.phoneno || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Alternative Mobile:</strong></td>
                          <td>{holder.altmobileno || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Communication Preference:</strong></td>
                          <td>{holder.commpreference || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </Col>
                </Row>

                <Row className="mt-4">
                  <Col md={4}>
                    <h6 className="text-primary mb-3">Employment & Financial</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Occupation:</strong></td>
                          <td>{holder.occupation || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Industry:</strong></td>
                          <td>{holder.industry || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Employer:</strong></td>
                          <td>{holder.employer || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Source of Income:</strong></td>
                          <td>{holder.sourceofincome || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Net Monthly Income:</strong></td>
                          <td>{holder.netmonthlyincome || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Specify Income:</strong></td>
                          <td>{holder.specifyincome || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Proof of Source of Funds:</strong></td>
                          <td>{holder.proofofsourceoffunds || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Specify Proof:</strong></td>
                          <td>{holder.specifyproofofsourceoffunds || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </Col>
                  <Col md={4}>
                    <h6 className="text-primary mb-3">Physical Address</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Address Line 1:</strong></td>
                          <td>{holder.physicaladdressline1 || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Address Line 2:</strong></td>
                          <td>{holder.physicaladdressline2 || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Address Line 3:</strong></td>
                          <td>{holder.physicaladdressline3 || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Address Line 4:</strong></td>
                          <td>{holder.physicaladdressline4 || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>City:</strong></td>
                          <td>{holder.physicaladdressline5city || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Country:</strong></td>
                          <td>{holder.physicaladdressline6country || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </Col>
                  <Col md={4}>
                    <h6 className="text-primary mb-3">Postal & Permanent Address</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Postal Line 1:</strong></td>
                          <td>{holder.postaladdressline1 || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Postal Line 2:</strong></td>
                          <td>{holder.postaladdressline2 || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Postal City:</strong></td>
                          <td>{holder.postaladdressline5city || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Postal Country:</strong></td>
                          <td>{holder.postaladdressline6country || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Permanent Village:</strong></td>
                          <td>{holder.permanentaddressvillage || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Permanent TA:</strong></td>
                          <td>{holder.permanentaddressta || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Permanent District:</strong></td>
                          <td>{holder.permanentaddressdistrict || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}

          {/* Payment Mandate Information - Mode-Specific Display */}
          {mandate && (
            <Card className="mb-4">
              <Card.Header>
                <h5>Payment Mandate Information</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <h6 className="text-primary mb-3">Payment Details</h6>
                    <table className="table table-borderless table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Premium Amount:</strong></td>
                          <td>
                            <Badge bg="success" className="fs-6">
                              {mandate.premium}
                            </Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Frequency:</strong></td>
                          <td>
                            <Badge bg="info">{mandate.frequency}</Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Mode of Payment:</strong></td>
                          <td>
                            <Badge bg="primary">{mandate.modeofpayment}</Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>First Deduction Date:</strong></td>
                          <td>{mandate.firstdeductiondate}</td>
                        </tr>
                        {(mandate.modeofpayment === 'Mobile Money' || mandate.mobilephoneno) && (
                          <>
                            <tr>
                              <td><strong>Mobile Phone No:</strong></td>
                              <td>{mandate.mobilephoneno || '-'}</td>
                            </tr>
                            <tr>
                              <td><strong>Mobile Operator:</strong></td>
                              <td>{mandate.mobileoperator || '-'}</td>
                            </tr>
                          </>
                        )}
                      </tbody>
                    </table>
                  </Col>

                  <Col md={6}>
                    {/* Conditional Display Based on Payment Mode */}
                    {mandate.modeofpayment === 'Debit Order' && (
                      <>
                        <h6 className="text-primary mb-3">
                          <i className="fas fa-university me-2"></i>
                          Debit Order Details
                        </h6>
                        <table className="table table-borderless table-sm">
                          <tbody>
                            <tr>
                              <td><strong>Account Holder:</strong></td>
                              <td>
                                <div className="fw-bold">
                                  {mandate.debitorderFirstname} {mandate.debitorderLastname}
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Bank Name:</strong></td>
                              <td>{mandate.debitorderNameofbank || '-'}</td>
                            </tr>
                            <tr>
                              <td><strong>Branch:</strong></td>
                              <td>{mandate.debitorderBranch || '-'}</td>
                            </tr>
                            <tr>
                              <td><strong>Account Number:</strong></td>
                              <td>
                                <code className="bg-light p-1 rounded">
                                  {mandate.debitorderAccountno || '-'}
                                </code>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Account Type:</strong></td>
                              <td>
                                <Badge bg="secondary">
                                  {mandate.debitorderTypeofaccount || 'Not specified'}
                                </Badge>
                              </td>
                            </tr>
                            {mandate.debitorderSalaryfundingdate && (
                              <tr>
                                <td><strong>Salary Funding Date:</strong></td>
                                <td>{mandate.debitorderSalaryfundingdate}</td>
                              </tr>
                            )}
                            {mandate.debitorderPremiumonapplication && (
                              <tr>
                                <td><strong>Premium on Application:</strong></td>
                                <td>{mandate.debitorderPremiumonapplication}</td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </>
                    )}

                    {mandate.modeofpayment === 'Stop Order' && (
                      <>
                        <h6 className="text-primary mb-3">
                          <i className="fas fa-building me-2"></i>
                          Stop Order Details
                        </h6>
                        <table className="table table-borderless table-sm">
                          <tbody>
                            <tr>
                              <td><strong>Employee ID:</strong></td>
                              <td>
                                <code className="bg-light p-1 rounded">
                                  {mandate.stoporderEmployeeid || '-'}
                                </code>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Employee Name:</strong></td>
                              <td>
                                <div className="fw-bold">
                                  {mandate.stoporderEmployeename || '-'}
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Employer Name:</strong></td>
                              <td>
                                <Badge bg="info" className="fs-6">
                                  {mandate.stoporderEmployername || '-'}
                                </Badge>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Employer Address:</strong></td>
                              <td>
                                <div className="small">
                                  {mandate.stoporderEmployeraddress || '-'}
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </>
                    )}

                    {mandate.modeofpayment === 'Mobile Money' && (
                      <>
                        <h6 className="text-primary mb-3">
                          <i className="fas fa-mobile-alt me-2"></i>
                          Mobile Money Details
                        </h6>
                        <table className="table table-borderless table-sm">
                          <tbody>
                            <tr>
                              <td><strong>Mobile Number:</strong></td>
                              <td>
                                <code className="bg-light p-1 rounded">
                                  {mandate.mobilephoneno || '-'}
                                </code>
                              </td>
                            </tr>
                            <tr>
                              <td><strong>Mobile Operator:</strong></td>
                              <td>
                                <Badge bg="warning" text="dark">
                                  {mandate.mobileoperator || 'Not specified'}
                                </Badge>
                              </td>
                            </tr>
                            {mandate.stoporderEmployername && (
                              <tr>
                                <td><strong>Service Provider:</strong></td>
                                <td>{mandate.stoporderEmployername}</td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </>
                    )}

                    {!['Debit Order', 'Stop Order', 'Mobile Money'].includes(mandate.modeofpayment) && (
                      <>
                        <h6 className="text-primary mb-3">
                          <i className="fas fa-credit-card me-2"></i>
                          {mandate.modeofpayment} Details
                        </h6>
                        <div className="alert alert-info">
                          <i className="fas fa-info-circle me-2"></i>
                          Payment method: <strong>{mandate.modeofpayment}</strong>
                          <br />
                          <small>Specific details for this payment method are not configured for display.</small>
                        </div>
                        {/* Show any available details */}
                        <table className="table table-borderless table-sm">
                          <tbody>
                            {mandate.debitorderFirstname && (
                              <tr>
                                <td><strong>Contact Person:</strong></td>
                                <td>{mandate.debitorderFirstname} {mandate.debitorderLastname}</td>
                              </tr>
                            )}
                            {mandate.stoporderEmployername && (
                              <tr>
                                <td><strong>Provider/Institution:</strong></td>
                                <td>{mandate.stoporderEmployername}</td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </>
                    )}
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}

          {/* Beneficiaries Information - Comprehensive */}
          {beneficiaries && beneficiaries.length > 0 && (
            <Card className="mb-4">
              <Card.Header>
                <h5>Beneficiaries ({beneficiaries.length})</h5>
              </Card.Header>
              <Card.Body>
                <Table responsive striped hover>
                  <thead className="table-dark">
                    <tr>
                      <th>#</th>
                      <th>Full Name</th>
                      <th>Gender</th>
                      <th>Birth Date</th>
                      <th>Marital Status</th>
                      <th>Relationship</th>
                      <th>Member Type</th>
                      <th>Ownership</th>
                      <th>ID Number</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {beneficiaries.map((beneficiary, index) => (
                      <tr key={beneficiary.id || index}>
                        <td>
                          <Badge bg="primary" className="rounded-pill">
                            {index + 1}
                          </Badge>
                        </td>
                        <td>
                          <div>
                            <strong>
                              {beneficiary.title} {beneficiary.initials} {beneficiary.firstname} {beneficiary.middlename} {beneficiary.surname}
                            </strong>
                          </div>
                          {beneficiary.initials && (
                            <div className="small text-muted">Initials: {beneficiary.initials}</div>
                          )}
                        </td>
                        <td>
                          <Badge bg={beneficiary.gender === 'Male' ? 'primary' : 'info'}>
                            {beneficiary.gender}
                          </Badge>
                        </td>
                        <td>
                          <div>{beneficiary.birthdate}</div>
                          {beneficiary.birthdate && (
                            <div className="small text-muted">
                              Age: {new Date().getFullYear() - new Date(beneficiary.birthdate).getFullYear()}
                            </div>
                          )}
                        </td>
                        <td>
                          <Badge bg="secondary">
                            {beneficiary.maritalstatus || 'Not specified'}
                          </Badge>
                        </td>
                        <td>
                          <Badge bg="warning" text="dark">
                            {beneficiary.relationship}
                          </Badge>
                        </td>
                        <td>
                          <Badge bg="info">{beneficiary.membertype}</Badge>
                        </td>
                        <td>
                          {beneficiary.benofownership || '-'}
                        </td>
                        <td>
                          <div>{beneficiary.idnumber || '-'}</div>
                          {beneficiary.idnumber && (
                            <div className="small text-muted">ID Provided</div>
                          )}
                        </td>
                        <td>
                          <Badge bg={beneficiary.status === 'ACTIVE' ? 'success' : 'secondary'}>
                            {beneficiary.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                {/* Beneficiaries Summary */}
                <Row className="mt-3">
                  <Col md={12}>
                    <div className="bg-light p-3 rounded">
                      <h6 className="text-primary mb-2">Beneficiaries Summary</h6>
                      <Row>
                        <Col md={3}>
                          <div className="text-center">
                            <div className="h4 text-primary">{beneficiaries.length}</div>
                            <div className="small text-muted">Total Beneficiaries</div>
                          </div>
                        </Col>
                        <Col md={3}>
                          <div className="text-center">
                            <div className="h4 text-success">
                              {beneficiaries.filter(b => b.status === 'ACTIVE').length}
                            </div>
                            <div className="small text-muted">Active</div>
                          </div>
                        </Col>
                        <Col md={3}>
                          <div className="text-center">
                            <div className="h4 text-info">
                              {beneficiaries.filter(b => b.membertype === 'Family').length}
                            </div>
                            <div className="small text-muted">Family Members</div>
                          </div>
                        </Col>
                        <Col md={3}>
                          <div className="text-center">
                            <div className="h4 text-warning">
                              {beneficiaries.filter(b => b.idnumber && b.idnumber.trim() !== '').length}
                            </div>
                            <div className="small text-muted">With ID Numbers</div>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default ComprehensivePolicyDetails;
