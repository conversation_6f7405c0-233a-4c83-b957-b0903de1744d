from models import User as UserModel
from sqlalchemy.orm import Session
from schema import UserBase as User


# User
# create
def create_user(db: Session, user_model: UserModel):
    """Create a new user - expects a SQLAlchemy User model instance"""
    db.add(user_model)
    db.commit()
    db.refresh(user_model)
    return user_model

# get single
def get_user(db: Session, user_id: int):
    return db.query(UserModel).filter(UserModel.id == user_id).first()

# get list
def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(UserModel).offset(skip).limit(limit).all()

# update
def update_user(db: Session, user_id: int, data: User):
    user_instance = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user_instance:
        return None
    for key, value in data.model_dump().items():
        setattr(user_instance, key, value)
    db.commit()
    db.refresh(user_instance)
    return user_instance

# delete
def delete_user(db: Session, user_id: int):
    user_instance = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user_instance:
        return None
    db.delete(user_instance)
    db.commit()
    return user_instance