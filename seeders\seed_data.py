"""
Database seeder script for Mthunzi Policy Management System
Creates default users, sample policies, and test data
"""

import sys
import os
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db import SessionLocal, engine
from models import User, Policy, PolicyHolder, Beneficiaries, PremiumPayer, PolicyMandate, PolicySummary, PolicyActivities
from auth import hash_password

def get_db_session():
    """Get database session"""
    return SessionLocal()

def create_default_users(db: Session):
    """Create default system users"""
    print("Creating default users...")
    
    # Check if users already exist
    existing_user = db.query(User).filter(User.username == "admin").first()
    if existing_user:
        print("Default users already exist, skipping...")
        return
    
    default_users = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "password",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "yes",
            "servicing": "yes",
            "mimoadmin": "yes",
            "sysadmin": "yes",
            "createdby": "system",
            "datecreated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "username": "policymanager",
            "email": "<EMAIL>",
            "password": "policy123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "yes",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "username": "serviceagent",
            "email": "<EMAIL>",
            "password": "service123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "no",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "username": "viewer",
            "email": "<EMAIL>",
            "password": "viewer123",
            "status": "active",
            "viewonly": "yes",
            "policyadmin": "no",
            "servicing": "no",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    ]
    
    for user_data in default_users:
        # Hash the password
        hashed_password = hash_password(user_data["password"])
        
        user = User(
            username=user_data["username"],
            email=user_data["email"],
            hashed_password=hashed_password,
            password=hashed_password,  # For backward compatibility
            status=user_data["status"],
            viewonly=user_data["viewonly"],
            policyadmin=user_data["policyadmin"],
            servicing=user_data["servicing"],
            mimoadmin=user_data["mimoadmin"],
            sysadmin=user_data["sysadmin"],
            createdby=user_data["createdby"],
            datecreated=user_data["datecreated"]
        )
        
        db.add(user)
        print(f"Created user: {user_data['username']}")
    
    db.commit()
    print("Default users created successfully!")

def create_sample_policies(db: Session):
    """Create sample policies with related data"""
    print("Creating sample policies...")
    
    # Check if policies already exist
    existing_policy = db.query(Policy).filter(Policy.policyno == "POL001").first()
    if existing_policy:
        print("Sample policies already exist, skipping...")
        return
    
    # Get admin user ID for created_by field
    admin_user = db.query(User).filter(User.username == "admin").first()
    admin_id = admin_user.id if admin_user else 1
    
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    sample_policies = [
        {
            "policyno": "POL001",
            "packages": "Life Insurance Premium",
            "bus": "Individual",
            "agentcode": "AGT001",
            "agentname": "John Smith",
            "pep": False,
            "aml": True,
            "applicantsigneddate": current_date,
            "agentsigneddate": current_date,
            "applicationform": "completed",
            "kycform": "completed",
            "mandateform": "completed",
            "schemecode": "SCH001",
            "worksitecode": "WS001",
            "riskprofile": "Low",
            "status": "active",
            "datecreated": current_date,
            "createdby": admin_id
        },
        {
            "policyno": "POL002",
            "packages": "Family Protection Plan",
            "bus": "Family",
            "agentcode": "AGT002",
            "agentname": "Sarah Johnson",
            "pep": False,
            "aml": True,
            "applicantsigneddate": current_date,
            "agentsigneddate": current_date,
            "applicationform": "completed",
            "kycform": "completed",
            "mandateform": "pending",
            "schemecode": "SCH002",
            "worksitecode": "WS002",
            "riskprofile": "Medium",
            "status": "pending",
            "datecreated": current_date,
            "createdby": admin_id
        }
    ]
    
    for policy_data in sample_policies:
        policy = Policy(**policy_data)
        db.add(policy)
        print(f"Created policy: {policy_data['policyno']}")
    
    db.commit()
    print("Sample policies created successfully!")

def create_sample_policyholders(db: Session):
    """Create sample policy holders"""
    print("Creating sample policy holders...")

    try:
        # Check if policy holders already exist
        existing_holder = db.query(PolicyHolder).filter(PolicyHolder.policyno == "POL001").first()
        if existing_holder:
            print("Sample policy holders already exist, skipping...")
            return
    except Exception as e:
        print(f"PolicyHolder table doesn't exist yet, skipping policy holders: {e}")
        return
    
    sample_holders = [
        {
            "policyno": "POL001",
            "title": "Mr",
            "initials": "J.D.",
            "middlename": "David",
            "surname": "Williams",
            "gender": "Male",
            "dateofbirth": "1985-03-15",
            "countryofbirth": "South Africa",
            "maritalstatus": "Married",
            "residentialstatus": "Citizen",
            "primaryid": "8503155678901",
            "primaryidexpirydate": "2030-03-15",
            "emailaddress": "<EMAIL>",
            "phoneno": "+27123456789",
            "physicaladdressline1": "123 Main Street",
            "physicaladdressline2": "Suburb",
            "physicaladdressline5city": "Cape Town",
            "physicaladdressline6country": "South Africa",
            "occupation": "Software Engineer",
            "industry": "Technology",
            "employer": "Tech Solutions Ltd",
            "netmonthlyincome": "R45000"
        },
        {
            "policyno": "POL002",
            "title": "Mrs",
            "initials": "M.A.",
            "middlename": "Anne",
            "surname": "Brown",
            "gender": "Female",
            "dateofbirth": "1990-07-22",
            "countryofbirth": "South Africa",
            "maritalstatus": "Single",
            "residentialstatus": "Citizen",
            "primaryid": "9007225678902",
            "primaryidexpirydate": "2030-07-22",
            "emailaddress": "<EMAIL>",
            "phoneno": "+27987654321",
            "physicaladdressline1": "456 Oak Avenue",
            "physicaladdressline2": "Greenside",
            "physicaladdressline5city": "Johannesburg",
            "physicaladdressline6country": "South Africa",
            "occupation": "Teacher",
            "industry": "Education",
            "employer": "ABC Primary School",
            "netmonthlyincome": "R28000"
        }
    ]
    
    for holder_data in sample_holders:
        holder = PolicyHolder(**holder_data)
        db.add(holder)
        print(f"Created policy holder for: {holder_data['policyno']}")
    
    db.commit()
    print("Sample policy holders created successfully!")

def run_all_seeders():
    """Run all seeder functions"""
    print("Starting database seeding...")
    
    db = get_db_session()
    try:
        create_default_users(db)
        create_sample_policies(db)
        create_sample_policyholders(db)
        
        print("\n✅ Database seeding completed successfully!")
        print("\nDefault users created:")
        print("- admin/admin123 (Full admin access)")
        print("- policymanager/policy123 (Policy management)")
        print("- serviceagent/service123 (Service operations)")
        print("- viewer/viewer123 (Read-only access)")
        
    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    run_all_seeders()
