#!/usr/bin/env python3
"""
Create valid test data for import testing
"""

import pandas as pd
import sys
from datetime import datetime, timedelta
import random

def create_policy_data(num_rows=5):
    """Create valid policy test data"""
    data = []
    packages = ["Standard", "Premier", "Lite"]
    risk_profiles = ["Low", "Medium", "High"]
    statuses = ["Active", "Pending", "Suspended"]

    for i in range(1, num_rows + 1):
        # Create base required data
        policy_data = {
            "policyno": f"POL{i:03d}",
            "packages": random.choice(packages),
            "bus": random.choice([True, False]),
            "pep": random.choice([True, False]),
            "agentcode": f"AG{i:03d}",
            "agentname": f"Agent {i}",
            "status": random.choice(statuses)
        }

        # Randomly add optional fields (to show they're optional)
        if random.choice([True, False]):  # 50% chance to include optional fields
            policy_data.update({
                "schemecode": f"SCH{i:03d}",
                "worksitecode": f"WS{i:03d}",
                "riskprofile": random.choice(risk_profiles)
            })

        data.append(policy_data)

    return pd.DataFrame(data)

def create_policyholder_data(num_rows=5):
    """Create valid policyholder test data"""
    data = []
    titles = ["Mr", "Ms", "Mrs", "Dr"]
    genders = ["Male", "Female"]
    marital_statuses = ["Single", "Married", "Divorced", "Widowed"]

    for i in range(1, num_rows + 1):
        # Generate random birth date (between 18-80 years old)
        birth_year = datetime.now().year - random.randint(18, 80)
        birth_date = datetime(birth_year, random.randint(1, 12), random.randint(1, 28))

        # Generate capture date (recent)
        capture_date = datetime.now() - timedelta(days=random.randint(1, 30))

        # Create base required data
        holder_data = {
            "policyno": f"POL{i:03d}",
            "title": random.choice(titles),
            "initials": f"F{i}.S{i}.",
            "firstname": f"FirstName{i}",
            "surname": f"Surname{i}",
            "dateofbirth": birth_date.strftime("%Y-%m-%d"),
            "gender": random.choice(genders),
            "maritalstatus": random.choice(marital_statuses),
            "capturedate": capture_date.strftime("%Y-%m-%d")
        }

        # Randomly add optional fields (to show they're optional)
        if random.choice([True, False]):  # 50% chance to include optional fields
            holder_data.update({
                "idnumber": f"{random.randint(*********0000, *********9999)}",
                "idtype": random.choice(["National ID", "Passport", "Driver's License"]),
                "occupation": random.choice(["Teacher", "Engineer", "Doctor", "Nurse", "Accountant"]),
                "mobilenumber": f"0{random.randint(*********, *********)}",
                "emailaddress": f"user{i}@example.com",
                "town": random.choice(["Lilongwe", "Blantyre", "Mzuzu", "Zomba"]),
                "residentialstatus": "Citizen"
            })

        data.append(holder_data)

    return pd.DataFrame(data)

def create_policymandate_data(num_rows=5):
    """Create valid policy mandate test data"""
    data = []
    payment_modes = ["Debit Order", "Stop Order", "Mobile Money"]
    frequencies = ["Monthly", "Quarterly", "Annually"]
    account_types = ["Savings", "Current", "Transmission"]
    paypoint_names = ["Standard Bank", "FDH Bank", "TNM", "Airtel Money", "ABC Company", "XYZ Corporation"]

    for i in range(1, num_rows + 1):
        # Generate future deduction date
        future_date = datetime.now() + timedelta(days=random.randint(1, 90))

        # Create base required data
        mandate_data = {
            "policyno": f"POL{i:03d}",
            "firstname": f"FirstName{i}",
            "surname": f"Surname{i}",
            "mobilenumber": f"0{random.randint(*********, *********)}",
            "frequency": random.choice(frequencies),
            "premium": round(random.uniform(100, 1000), 2),
            "modeofpayment": random.choice(payment_modes),
            "FirstDeductionStartDate": future_date.strftime("%Y-%m-%d"),
            "PaypointName": random.choice(paypoint_names)
        }

        # Randomly add optional fields (to show they're optional)
        if random.choice([True, False]):  # 50% chance to include optional fields
            mandate_data.update({
                "BankAccountNumber": f"{random.randint(*********0, *********9)}",
                "BankAccountType": random.choice(account_types),
                "BranchName": f"Branch{i}"
            })

        data.append(mandate_data)

    return pd.DataFrame(data)

def create_beneficiaries_data(num_rows=5):
    """Create valid beneficiaries test data"""
    data = []
    titles = ["Mr", "Ms", "Mrs", "Dr"]
    genders = ["Male", "Female"]
    relationships = ["Son", "Daughter", "Mother", "Father", "Brother", "Sister", "Spouse"]
    member_types = ["Family", "Other", "BFO"]
    
    for i in range(1, num_rows + 1):
        # Generate random birth date
        birth_year = datetime.now().year - random.randint(1, 70)
        birth_date = datetime(birth_year, random.randint(1, 12), random.randint(1, 28))
        
        # Use existing policy numbers (POL001, POL002, etc.)
        policy_num = random.randint(1, 3)  # Reference first 3 policies
        
        # Create base required data
        beneficiary_data = {
            "policyno": f"POL{policy_num:03d}",
            "title": random.choice(titles),
            "initials": f"B{i}.S{i}.",
            "firstname": f"Beneficiary{i}",
            "surname": f"Surname{i}",
            "gender": random.choice(genders),
            "birthdate": birth_date.strftime("%Y-%m-%d"),
            "relationship": random.choice(relationships),
            "membertype": random.choice(member_types)
        }

        # Randomly add optional idnumber field (to show it's optional)
        if random.choice([True, False]):  # 50% chance to include idnumber
            beneficiary_data["idnumber"] = f"{random.randint(*********0000, *********9999)}"

        data.append(beneficiary_data)
    
    return pd.DataFrame(data)

def main():
    """Main function"""
    print("🏭 TEST DATA GENERATOR")
    print("=" * 50)
    
    if len(sys.argv) != 3:
        print("Usage: python create_test_data.py <import_type> <num_rows>")
        print("Import types: policy, policyholder, policymandate, beneficiaries")
        print("\nExample:")
        print("  python create_test_data.py policy 10")
        return
    
    import_type = sys.argv[1]
    try:
        num_rows = int(sys.argv[2])
    except ValueError:
        print("❌ Number of rows must be an integer")
        return
    
    if import_type not in ["policy", "policyholder", "policymandate", "beneficiaries"]:
        print(f"❌ Invalid import type: {import_type}")
        print("Valid types: policy, policyholder, policymandate, beneficiaries")
        return
    
    if num_rows < 1 or num_rows > 1000:
        print("❌ Number of rows must be between 1 and 1000")
        return
    
    print(f"🔄 Creating {num_rows} rows of {import_type} test data...")
    
    # Create data based on type
    if import_type == "policy":
        df = create_policy_data(num_rows)
    elif import_type == "policyholder":
        df = create_policyholder_data(num_rows)
    elif import_type == "policymandate":
        df = create_policymandate_data(num_rows)
    elif import_type == "beneficiaries":
        df = create_beneficiaries_data(num_rows)
    
    # Save to CSV
    filename = f"test_{import_type}_{num_rows}rows.csv"
    df.to_csv(filename, index=False)
    
    print(f"✅ Created: {filename}")
    print(f"📊 Columns: {list(df.columns)}")
    print(f"📋 Sample data:")
    print(df.head(3).to_string())
    
    print(f"\n🎯 Next steps:")
    print(f"1. Upload {filename} to your import system")
    print(f"2. Select '{import_type}' as import type")
    print(f"3. Click 'Validate File' - should work perfectly!")

if __name__ == "__main__":
    main()
