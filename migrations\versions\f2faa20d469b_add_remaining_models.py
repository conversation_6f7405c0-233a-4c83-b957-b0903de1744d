"""Add remaining models

Revision ID: f2faa20d469b
Revises: 382f5346e037
Create Date: 2025-07-24 18:26:14.385898

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f2faa20d469b'
down_revision: Union[str, Sequence[str], None] = '382f5346e037'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('beneficiaries',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyid', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('initials', sa.String(), nullable=True),
    sa.Column('firstname', sa.String(), nullable=True),
    sa.Column('middlename', sa.String(), nullable=True),
    sa.Column('surname', sa.String(), nullable=True),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('birthdate', sa.String(), nullable=True),
    sa.Column('maritalstatus', sa.String(), nullable=True),
    sa.Column('membertype', sa.String(), nullable=True),
    sa.Column('relationship', sa.String(), nullable=True),
    sa.Column('benofownership', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('datecreated', sa.String(), nullable=True),
    sa.Column('createdby', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_ben_id', 'beneficiaries', ['id'], unique=False)
    op.create_index('ix_ben_policyid', 'beneficiaries', ['policyid'], unique=False)
    op.create_index(op.f('ix_beneficiaries_benofownership'), 'beneficiaries', ['benofownership'], unique=False)
    op.create_index(op.f('ix_beneficiaries_birthdate'), 'beneficiaries', ['birthdate'], unique=False)
    op.create_index(op.f('ix_beneficiaries_createdby'), 'beneficiaries', ['createdby'], unique=False)
    op.create_index(op.f('ix_beneficiaries_datecreated'), 'beneficiaries', ['datecreated'], unique=False)
    op.create_index(op.f('ix_beneficiaries_firstname'), 'beneficiaries', ['firstname'], unique=False)
    op.create_index(op.f('ix_beneficiaries_gender'), 'beneficiaries', ['gender'], unique=False)
    op.create_index(op.f('ix_beneficiaries_id'), 'beneficiaries', ['id'], unique=False)
    op.create_index(op.f('ix_beneficiaries_initials'), 'beneficiaries', ['initials'], unique=False)
    op.create_index(op.f('ix_beneficiaries_maritalstatus'), 'beneficiaries', ['maritalstatus'], unique=False)
    op.create_index(op.f('ix_beneficiaries_membertype'), 'beneficiaries', ['membertype'], unique=False)
    op.create_index(op.f('ix_beneficiaries_middlename'), 'beneficiaries', ['middlename'], unique=False)
    op.create_index(op.f('ix_beneficiaries_policyid'), 'beneficiaries', ['policyid'], unique=False)
    op.create_index(op.f('ix_beneficiaries_relationship'), 'beneficiaries', ['relationship'], unique=False)
    op.create_index(op.f('ix_beneficiaries_status'), 'beneficiaries', ['status'], unique=False)
    op.create_index(op.f('ix_beneficiaries_surname'), 'beneficiaries', ['surname'], unique=False)
    op.create_index(op.f('ix_beneficiaries_title'), 'beneficiaries', ['title'], unique=False)
    op.create_table('policy',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyno', sa.String(), nullable=True),
    sa.Column('packages', sa.String(), nullable=True),
    sa.Column('bus', sa.String(), nullable=True),
    sa.Column('agentcode', sa.String(), nullable=True),
    sa.Column('agentname', sa.String(), nullable=True),
    sa.Column('pep', sa.Boolean(), nullable=True),
    sa.Column('aml', sa.Boolean(), nullable=True),
    sa.Column('applicantsigneddate', sa.String(), nullable=True),
    sa.Column('agentsigneddate', sa.String(), nullable=True),
    sa.Column('applicationform', sa.String(), nullable=True),
    sa.Column('kycform', sa.String(), nullable=True),
    sa.Column('mandateform', sa.String(), nullable=True),
    sa.Column('schemecode', sa.String(), nullable=True),
    sa.Column('worksitecode', sa.String(), nullable=True),
    sa.Column('riskprofile', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('datecreated', sa.String(), nullable=True),
    sa.Column('createdby', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_policy_agentcode'), 'policy', ['agentcode'], unique=False)
    op.create_index(op.f('ix_policy_agentname'), 'policy', ['agentname'], unique=False)
    op.create_index(op.f('ix_policy_agentsigneddate'), 'policy', ['agentsigneddate'], unique=False)
    op.create_index(op.f('ix_policy_applicantsigneddate'), 'policy', ['applicantsigneddate'], unique=False)
    op.create_index(op.f('ix_policy_applicationform'), 'policy', ['applicationform'], unique=False)
    op.create_index(op.f('ix_policy_bus'), 'policy', ['bus'], unique=False)
    op.create_index(op.f('ix_policy_createdby'), 'policy', ['createdby'], unique=False)
    op.create_index(op.f('ix_policy_datecreated'), 'policy', ['datecreated'], unique=False)
    op.create_index('ix_policy_id', 'policy', ['id'], unique=False)
    op.create_index(op.f('ix_policy_kycform'), 'policy', ['kycform'], unique=False)
    op.create_index(op.f('ix_policy_mandateform'), 'policy', ['mandateform'], unique=False)
    op.create_index(op.f('ix_policy_packages'), 'policy', ['packages'], unique=False)
    op.create_index('ix_policy_policyno', 'policy', ['policyno'], unique=False)
    op.create_index(op.f('ix_policy_riskprofile'), 'policy', ['riskprofile'], unique=False)
    op.create_index(op.f('ix_policy_schemecode'), 'policy', ['schemecode'], unique=False)
    op.create_index(op.f('ix_policy_status'), 'policy', ['status'], unique=False)
    op.create_index(op.f('ix_policy_worksitecode'), 'policy', ['worksitecode'], unique=False)
    op.create_table('policyactivities',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyid', sa.String(), nullable=True),
    sa.Column('policyno', sa.String(), nullable=True),
    sa.Column('activitydesc', sa.String(), nullable=True),
    sa.Column('remarks', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('datecreated', sa.String(), nullable=True),
    sa.Column('createdby', sa.Integer(), nullable=True),
    sa.Column('activitydetails', sa.String(), nullable=True),
    sa.Column('effectivedate', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_policyactivities_activitydesc'), 'policyactivities', ['activitydesc'], unique=False)
    op.create_index(op.f('ix_policyactivities_activitydetails'), 'policyactivities', ['activitydetails'], unique=False)
    op.create_index(op.f('ix_policyactivities_createdby'), 'policyactivities', ['createdby'], unique=False)
    op.create_index(op.f('ix_policyactivities_datecreated'), 'policyactivities', ['datecreated'], unique=False)
    op.create_index(op.f('ix_policyactivities_effectivedate'), 'policyactivities', ['effectivedate'], unique=False)
    op.create_index(op.f('ix_policyactivities_id'), 'policyactivities', ['id'], unique=False)
    op.create_index(op.f('ix_policyactivities_policyid'), 'policyactivities', ['policyid'], unique=False)
    op.create_index('ix_policyactivities_policyno', 'policyactivities', ['policyno'], unique=False)
    op.create_index(op.f('ix_policyactivities_remarks'), 'policyactivities', ['remarks'], unique=False)
    op.create_index(op.f('ix_policyactivities_status'), 'policyactivities', ['status'], unique=False)
    op.create_table('policyholder',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyno', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('initials', sa.String(), nullable=True),
    sa.Column('middlename', sa.String(), nullable=True),
    sa.Column('surname', sa.String(), nullable=True),
    sa.Column('previousname', sa.String(), nullable=True),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('dateofbirth', sa.String(), nullable=True),
    sa.Column('countryofbirth', sa.String(), nullable=True),
    sa.Column('maritalstatus', sa.String(), nullable=True),
    sa.Column('residentialstatus', sa.String(), nullable=True),
    sa.Column('primaryid', sa.String(), nullable=True),
    sa.Column('primaryidexpirydate', sa.String(), nullable=True),
    sa.Column('secondaryid', sa.String(), nullable=True),
    sa.Column('secondaryidexpirydate', sa.String(), nullable=True),
    sa.Column('expatriate', sa.String(), nullable=True),
    sa.Column('workpermit', sa.String(), nullable=True),
    sa.Column('workpermitexpirydate', sa.String(), nullable=True),
    sa.Column('sourceofincome', sa.String(), nullable=True),
    sa.Column('netmonthlyincome', sa.String(), nullable=True),
    sa.Column('specifyincome', sa.String(), nullable=True),
    sa.Column('proofofsourceoffunds', sa.String(), nullable=True),
    sa.Column('specifyproofofsourceoffunds', sa.String(), nullable=True),
    sa.Column('occupation', sa.String(), nullable=True),
    sa.Column('industry', sa.String(), nullable=True),
    sa.Column('employer', sa.String(), nullable=True),
    sa.Column('other', sa.String(), nullable=True),
    sa.Column('commpreference', sa.String(), nullable=True),
    sa.Column('emailaddress', sa.String(), nullable=True),
    sa.Column('phoneno', sa.String(), nullable=True),
    sa.Column('altmobileno', sa.String(), nullable=True),
    sa.Column('physicaladdressline1', sa.String(), nullable=True),
    sa.Column('physicaladdressline2', sa.String(), nullable=True),
    sa.Column('physicaladdressline3', sa.String(), nullable=True),
    sa.Column('physicaladdressline4', sa.String(), nullable=True),
    sa.Column('physicaladdressline5city', sa.String(), nullable=True),
    sa.Column('physicaladdressline6country', sa.String(), nullable=True),
    sa.Column('permanentaddressvillage', sa.String(), nullable=True),
    sa.Column('permanentaddressta', sa.String(), nullable=True),
    sa.Column('permanentaddressdistrict', sa.String(), nullable=True),
    sa.Column('postaladdressline1', sa.String(), nullable=True),
    sa.Column('postaladdressline2', sa.String(), nullable=True),
    sa.Column('postaladdressline3', sa.String(), nullable=True),
    sa.Column('postaladdressline4', sa.String(), nullable=True),
    sa.Column('postaladdressline5city', sa.String(), nullable=True),
    sa.Column('postaladdressline6country', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_policyholder_altmobileno'), 'policyholder', ['altmobileno'], unique=False)
    op.create_index(op.f('ix_policyholder_commpreference'), 'policyholder', ['commpreference'], unique=False)
    op.create_index(op.f('ix_policyholder_countryofbirth'), 'policyholder', ['countryofbirth'], unique=False)
    op.create_index(op.f('ix_policyholder_dateofbirth'), 'policyholder', ['dateofbirth'], unique=False)
    op.create_index(op.f('ix_policyholder_emailaddress'), 'policyholder', ['emailaddress'], unique=False)
    op.create_index(op.f('ix_policyholder_employer'), 'policyholder', ['employer'], unique=False)
    op.create_index(op.f('ix_policyholder_expatriate'), 'policyholder', ['expatriate'], unique=False)
    op.create_index(op.f('ix_policyholder_gender'), 'policyholder', ['gender'], unique=False)
    op.create_index('ix_policyholder_id', 'policyholder', ['id'], unique=False)
    op.create_index(op.f('ix_policyholder_industry'), 'policyholder', ['industry'], unique=False)
    op.create_index(op.f('ix_policyholder_initials'), 'policyholder', ['initials'], unique=False)
    op.create_index(op.f('ix_policyholder_maritalstatus'), 'policyholder', ['maritalstatus'], unique=False)
    op.create_index(op.f('ix_policyholder_middlename'), 'policyholder', ['middlename'], unique=False)
    op.create_index(op.f('ix_policyholder_netmonthlyincome'), 'policyholder', ['netmonthlyincome'], unique=False)
    op.create_index(op.f('ix_policyholder_occupation'), 'policyholder', ['occupation'], unique=False)
    op.create_index(op.f('ix_policyholder_other'), 'policyholder', ['other'], unique=False)
    op.create_index(op.f('ix_policyholder_permanentaddressdistrict'), 'policyholder', ['permanentaddressdistrict'], unique=False)
    op.create_index(op.f('ix_policyholder_permanentaddressta'), 'policyholder', ['permanentaddressta'], unique=False)
    op.create_index(op.f('ix_policyholder_permanentaddressvillage'), 'policyholder', ['permanentaddressvillage'], unique=False)
    op.create_index(op.f('ix_policyholder_phoneno'), 'policyholder', ['phoneno'], unique=False)
    op.create_index(op.f('ix_policyholder_physicaladdressline1'), 'policyholder', ['physicaladdressline1'], unique=False)
    op.create_index(op.f('ix_policyholder_physicaladdressline2'), 'policyholder', ['physicaladdressline2'], unique=False)
    op.create_index(op.f('ix_policyholder_physicaladdressline3'), 'policyholder', ['physicaladdressline3'], unique=False)
    op.create_index(op.f('ix_policyholder_physicaladdressline4'), 'policyholder', ['physicaladdressline4'], unique=False)
    op.create_index(op.f('ix_policyholder_physicaladdressline5city'), 'policyholder', ['physicaladdressline5city'], unique=False)
    op.create_index(op.f('ix_policyholder_physicaladdressline6country'), 'policyholder', ['physicaladdressline6country'], unique=False)
    op.create_index(op.f('ix_policyholder_policyno'), 'policyholder', ['policyno'], unique=False)
    op.create_index(op.f('ix_policyholder_postaladdressline1'), 'policyholder', ['postaladdressline1'], unique=False)
    op.create_index(op.f('ix_policyholder_postaladdressline2'), 'policyholder', ['postaladdressline2'], unique=False)
    op.create_index(op.f('ix_policyholder_postaladdressline3'), 'policyholder', ['postaladdressline3'], unique=False)
    op.create_index(op.f('ix_policyholder_postaladdressline4'), 'policyholder', ['postaladdressline4'], unique=False)
    op.create_index(op.f('ix_policyholder_postaladdressline5city'), 'policyholder', ['postaladdressline5city'], unique=False)
    op.create_index(op.f('ix_policyholder_postaladdressline6country'), 'policyholder', ['postaladdressline6country'], unique=False)
    op.create_index(op.f('ix_policyholder_previousname'), 'policyholder', ['previousname'], unique=False)
    op.create_index(op.f('ix_policyholder_primaryid'), 'policyholder', ['primaryid'], unique=False)
    op.create_index(op.f('ix_policyholder_primaryidexpirydate'), 'policyholder', ['primaryidexpirydate'], unique=False)
    op.create_index(op.f('ix_policyholder_proofofsourceoffunds'), 'policyholder', ['proofofsourceoffunds'], unique=False)
    op.create_index(op.f('ix_policyholder_residentialstatus'), 'policyholder', ['residentialstatus'], unique=False)
    op.create_index(op.f('ix_policyholder_secondaryid'), 'policyholder', ['secondaryid'], unique=False)
    op.create_index(op.f('ix_policyholder_secondaryidexpirydate'), 'policyholder', ['secondaryidexpirydate'], unique=False)
    op.create_index(op.f('ix_policyholder_sourceofincome'), 'policyholder', ['sourceofincome'], unique=False)
    op.create_index(op.f('ix_policyholder_specifyincome'), 'policyholder', ['specifyincome'], unique=False)
    op.create_index(op.f('ix_policyholder_specifyproofofsourceoffunds'), 'policyholder', ['specifyproofofsourceoffunds'], unique=False)
    op.create_index(op.f('ix_policyholder_surname'), 'policyholder', ['surname'], unique=False)
    op.create_index(op.f('ix_policyholder_title'), 'policyholder', ['title'], unique=False)
    op.create_index(op.f('ix_policyholder_workpermit'), 'policyholder', ['workpermit'], unique=False)
    op.create_index(op.f('ix_policyholder_workpermitexpirydate'), 'policyholder', ['workpermitexpirydate'], unique=False)
    op.create_table('policymandate',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyno', sa.String(), nullable=True),
    sa.Column('modeofpayment', sa.String(), nullable=True),
    sa.Column('frequency', sa.String(), nullable=True),
    sa.Column('premium', sa.String(), nullable=True),
    sa.Column('firstdeductiondate', sa.String(), nullable=True),
    sa.Column('debitorderFirstname', sa.String(), nullable=True),
    sa.Column('debitorderLastname', sa.String(), nullable=True),
    sa.Column('debitorderNameofbank', sa.String(), nullable=True),
    sa.Column('debitorderBranch', sa.String(), nullable=True),
    sa.Column('debitorderAccountno', sa.String(), nullable=True),
    sa.Column('debitorderTypeofaccount', sa.String(), nullable=True),
    sa.Column('debitorderSalaryfundingdate', sa.String(), nullable=True),
    sa.Column('debitorderPremiumonapplication', sa.String(), nullable=True),
    sa.Column('stoporderEmployeeid', sa.String(), nullable=True),
    sa.Column('stoporderEmployeename', sa.String(), nullable=True),
    sa.Column('stoporderEmployername', sa.String(), nullable=True),
    sa.Column('stoporderEmployeraddress', sa.String(), nullable=True),
    sa.Column('mobileoperator', sa.String(), nullable=True),
    sa.Column('mobilephoneno', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_policymandate_debitorderAccountno'), 'policymandate', ['debitorderAccountno'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderBranch'), 'policymandate', ['debitorderBranch'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderFirstname'), 'policymandate', ['debitorderFirstname'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderLastname'), 'policymandate', ['debitorderLastname'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderNameofbank'), 'policymandate', ['debitorderNameofbank'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderPremiumonapplication'), 'policymandate', ['debitorderPremiumonapplication'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderSalaryfundingdate'), 'policymandate', ['debitorderSalaryfundingdate'], unique=False)
    op.create_index(op.f('ix_policymandate_debitorderTypeofaccount'), 'policymandate', ['debitorderTypeofaccount'], unique=False)
    op.create_index(op.f('ix_policymandate_firstdeductiondate'), 'policymandate', ['firstdeductiondate'], unique=False)
    op.create_index(op.f('ix_policymandate_frequency'), 'policymandate', ['frequency'], unique=False)
    op.create_index('ix_policymandate_id', 'policymandate', ['id'], unique=False)
    op.create_index(op.f('ix_policymandate_mobileoperator'), 'policymandate', ['mobileoperator'], unique=False)
    op.create_index(op.f('ix_policymandate_mobilephoneno'), 'policymandate', ['mobilephoneno'], unique=False)
    op.create_index(op.f('ix_policymandate_modeofpayment'), 'policymandate', ['modeofpayment'], unique=False)
    op.create_index('ix_policymandate_policyno', 'policymandate', ['policyno'], unique=False)
    op.create_index(op.f('ix_policymandate_premium'), 'policymandate', ['premium'], unique=False)
    op.create_index(op.f('ix_policymandate_stoporderEmployeeid'), 'policymandate', ['stoporderEmployeeid'], unique=False)
    op.create_index(op.f('ix_policymandate_stoporderEmployeename'), 'policymandate', ['stoporderEmployeename'], unique=False)
    op.create_index(op.f('ix_policymandate_stoporderEmployeraddress'), 'policymandate', ['stoporderEmployeraddress'], unique=False)
    op.create_index(op.f('ix_policymandate_stoporderEmployername'), 'policymandate', ['stoporderEmployername'], unique=False)
    op.create_table('policysummary',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyno', sa.String(), nullable=True),
    sa.Column('premiumsdue', sa.String(), nullable=True),
    sa.Column('premiumspaid', sa.String(), nullable=True),
    sa.Column('totalpremiumspaid', sa.String(), nullable=True),
    sa.Column('outstanding', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_policysummary_id'), 'policysummary', ['id'], unique=False)
    op.create_index(op.f('ix_policysummary_outstanding'), 'policysummary', ['outstanding'], unique=False)
    op.create_index('ix_policysummary_policyno', 'policysummary', ['policyno'], unique=False)
    op.create_index(op.f('ix_policysummary_premiumsdue'), 'policysummary', ['premiumsdue'], unique=False)
    op.create_index(op.f('ix_policysummary_premiumspaid'), 'policysummary', ['premiumspaid'], unique=False)
    op.create_index(op.f('ix_policysummary_totalpremiumspaid'), 'policysummary', ['totalpremiumspaid'], unique=False)
    op.create_table('premiumpayer',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('policyno', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('initials', sa.String(), nullable=True),
    sa.Column('middlename', sa.String(), nullable=True),
    sa.Column('surname', sa.String(), nullable=True),
    sa.Column('previousname', sa.String(), nullable=True),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('dateofbirth', sa.String(), nullable=True),
    sa.Column('countryofbirth', sa.String(), nullable=True),
    sa.Column('maritalstatus', sa.String(), nullable=True),
    sa.Column('residentialstatus', sa.String(), nullable=True),
    sa.Column('primaryid', sa.String(), nullable=True),
    sa.Column('primaryidexpirydate', sa.String(), nullable=True),
    sa.Column('secondaryid', sa.String(), nullable=True),
    sa.Column('secondaryidexpirydate', sa.String(), nullable=True),
    sa.Column('expatriate', sa.String(), nullable=True),
    sa.Column('workpermit', sa.String(), nullable=True),
    sa.Column('workpermitexpirydate', sa.String(), nullable=True),
    sa.Column('sourceofincome', sa.String(), nullable=True),
    sa.Column('netmonthlyincome', sa.String(), nullable=True),
    sa.Column('specifyincome', sa.String(), nullable=True),
    sa.Column('proofofsourceoffunds', sa.String(), nullable=True),
    sa.Column('specifyproofofsourceoffunds', sa.String(), nullable=True),
    sa.Column('occupation', sa.String(), nullable=True),
    sa.Column('industry', sa.String(), nullable=True),
    sa.Column('employer', sa.String(), nullable=True),
    sa.Column('other', sa.String(), nullable=True),
    sa.Column('commpreference', sa.String(), nullable=True),
    sa.Column('emailaddress', sa.String(), nullable=True),
    sa.Column('phoneno', sa.String(), nullable=True),
    sa.Column('altmobileno', sa.String(), nullable=True),
    sa.Column('physicaladdressline1', sa.String(), nullable=True),
    sa.Column('physicaladdressline2', sa.String(), nullable=True),
    sa.Column('physicaladdressline3', sa.String(), nullable=True),
    sa.Column('physicaladdressline4', sa.String(), nullable=True),
    sa.Column('physicaladdressline5city', sa.String(), nullable=True),
    sa.Column('physicaladdressline6country', sa.String(), nullable=True),
    sa.Column('permanentaddressvillage', sa.String(), nullable=True),
    sa.Column('permanentaddressta', sa.String(), nullable=True),
    sa.Column('permanentaddressdistrict', sa.String(), nullable=True),
    sa.Column('postaladdressline1', sa.String(), nullable=True),
    sa.Column('postaladdressline2', sa.String(), nullable=True),
    sa.Column('postaladdressline3', sa.String(), nullable=True),
    sa.Column('postaladdressline4', sa.String(), nullable=True),
    sa.Column('postaladdressline5city', sa.String(), nullable=True),
    sa.Column('postaladdressline6country', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_premiumpayer_altmobileno'), 'premiumpayer', ['altmobileno'], unique=False)
    op.create_index(op.f('ix_premiumpayer_commpreference'), 'premiumpayer', ['commpreference'], unique=False)
    op.create_index(op.f('ix_premiumpayer_countryofbirth'), 'premiumpayer', ['countryofbirth'], unique=False)
    op.create_index(op.f('ix_premiumpayer_dateofbirth'), 'premiumpayer', ['dateofbirth'], unique=False)
    op.create_index(op.f('ix_premiumpayer_emailaddress'), 'premiumpayer', ['emailaddress'], unique=False)
    op.create_index(op.f('ix_premiumpayer_employer'), 'premiumpayer', ['employer'], unique=False)
    op.create_index(op.f('ix_premiumpayer_expatriate'), 'premiumpayer', ['expatriate'], unique=False)
    op.create_index(op.f('ix_premiumpayer_gender'), 'premiumpayer', ['gender'], unique=False)
    op.create_index(op.f('ix_premiumpayer_id'), 'premiumpayer', ['id'], unique=False)
    op.create_index(op.f('ix_premiumpayer_industry'), 'premiumpayer', ['industry'], unique=False)
    op.create_index(op.f('ix_premiumpayer_initials'), 'premiumpayer', ['initials'], unique=False)
    op.create_index(op.f('ix_premiumpayer_maritalstatus'), 'premiumpayer', ['maritalstatus'], unique=False)
    op.create_index(op.f('ix_premiumpayer_middlename'), 'premiumpayer', ['middlename'], unique=False)
    op.create_index(op.f('ix_premiumpayer_netmonthlyincome'), 'premiumpayer', ['netmonthlyincome'], unique=False)
    op.create_index(op.f('ix_premiumpayer_occupation'), 'premiumpayer', ['occupation'], unique=False)
    op.create_index(op.f('ix_premiumpayer_other'), 'premiumpayer', ['other'], unique=False)
    op.create_index(op.f('ix_premiumpayer_permanentaddressdistrict'), 'premiumpayer', ['permanentaddressdistrict'], unique=False)
    op.create_index(op.f('ix_premiumpayer_permanentaddressta'), 'premiumpayer', ['permanentaddressta'], unique=False)
    op.create_index(op.f('ix_premiumpayer_permanentaddressvillage'), 'premiumpayer', ['permanentaddressvillage'], unique=False)
    op.create_index(op.f('ix_premiumpayer_phoneno'), 'premiumpayer', ['phoneno'], unique=False)
    op.create_index(op.f('ix_premiumpayer_physicaladdressline1'), 'premiumpayer', ['physicaladdressline1'], unique=False)
    op.create_index(op.f('ix_premiumpayer_physicaladdressline2'), 'premiumpayer', ['physicaladdressline2'], unique=False)
    op.create_index(op.f('ix_premiumpayer_physicaladdressline3'), 'premiumpayer', ['physicaladdressline3'], unique=False)
    op.create_index(op.f('ix_premiumpayer_physicaladdressline4'), 'premiumpayer', ['physicaladdressline4'], unique=False)
    op.create_index(op.f('ix_premiumpayer_physicaladdressline5city'), 'premiumpayer', ['physicaladdressline5city'], unique=False)
    op.create_index(op.f('ix_premiumpayer_physicaladdressline6country'), 'premiumpayer', ['physicaladdressline6country'], unique=False)
    op.create_index(op.f('ix_premiumpayer_policyno'), 'premiumpayer', ['policyno'], unique=False)
    op.create_index(op.f('ix_premiumpayer_postaladdressline1'), 'premiumpayer', ['postaladdressline1'], unique=False)
    op.create_index(op.f('ix_premiumpayer_postaladdressline2'), 'premiumpayer', ['postaladdressline2'], unique=False)
    op.create_index(op.f('ix_premiumpayer_postaladdressline3'), 'premiumpayer', ['postaladdressline3'], unique=False)
    op.create_index(op.f('ix_premiumpayer_postaladdressline4'), 'premiumpayer', ['postaladdressline4'], unique=False)
    op.create_index(op.f('ix_premiumpayer_postaladdressline5city'), 'premiumpayer', ['postaladdressline5city'], unique=False)
    op.create_index(op.f('ix_premiumpayer_postaladdressline6country'), 'premiumpayer', ['postaladdressline6country'], unique=False)
    op.create_index(op.f('ix_premiumpayer_previousname'), 'premiumpayer', ['previousname'], unique=False)
    op.create_index(op.f('ix_premiumpayer_primaryid'), 'premiumpayer', ['primaryid'], unique=False)
    op.create_index(op.f('ix_premiumpayer_primaryidexpirydate'), 'premiumpayer', ['primaryidexpirydate'], unique=False)
    op.create_index(op.f('ix_premiumpayer_proofofsourceoffunds'), 'premiumpayer', ['proofofsourceoffunds'], unique=False)
    op.create_index(op.f('ix_premiumpayer_residentialstatus'), 'premiumpayer', ['residentialstatus'], unique=False)
    op.create_index(op.f('ix_premiumpayer_secondaryid'), 'premiumpayer', ['secondaryid'], unique=False)
    op.create_index(op.f('ix_premiumpayer_secondaryidexpirydate'), 'premiumpayer', ['secondaryidexpirydate'], unique=False)
    op.create_index(op.f('ix_premiumpayer_sourceofincome'), 'premiumpayer', ['sourceofincome'], unique=False)
    op.create_index(op.f('ix_premiumpayer_specifyincome'), 'premiumpayer', ['specifyincome'], unique=False)
    op.create_index(op.f('ix_premiumpayer_specifyproofofsourceoffunds'), 'premiumpayer', ['specifyproofofsourceoffunds'], unique=False)
    op.create_index(op.f('ix_premiumpayer_surname'), 'premiumpayer', ['surname'], unique=False)
    op.create_index(op.f('ix_premiumpayer_title'), 'premiumpayer', ['title'], unique=False)
    op.create_index(op.f('ix_premiumpayer_workpermit'), 'premiumpayer', ['workpermit'], unique=False)
    op.create_index(op.f('ix_premiumpayer_workpermitexpirydate'), 'premiumpayer', ['workpermitexpirydate'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_premiumpayer_workpermitexpirydate'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_workpermit'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_title'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_surname'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_specifyproofofsourceoffunds'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_specifyincome'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_sourceofincome'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_secondaryidexpirydate'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_secondaryid'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_residentialstatus'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_proofofsourceoffunds'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_primaryidexpirydate'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_primaryid'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_previousname'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_postaladdressline6country'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_postaladdressline5city'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_postaladdressline4'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_postaladdressline3'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_postaladdressline2'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_postaladdressline1'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_policyno'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_physicaladdressline6country'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_physicaladdressline5city'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_physicaladdressline4'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_physicaladdressline3'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_physicaladdressline2'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_physicaladdressline1'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_phoneno'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_permanentaddressvillage'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_permanentaddressta'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_permanentaddressdistrict'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_other'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_occupation'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_netmonthlyincome'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_middlename'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_maritalstatus'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_initials'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_industry'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_id'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_gender'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_expatriate'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_employer'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_emailaddress'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_dateofbirth'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_countryofbirth'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_commpreference'), table_name='premiumpayer')
    op.drop_index(op.f('ix_premiumpayer_altmobileno'), table_name='premiumpayer')
    op.drop_table('premiumpayer')
    op.drop_index(op.f('ix_policysummary_totalpremiumspaid'), table_name='policysummary')
    op.drop_index(op.f('ix_policysummary_premiumspaid'), table_name='policysummary')
    op.drop_index(op.f('ix_policysummary_premiumsdue'), table_name='policysummary')
    op.drop_index('ix_policysummary_policyno', table_name='policysummary')
    op.drop_index(op.f('ix_policysummary_outstanding'), table_name='policysummary')
    op.drop_index(op.f('ix_policysummary_id'), table_name='policysummary')
    op.drop_table('policysummary')
    op.drop_index(op.f('ix_policymandate_stoporderEmployername'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_stoporderEmployeraddress'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_stoporderEmployeename'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_stoporderEmployeeid'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_premium'), table_name='policymandate')
    op.drop_index('ix_policymandate_policyno', table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_modeofpayment'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_mobilephoneno'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_mobileoperator'), table_name='policymandate')
    op.drop_index('ix_policymandate_id', table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_frequency'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_firstdeductiondate'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderTypeofaccount'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderSalaryfundingdate'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderPremiumonapplication'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderNameofbank'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderLastname'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderFirstname'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderBranch'), table_name='policymandate')
    op.drop_index(op.f('ix_policymandate_debitorderAccountno'), table_name='policymandate')
    op.drop_table('policymandate')
    op.drop_index(op.f('ix_policyholder_workpermitexpirydate'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_workpermit'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_title'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_surname'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_specifyproofofsourceoffunds'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_specifyincome'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_sourceofincome'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_secondaryidexpirydate'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_secondaryid'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_residentialstatus'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_proofofsourceoffunds'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_primaryidexpirydate'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_primaryid'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_previousname'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postaladdressline6country'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postaladdressline5city'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postaladdressline4'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postaladdressline3'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postaladdressline2'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_postaladdressline1'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_policyno'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_physicaladdressline6country'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_physicaladdressline5city'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_physicaladdressline4'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_physicaladdressline3'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_physicaladdressline2'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_physicaladdressline1'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_phoneno'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_permanentaddressvillage'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_permanentaddressta'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_permanentaddressdistrict'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_other'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_occupation'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_netmonthlyincome'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_middlename'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_maritalstatus'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_initials'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_industry'), table_name='policyholder')
    op.drop_index('ix_policyholder_id', table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_gender'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_expatriate'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_employer'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_emailaddress'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_dateofbirth'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_countryofbirth'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_commpreference'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_altmobileno'), table_name='policyholder')
    op.drop_table('policyholder')
    op.drop_index(op.f('ix_policyactivities_status'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_remarks'), table_name='policyactivities')
    op.drop_index('ix_policyactivities_policyno', table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_policyid'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_id'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_effectivedate'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_datecreated'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_createdby'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_activitydetails'), table_name='policyactivities')
    op.drop_index(op.f('ix_policyactivities_activitydesc'), table_name='policyactivities')
    op.drop_table('policyactivities')
    op.drop_index(op.f('ix_policy_worksitecode'), table_name='policy')
    op.drop_index(op.f('ix_policy_status'), table_name='policy')
    op.drop_index(op.f('ix_policy_schemecode'), table_name='policy')
    op.drop_index(op.f('ix_policy_riskprofile'), table_name='policy')
    op.drop_index('ix_policy_policyno', table_name='policy')
    op.drop_index(op.f('ix_policy_packages'), table_name='policy')
    op.drop_index(op.f('ix_policy_mandateform'), table_name='policy')
    op.drop_index(op.f('ix_policy_kycform'), table_name='policy')
    op.drop_index('ix_policy_id', table_name='policy')
    op.drop_index(op.f('ix_policy_datecreated'), table_name='policy')
    op.drop_index(op.f('ix_policy_createdby'), table_name='policy')
    op.drop_index(op.f('ix_policy_bus'), table_name='policy')
    op.drop_index(op.f('ix_policy_applicationform'), table_name='policy')
    op.drop_index(op.f('ix_policy_applicantsigneddate'), table_name='policy')
    op.drop_index(op.f('ix_policy_agentsigneddate'), table_name='policy')
    op.drop_index(op.f('ix_policy_agentname'), table_name='policy')
    op.drop_index(op.f('ix_policy_agentcode'), table_name='policy')
    op.drop_table('policy')
    op.drop_index(op.f('ix_beneficiaries_title'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_surname'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_status'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_relationship'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_policyid'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_middlename'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_membertype'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_maritalstatus'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_initials'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_id'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_gender'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_firstname'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_datecreated'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_createdby'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_birthdate'), table_name='beneficiaries')
    op.drop_index(op.f('ix_beneficiaries_benofownership'), table_name='beneficiaries')
    op.drop_index('ix_ben_policyid', table_name='beneficiaries')
    op.drop_index('ix_ben_id', table_name='beneficiaries')
    op.drop_table('beneficiaries')
    # ### end Alembic commands ###
