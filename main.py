from fastapi import FastAP<PERSON>,Depends, HTTPException,status
import services, models, schema
from db import get_db, Base
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Annotated
from fastapi.security import <PERSON>A<PERSON>2P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from passlib.context import CryptContext
from auth import hash_password, verify_password, create_access_token, decode_access_token, get_current_user
from datetime import timedelta
from routers import activities, beneficiaries, policy, users, payer,policyholder, policysummary, policymandate, imports, policy_management
import uvicorn

app = FastAPI()
security = HTTPBearer()

db_dependency = Annotated[Session, Depends(get_db)]
# user_dependency = Annotated[dict, Depends(get_current_user)]

# Setting up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(activities.router)
app.include_router(users.router)
app.include_router(payer.router)
app.include_router(policyholder.router)
app.include_router(policysummary.router)
app.include_router(policymandate.router)
app.include_router(beneficiaries.router)
app.include_router(policy.router)
app.include_router(imports.router)
app.include_router(policy_management.router)

# Department CRUD operations with Authentication
@app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# signup user
@app.post("/signup", response_model=schema.UserBase)
def signup(user: schema.UserCreate, db: Session = Depends(get_db)):
    hashed_password = hash_password(user.password)
    user.password = hashed_password
    user_model = models.User(**user.model_dump())
    return services.create_user(db, user_model)

# login user
@app.post("/login")
def login(user: schema.Userlogin, db: Session = Depends(get_db)):
    db_user = db.query(models.User).filter(models.User.username == user.username).first()
    if not db_user:
        raise HTTPException(status_code=400, detail="1. Invalid username")

    if not verify_password(user.password, db_user.password if isinstance(db_user.password, str) else db_user.password.value):
        raise HTTPException(status_code=400, detail="2. Invalid password")

    access_token = create_access_token(data={"sub": db_user.username, "id": db_user.id, "username": db_user.username}, expires_delta=timedelta(minutes=30))
    return {"access_token": access_token, "token_type": "bearer", "user": db_user}

# logout user
@app.post("/logout")
def logout(token: HTTPAuthorizationCredentials = Depends(security)):
    # Verify the token is valid before logout
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")

    # Since JWT tokens are stateless, we just return a success message
    # The client should remove the token from storage
    return {"message": "Successfully logged out", "status": "success"}