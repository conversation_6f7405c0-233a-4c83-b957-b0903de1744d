from fastapi import FastAPI, Response, status, HTTPException, Depends, APIRouter
from fastapi.security import <PERSON>A<PERSON>2P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from auth import hash_password, verify_password, create_access_token, decode_access_token
from sqlalchemy.orm import Session
from typing import List, Optional

from sqlalchemy import func
# from sqlalchemy.sql.functions import func
import models, schema, auth
from db import get_db

security = HTTPBearer()

router = APIRouter(
    prefix="/policymandate",
    tags=['Policymandate'],
)

# @app.get("/checktoken/")
def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
    user_data = decode_access_token(token.credentials)
    if not user_data:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    return user_data

# get policymandates
@router.get("/", response_model=List[schema.PolicyMandate])
def get_policymandates(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policymandates = db.query(models.PolicyMandate).offset(skip).limit(limit).all()
    if not policymandates:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policymandates found")
    return policymandates

# get policymandate
@router.get("/{policymandate_id}", response_model=schema.PolicyMandate)
def get_policymandate(policymandate_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policymandate = db.query(models.PolicyMandate).filter(models.PolicyMandate.id == policymandate_id).first()
    if not policymandate:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policymandate not found")
    return policymandate

# create policymandate
@router.post("/", response_model=schema.PolicyMandate, status_code=status.HTTP_201_CREATED)
def create_policymandate(policymandate: schema.PolicyMandate, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
# check if policy mandate with policy no exists
    existing_policymandate = db.query(models.PolicyMandate).filter(models.PolicyMandate.policyno == policymandate.policyno).first()
    if existing_policymandate:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Policymandate with this policy number already exists") 
    # Create the policymandate
    policymandate_data = models.PolicyMandate(**policymandate.model_dump(), createdby=current_user['id'])
    db.add(policymandate_data)
    db.commit()
    db.refresh(policymandate_data)
    return policymandate_data

# update policymandate
@router.put("/{policymandate_id}", response_model=schema.PolicyMandate)
def update_policymandate(policymandate_id: int, policymandate: schema.PolicyMandate, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policymandate = db.query(models.PolicyMandate).filter(models.PolicyMandate.id == policymandate_id).first()
    if not existing_policymandate:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policymandate not found")
    
    # Update the policymandate
    for key, value in policymandate.model_dump().items():
        setattr(existing_policymandate, key, value)
    existing_policymandate.updatedby = current_user['id']
    
    db.commit()
    db.refresh(existing_policymandate)
    return existing_policymandate

# delete policymandate
@router.delete("/{policymandate_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_policymandate(policymandate_id: int, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    existing_policymandate = db.query(models.PolicyMandate).filter(models.PolicyMandate.id == policymandate_id).first()
    if not existing_policymandate:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policymandate not found")
    
    db.delete(existing_policymandate)
    db.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)

# search policymandate by policy number
@router.get("/search/{policy_no}", response_model=List[schema.PolicyMandate])
def search_policymandate(policy_no: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policymandates = db.query(models.PolicyMandate).filter(models.PolicyMandate.policyno == policy_no).all()
    if not policymandates:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Policymandate not found")
    return policymandates

# search policymandate by modeofpayment
@router.get("/search/modeofpayment/{modeofpayment}", response_model=List[schema.PolicyMandate])
def search_policymandate_by_modeofpayment(modeofpayment: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policymandates = db.query(models.PolicyMandate).filter(func.lower(models.PolicyMandate.modeofpayment) == func.lower(modeofpayment)).all()
    if not policymandates:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policymandates found with the specified mode of payment")
    return policymandates   

# search policymandate by firstdeductiondate
@router.get("/search/firstdeductiondate/{firstdeductiondate}", response_model=List[schema.PolicyMandate])
def search_policymandate_by_firstdeductiondate(firstdeductiondate: str, db: Session = Depends(get_db), current_user: dict = Depends(get_current_user)):
    policymandates = db.query(models.PolicyMandate).filter(models.PolicyMandate.firstdeductiondate == firstdeductiondate).all()
    if not policymandates:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No policymandates found with the specified first deduction date")
    return policymandates