"""Add missing fields to PolicyHolder model

Revision ID: fca72f1a1fdd
Revises: 7aee5cd85a80
Create Date: 2025-08-04 19:17:01.017832

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fca72f1a1fdd'
down_revision: Union[str, Sequence[str], None] = '7aee5cd85a80'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('policyholder', sa.Column('firstname', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('datecreated', sa.String(), nullable=True))
    op.add_column('policyholder', sa.Column('createdby', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_policyholder_createdby'), 'policyholder', ['createdby'], unique=False)
    op.create_index(op.f('ix_policyholder_datecreated'), 'policyholder', ['datecreated'], unique=False)
    op.create_index(op.f('ix_policyholder_firstname'), 'policyholder', ['firstname'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_policyholder_firstname'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_datecreated'), table_name='policyholder')
    op.drop_index(op.f('ix_policyholder_createdby'), table_name='policyholder')
    op.drop_column('policyholder', 'createdby')
    op.drop_column('policyholder', 'datecreated')
    op.drop_column('policyholder', 'firstname')
    # ### end Alembic commands ###
