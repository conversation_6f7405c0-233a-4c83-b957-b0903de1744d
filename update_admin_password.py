#!/usr/bin/env python3
"""
Update admin user password to 'password'
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db import SessionLocal
from models import User
from auth import hash_password

def update_admin_password():
    """Update admin user password"""
    db = SessionLocal()
    try:
        # Find admin user
        admin_user = db.query(User).filter(User.username == "admin").first()
        
        if admin_user:
            # Update password to 'password'
            new_password = "password"
            hashed_password = hash_password(new_password)
            
            admin_user.hashed_password = hashed_password
            admin_user.password = hashed_password  # For backward compatibility
            
            db.commit()
            print(f"✅ Admin password updated successfully!")
            print(f"   Username: admin")
            print(f"   Password: {new_password}")
        else:
            print("❌ Admin user not found")
            
    except Exception as e:
        print(f"❌ Error updating admin password: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    update_admin_password()
