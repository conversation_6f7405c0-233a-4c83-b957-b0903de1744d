"""
Test configuration and fixtures for the Mthunzi Policy Management System
"""

import pytest
import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import app
from db import Base, get_db
from models import User, Policy, PolicyHolder, Beneficiaries
from auth import hash_password

# Test database URL - using the same database as main app for now
# In production, you should use a separate test database
TEST_DATABASE_URL = "postgresql+psycopg2://127.0.0.1:5432/mthunziauth?user=workmate2&password=workmate2025"

@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine"""
    engine = create_engine(TEST_DATABASE_URL)
    return engine

@pytest.fixture(scope="session")
def test_db_setup(test_engine):
    """Set up test database tables"""
    # Create all tables for testing
    Base.metadata.create_all(bind=test_engine)
    yield
    # Don't drop tables since we're using the main database

@pytest.fixture(scope="function")
def test_db_session(test_engine, test_db_setup):
    """Create a test database session"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    yield session
    session.close()

@pytest.fixture(scope="function")
def client(test_db_session):
    """Create test client with test database"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture
def test_user_data():
    """Sample user data for testing"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123",
        "status": "active",
        "viewonly": "no",
        "policyadmin": "yes",
        "servicing": "yes",
        "mimoadmin": "no",
        "sysadmin": "no",
        "createdby": "system",
        "datecreated": "2024-01-01 10:00:00"
    }

@pytest.fixture
def test_user(test_db_session, test_user_data):
    """Create a test user in the database"""
    hashed_password = hash_password(test_user_data["password"])
    user = User(
        username=test_user_data["username"],
        email=test_user_data["email"],
        hashed_password=hashed_password,
        password=hashed_password,
        status=test_user_data["status"],
        viewonly=test_user_data["viewonly"],
        policyadmin=test_user_data["policyadmin"],
        servicing=test_user_data["servicing"],
        mimoadmin=test_user_data["mimoadmin"],
        sysadmin=test_user_data["sysadmin"],
        createdby=test_user_data["createdby"],
        datecreated=test_user_data["datecreated"]
    )
    test_db_session.add(user)
    test_db_session.commit()
    test_db_session.refresh(user)
    return user

@pytest.fixture
def test_policy_data():
    """Sample policy data for testing"""
    return {
        "policyno": "TEST001",
        "packages": "Test Life Insurance",
        "bus": "Individual",
        "agentcode": "AGT999",
        "agentname": "Test Agent",
        "pep": False,
        "aml": True,
        "applicantsigneddate": "2024-01-01",
        "agentsigneddate": "2024-01-01",
        "applicationform": "completed",
        "kycform": "completed",
        "mandateform": "completed",
        "schemecode": "TEST001",
        "worksitecode": "WS999",
        "riskprofile": "Low",
        "status": "active",
        "datecreated": "2024-01-01",
        "createdby": 1
    }

@pytest.fixture
def test_policy(test_db_session, test_policy_data):
    """Create a test policy in the database"""
    policy = Policy(**test_policy_data)
    test_db_session.add(policy)
    test_db_session.commit()
    test_db_session.refresh(policy)
    return policy

@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for API requests"""
    login_data = {
        "username": test_user.username,
        "password": "testpass123"
    }
    response = client.post("/login", json=login_data)
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def admin_user(test_db_session):
    """Create an admin user for testing"""
    hashed_password = hash_password("admin123")
    admin = User(
        username="admin",
        email="<EMAIL>",
        hashed_password=hashed_password,
        password=hashed_password,
        status="active",
        viewonly="no",
        policyadmin="yes",
        servicing="yes",
        mimoadmin="yes",
        sysadmin="yes",
        createdby="system",
        datecreated="2024-01-01 10:00:00"
    )
    test_db_session.add(admin)
    test_db_session.commit()
    test_db_session.refresh(admin)
    return admin
