"""
Unit tests for authentication functionality
"""

import pytest
from datetime import datetime, timedelta
from auth import hash_password, verify_password, create_access_token, decode_access_token

class TestPasswordHashing:
    """Test password hashing and verification"""
    
    def test_hash_password(self):
        """Test password hashing"""
        password = "testpassword123"
        hashed = hash_password(password)
        
        assert hashed is not None
        assert hashed != password
        assert len(hashed) > 20  # Bcrypt hashes are typically longer

    def test_verify_password_correct(self):
        """Test password verification with correct password"""
        password = "testpassword123"
        hashed = hash_password(password)
        
        assert verify_password(password, hashed) is True

    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password"""
        password = "testpassword123"
        wrong_password = "wrongpassword"
        hashed = hash_password(password)
        
        assert verify_password(wrong_password, hashed) is False

    def test_hash_different_passwords_different_hashes(self):
        """Test that different passwords produce different hashes"""
        password1 = "password1"
        password2 = "password2"
        
        hash1 = hash_password(password1)
        hash2 = hash_password(password2)
        
        assert hash1 != hash2

    def test_same_password_different_hashes(self):
        """Test that same password produces different hashes (salt)"""
        password = "testpassword123"
        
        hash1 = hash_password(password)
        hash2 = hash_password(password)
        
        # Due to salt, hashes should be different
        assert hash1 != hash2
        # But both should verify correctly
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True

class TestJWTTokens:
    """Test JWT token creation and decoding"""
    
    def test_create_access_token(self):
        """Test access token creation"""
        data = {"sub": "testuser"}
        token = create_access_token(data)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 20

    def test_create_access_token_with_expiry(self):
        """Test access token creation with custom expiry"""
        data = {"sub": "testuser"}
        expires_delta = timedelta(minutes=60)
        token = create_access_token(data, expires_delta)
        
        assert token is not None
        assert isinstance(token, str)

    def test_decode_access_token_valid(self):
        """Test decoding valid access token"""
        data = {"sub": "testuser", "role": "admin"}
        token = create_access_token(data)
        
        decoded = decode_access_token(token)
        
        assert decoded is not None
        assert decoded["sub"] == "testuser"
        assert decoded["role"] == "admin"
        assert "exp" in decoded

    def test_decode_access_token_invalid(self):
        """Test decoding invalid access token"""
        invalid_token = "invalid.token.here"
        
        decoded = decode_access_token(invalid_token)
        
        assert decoded is None

    def test_decode_access_token_expired(self):
        """Test decoding expired access token"""
        data = {"sub": "testuser"}
        # Create token that expires immediately
        expires_delta = timedelta(seconds=-1)
        token = create_access_token(data, expires_delta)
        
        decoded = decode_access_token(token)
        
        assert decoded is None

    def test_token_contains_expiry(self):
        """Test that token contains expiry information"""
        data = {"sub": "testuser"}
        token = create_access_token(data)
        decoded = decode_access_token(token)
        
        assert "exp" in decoded
        assert isinstance(decoded["exp"], int)
        
        # Check that expiry is in the future
        current_time = datetime.now().timestamp()
        assert decoded["exp"] > current_time

class TestAuthIntegration:
    """Integration tests for authentication flow"""
    
    def test_full_auth_flow(self):
        """Test complete authentication flow"""
        # 1. Hash password
        original_password = "userpassword123"
        hashed_password = hash_password(original_password)
        
        # 2. Verify password
        assert verify_password(original_password, hashed_password) is True
        
        # 3. Create token
        user_data = {"sub": "testuser", "role": "user"}
        token = create_access_token(user_data)
        
        # 4. Decode token
        decoded_data = decode_access_token(token)
        
        assert decoded_data["sub"] == "testuser"
        assert decoded_data["role"] == "user"

    def test_auth_flow_with_wrong_password(self):
        """Test authentication flow with wrong password"""
        original_password = "userpassword123"
        wrong_password = "wrongpassword"
        hashed_password = hash_password(original_password)
        
        # Should fail password verification
        assert verify_password(wrong_password, hashed_password) is False

    def test_multiple_users_different_tokens(self):
        """Test that different users get different tokens"""
        user1_data = {"sub": "user1"}
        user2_data = {"sub": "user2"}
        
        token1 = create_access_token(user1_data)
        token2 = create_access_token(user2_data)
        
        assert token1 != token2
        
        decoded1 = decode_access_token(token1)
        decoded2 = decode_access_token(token2)
        
        assert decoded1["sub"] == "user1"
        assert decoded2["sub"] == "user2"
