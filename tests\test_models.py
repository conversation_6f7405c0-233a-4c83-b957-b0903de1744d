"""
Unit tests for database models
"""

import pytest
from models import User, Policy, PolicyHolder, Beneficiaries, PremiumPayer, PolicyMandate, PolicySummary, PolicyActivities

class TestUserModel:
    """Test User model"""
    
    def test_create_user(self, test_db_session):
        """Test creating a user"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            viewonly="no",
            policyadmin="yes",
            servicing="yes",
            mimoadmin="no",
            sysadmin="no",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.status == "active"

    def test_user_unique_username(self, test_db_session):
        """Test that username must be unique"""
        user1 = User(
            username="uniqueuser",
            email="<EMAIL>",
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        user2 = User(
            username="uniqueuser",  # Same username
            email="<EMAIL>",
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        test_db_session.add(user1)
        test_db_session.commit()
        
        test_db_session.add(user2)
        with pytest.raises(Exception):  # Should raise integrity error
            test_db_session.commit()

    def test_user_unique_email(self, test_db_session):
        """Test that email must be unique"""
        user1 = User(
            username="user1",
            email="<EMAIL>",
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        user2 = User(
            username="user2",
            email="<EMAIL>",  # Same email
            hashed_password="hashedpass",
            password="hashedpass",
            status="active",
            createdby="admin",
            datecreated="2024-01-01 10:00:00"
        )
        
        test_db_session.add(user1)
        test_db_session.commit()
        
        test_db_session.add(user2)
        with pytest.raises(Exception):  # Should raise integrity error
            test_db_session.commit()

class TestPolicyModel:
    """Test Policy model"""
    
    def test_create_policy(self, test_db_session):
        """Test creating a policy"""
        policy = Policy(
            policyno="POL123",
            packages="Life Insurance",
            bus="Individual",
            agentcode="AGT001",
            agentname="John Agent",
            pep=False,
            aml=True,
            status="active",
            datecreated="2024-01-01",
            createdby=1
        )
        
        test_db_session.add(policy)
        test_db_session.commit()
        test_db_session.refresh(policy)
        
        assert policy.id is not None
        assert policy.policyno == "POL123"
        assert policy.packages == "Life Insurance"
        assert policy.pep is False
        assert policy.aml is True

    def test_policy_boolean_fields(self, test_db_session):
        """Test policy boolean fields"""
        policy = Policy(
            policyno="POL124",
            pep=True,
            aml=False,
            status="active",
            datecreated="2024-01-01",
            createdby=1
        )
        
        test_db_session.add(policy)
        test_db_session.commit()
        test_db_session.refresh(policy)
        
        assert policy.pep is True
        assert policy.aml is False

class TestPolicyHolderModel:
    """Test PolicyHolder model"""
    
    def test_create_policyholder(self, test_db_session):
        """Test creating a policy holder"""
        holder = PolicyHolder(
            policyno="POL123",
            title="Mr",
            initials="J.D.",
            surname="Smith",
            gender="Male",
            dateofbirth="1985-01-01",
            emailaddress="<EMAIL>",
            phoneno="+27123456789"
        )
        
        test_db_session.add(holder)
        test_db_session.commit()
        test_db_session.refresh(holder)
        
        assert holder.id is not None
        assert holder.policyno == "POL123"
        assert holder.title == "Mr"
        assert holder.surname == "Smith"
        assert holder.gender == "Male"

class TestBeneficiariesModel:
    """Test Beneficiaries model"""
    
    def test_create_beneficiary(self, test_db_session):
        """Test creating a beneficiary"""
        beneficiary = Beneficiaries(
            policyid="POL123",
            title="Mrs",
            firstname="Jane",
            surname="Smith",
            gender="Female",
            relationship="Spouse",
            benofownership="50%",
            status="active",
            datecreated="2024-01-01",
            createdby=1
        )
        
        test_db_session.add(beneficiary)
        test_db_session.commit()
        test_db_session.refresh(beneficiary)
        
        assert beneficiary.id is not None
        assert beneficiary.policyid == "POL123"
        assert beneficiary.firstname == "Jane"
        assert beneficiary.relationship == "Spouse"
        assert beneficiary.benofownership == "50%"

class TestPolicyMandateModel:
    """Test PolicyMandate model"""
    
    def test_create_policy_mandate(self, test_db_session):
        """Test creating a policy mandate"""
        mandate = PolicyMandate(
            policyno="POL123",
            modeofpayment="Debit Order",
            frequency="Monthly",
            premium="R500",
            firstdeductiondate="2024-02-01",
            debitorderFirstname="John",
            debitorderLastname="Smith"
        )
        
        test_db_session.add(mandate)
        test_db_session.commit()
        test_db_session.refresh(mandate)
        
        assert mandate.id is not None
        assert mandate.policyno == "POL123"
        assert mandate.modeofpayment == "Debit Order"
        assert mandate.frequency == "Monthly"
        assert mandate.premium == "R500"

class TestPolicySummaryModel:
    """Test PolicySummary model"""
    
    def test_create_policy_summary(self, test_db_session):
        """Test creating a policy summary"""
        summary = PolicySummary(
            policyno="POL123",
            premiumsdue="R1000",
            premiumspaid="R500",
            totalpremiumspaid="R2500",
            outstanding="R500"
        )
        
        test_db_session.add(summary)
        test_db_session.commit()
        test_db_session.refresh(summary)
        
        assert summary.id is not None
        assert summary.policyno == "POL123"
        assert summary.premiumsdue == "R1000"
        assert summary.outstanding == "R500"

class TestPolicyActivitiesModel:
    """Test PolicyActivities model"""
    
    def test_create_policy_activity(self, test_db_session):
        """Test creating a policy activity"""
        activity = PolicyActivities(
            policyid="POL123",
            policyno="POL123",
            activitydesc="Policy Created",
            remarks="Initial policy setup",
            status="completed",
            datecreated="2024-01-01",
            createdby=1,
            effectivedate="2024-01-01"
        )
        
        test_db_session.add(activity)
        test_db_session.commit()
        test_db_session.refresh(activity)
        
        assert activity.id is not None
        assert activity.policyid == "POL123"
        assert activity.activitydesc == "Policy Created"
        assert activity.status == "completed"
