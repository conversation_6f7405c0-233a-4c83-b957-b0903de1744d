{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\mthunzibackend\\\\frontend\\\\src\\\\components\\\\Reports\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Table, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\nimport apiService from '../../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);\nconst Reports = () => {\n  _s();\n  const [filters, setFilters] = useState({\n    policyType: '',\n    status: '',\n    dateFrom: '',\n    dateTo: '',\n    agentCode: '',\n    premiumMin: '',\n    premiumMax: ''\n  });\n  const [reportData, setReportData] = useState([]);\n  const [summary, setSummary] = useState({\n    totalPolicies: 0,\n    totalPremiums: 0,\n    activePolicies: 0,\n    pendingPolicies: 0\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [hasGenerated, setHasGenerated] = useState(false);\n  useEffect(() => {\n    // Load initial data\n    generateReport();\n  }, []);\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const generateReport = async () => {\n    setLoading(true);\n    setError('');\n    setHasGenerated(true);\n    try {\n      // Fetch policies data\n      const policiesResponse = await apiService.getPolicies();\n      let policies = policiesResponse.data;\n\n      // Apply filters\n      if (filters.policyType) {\n        policies = policies.filter(p => p.packages.toLowerCase().includes(filters.policyType.toLowerCase()));\n      }\n      if (filters.status) {\n        policies = policies.filter(p => p.status === filters.status);\n      }\n      if (filters.agentCode) {\n        policies = policies.filter(p => p.agentcode.toLowerCase().includes(filters.agentCode.toLowerCase()));\n      }\n      if (filters.dateFrom) {\n        policies = policies.filter(p => new Date(p.datecreated) >= new Date(filters.dateFrom));\n      }\n      if (filters.dateTo) {\n        policies = policies.filter(p => new Date(p.datecreated) <= new Date(filters.dateTo));\n      }\n\n      // Calculate summary\n      const totalPolicies = policies.length;\n      const activePolicies = policies.filter(p => p.status === 'Active').length;\n      const pendingPolicies = policies.filter(p => p.status === 'Pending').length;\n\n      // For premium calculation, we'll use mock data since the API doesn't have premium amounts\n      const totalPremiums = policies.length * 1500; // Mock calculation\n\n      setSummary({\n        totalPolicies,\n        totalPremiums,\n        activePolicies,\n        pendingPolicies\n      });\n      setReportData(policies);\n    } catch (err) {\n      console.error('Error generating report:', err);\n      setError('Failed to generate report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const clearFilters = () => {\n    setFilters({\n      policyType: '',\n      status: '',\n      dateFrom: '',\n      dateTo: '',\n      agentCode: '',\n      premiumMin: '',\n      premiumMax: ''\n    });\n  };\n  const exportToCSV = () => {\n    const headers = ['Policy No', 'Package', 'Agent Name', 'Status', 'Date Created', 'Business Unit'];\n    const csvContent = [headers.join(','), ...reportData.map(policy => [policy.policyno, policy.packages, policy.agentname, policy.status, policy.datecreated, policy.bus].join(','))].join('\\n');\n    const blob = new Blob([csvContent], {\n      type: 'text/csv'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `policy_report_${new Date().toISOString().split('T')[0]}.csv`;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n  const getStatusBadge = status => {\n    const statusMap = {\n      'Active': 'success',\n      'Pending': 'warning',\n      'Expired': 'danger',\n      'Cancelled': 'secondary'\n    };\n    return statusMap[status] || 'secondary';\n  };\n\n  // Chart data\n  const chartData = {\n    labels: ['Active', 'Pending', 'Expired', 'Cancelled'],\n    datasets: [{\n      label: 'Number of Policies',\n      data: [summary.activePolicies, summary.pendingPolicies, Math.floor(summary.totalPolicies * 0.1),\n      // Mock expired\n      Math.floor(summary.totalPolicies * 0.05) // Mock cancelled\n      ],\n      backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#6c757d'],\n      borderColor: ['#1e7e34', '#e0a800', '#c82333', '#5a6268'],\n      borderWidth: 1\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      title: {\n        display: true,\n        text: 'Policy Status Distribution'\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"h3 mb-0 text-gray-800\",\n          children: \"Reports & Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Generate comprehensive policy reports with custom filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        children: /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-filter me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), \"Report Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Policy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"policyType\",\n                  value: filters.policyType,\n                  onChange: handleFilterChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Life\",\n                    children: \"Life Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Health\",\n                    children: \"Health Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Property\",\n                    children: \"Property Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Auto\",\n                    children: \"Auto Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"status\",\n                  value: filters.status,\n                  onChange: handleFilterChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Statuses\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Active\",\n                    children: \"Active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Pending\",\n                    children: \"Pending\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Expired\",\n                    children: \"Expired\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Cancelled\",\n                    children: \"Cancelled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Date From\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"dateFrom\",\n                  value: filters.dateFrom,\n                  onChange: handleFilterChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Date To\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"dateTo\",\n                  value: filters.dateTo,\n                  onChange: handleFilterChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Agent Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"agentCode\",\n                  value: filters.agentCode,\n                  onChange: handleFilterChange,\n                  placeholder: \"Enter agent code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Min Premium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"premiumMin\",\n                  value: filters.premiumMin,\n                  onChange: handleFilterChange,\n                  placeholder: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Max Premium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"premiumMax\",\n                  value: filters.premiumMax,\n                  onChange: handleFilterChange,\n                  placeholder: \"10000\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"primary\",\n                  onClick: generateReport,\n                  disabled: loading,\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      as: \"span\",\n                      animation: \"border\",\n                      size: \"sm\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 25\n                    }, this), \"Generating...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-chart-bar me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this), \"Generate Report\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-secondary\",\n                  onClick: clearFilters,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-times me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this), \"Clear Filters\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), hasGenerated && reportData.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"success\",\n                  onClick: exportToCSV,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-download me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 23\n                  }, this), \"Export CSV\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-exclamation-triangle me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 9\n    }, this), hasGenerated && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xl: 3,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"metric-card h-100\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                    children: \"Total Policies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h5 mb-0 font-weight-bold\",\n                    children: summary.totalPolicies.toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: \"auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-file-contract fa-2x\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xl: 3,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"metric-card h-100\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                    children: \"Active Policies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h5 mb-0 font-weight-bold\",\n                    children: summary.activePolicies.toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: \"auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-check-circle fa-2x\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xl: 3,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"metric-card h-100\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                    children: \"Total Premiums\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h5 mb-0 font-weight-bold\",\n                    children: [\"$\", summary.totalPremiums.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: \"auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-dollar-sign fa-2x\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xl: 3,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"metric-card h-100\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-weight-bold text-uppercase mb-1\",\n                    children: \"Pending Policies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h5 mb-0 font-weight-bold\",\n                    children: summary.pendingPolicies.toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: \"auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock fa-2x\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"m-0 font-weight-bold text-primary\",\n                children: \"Policy Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Bar, {\n                data: chartData,\n                options: chartOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"py-3 d-flex flex-row align-items-center justify-content-between\",\n              children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"m-0 font-weight-bold text-primary\",\n                children: [\"Report Data (\", reportData.length, \" records)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"p-0\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-5\",\n                children: /*#__PURE__*/_jsxDEV(Spinner, {\n                  animation: \"border\",\n                  variant: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 21\n              }, this) : reportData.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: '400px',\n                  overflowY: 'auto'\n                },\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  responsive: true,\n                  striped: true,\n                  hover: true,\n                  className: \"mb-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"table-light sticky-top\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Policy No\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Package\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Agent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 476,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Applicant Signed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 477,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Date Created\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 478,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: reportData.map(policy => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"fw-bold\",\n                        children: policy.policyno\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 484,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.packages\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.agentname\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: getStatusBadge(policy.status),\n                          children: policy.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 488,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: policy.datecreated\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 492,\n                        columnNumber: 31\n                      }, this)]\n                    }, policy.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chart-bar fa-3x text-muted mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-muted\",\n                  children: \"No data found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"Try adjusting your filters to see results.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"+3WxPeD6Z6esZ7X+2s/4CMpY88w=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "Table", "<PERSON><PERSON>", "Spinner", "Badge", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Bar", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "Reports", "_s", "filters", "setFilters", "policyType", "status", "dateFrom", "dateTo", "agentCode", "premiumMin", "premiumMax", "reportData", "setReportData", "summary", "set<PERSON>ummary", "totalPolicies", "totalPremiums", "activePolicies", "pendingPolicies", "loading", "setLoading", "error", "setError", "hasGenerated", "setHasGenerated", "generateReport", "handleFilterChange", "e", "name", "value", "target", "prev", "policiesResponse", "getPolicies", "policies", "data", "filter", "p", "packages", "toLowerCase", "includes", "agentcode", "Date", "datecreated", "length", "err", "console", "clearFilters", "exportToCSV", "headers", "csv<PERSON><PERSON>nt", "join", "map", "policy", "policyno", "agentname", "bus", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "getStatusBadge", "statusMap", "chartData", "labels", "datasets", "label", "Math", "floor", "backgroundColor", "borderColor", "borderWidth", "chartOptions", "responsive", "plugins", "legend", "position", "title", "display", "text", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Header", "Body", "md", "Group", "Label", "Select", "onChange", "Control", "placeholder", "variant", "onClick", "disabled", "as", "animation", "size", "role", "xl", "toLocaleString", "xs", "lg", "options", "style", "maxHeight", "overflowY", "striped", "hover", "bg", "id", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/mthunzibackend/frontend/src/components/Reports/Reports.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Table, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\nimport apiService from '../../services/apiService';\n\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);\n\nconst Reports = () => {\n  const [filters, setFilters] = useState({\n    policyType: '',\n    status: '',\n    dateFrom: '',\n    dateTo: '',\n    agentCode: '',\n    premiumMin: '',\n    premiumMax: ''\n  });\n  \n  const [reportData, setReportData] = useState([]);\n  const [summary, setSummary] = useState({\n    totalPolicies: 0,\n    totalPremiums: 0,\n    activePolicies: 0,\n    pendingPolicies: 0\n  });\n  \n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [hasGenerated, setHasGenerated] = useState(false);\n\n  useEffect(() => {\n    // Load initial data\n    generateReport();\n  }, []);\n\n  const handleFilterChange = (e) => {\n    const { name, value } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const generateReport = async () => {\n    setLoading(true);\n    setError('');\n    setHasGenerated(true);\n    \n    try {\n      // Fetch policies data\n      const policiesResponse = await apiService.getPolicies();\n      let policies = policiesResponse.data;\n      \n      // Apply filters\n      if (filters.policyType) {\n        policies = policies.filter(p => p.packages.toLowerCase().includes(filters.policyType.toLowerCase()));\n      }\n      \n      if (filters.status) {\n        policies = policies.filter(p => p.status === filters.status);\n      }\n      \n      if (filters.agentCode) {\n        policies = policies.filter(p => p.agentcode.toLowerCase().includes(filters.agentCode.toLowerCase()));\n      }\n      \n      if (filters.dateFrom) {\n        policies = policies.filter(p => new Date(p.datecreated) >= new Date(filters.dateFrom));\n      }\n      \n      if (filters.dateTo) {\n        policies = policies.filter(p => new Date(p.datecreated) <= new Date(filters.dateTo));\n      }\n      \n      // Calculate summary\n      const totalPolicies = policies.length;\n      const activePolicies = policies.filter(p => p.status === 'Active').length;\n      const pendingPolicies = policies.filter(p => p.status === 'Pending').length;\n      \n      // For premium calculation, we'll use mock data since the API doesn't have premium amounts\n      const totalPremiums = policies.length * 1500; // Mock calculation\n      \n      setSummary({\n        totalPolicies,\n        totalPremiums,\n        activePolicies,\n        pendingPolicies\n      });\n      \n      setReportData(policies);\n      \n    } catch (err) {\n      console.error('Error generating report:', err);\n      setError('Failed to generate report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      policyType: '',\n      status: '',\n      dateFrom: '',\n      dateTo: '',\n      agentCode: '',\n      premiumMin: '',\n      premiumMax: ''\n    });\n  };\n\n  const exportToCSV = () => {\n    const headers = ['Policy No', 'Package', 'Agent Name', 'Status', 'Date Created', 'Business Unit'];\n    const csvContent = [\n      headers.join(','),\n      ...reportData.map(policy => [\n        policy.policyno,\n        policy.packages,\n        policy.agentname,\n        policy.status,\n        policy.datecreated,\n        policy.bus\n      ].join(','))\n    ].join('\\n');\n    \n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `policy_report_${new Date().toISOString().split('T')[0]}.csv`;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n\n  const getStatusBadge = (status) => {\n    const statusMap = {\n      'Active': 'success',\n      'Pending': 'warning',\n      'Expired': 'danger',\n      'Cancelled': 'secondary'\n    };\n    return statusMap[status] || 'secondary';\n  };\n\n  // Chart data\n  const chartData = {\n    labels: ['Active', 'Pending', 'Expired', 'Cancelled'],\n    datasets: [\n      {\n        label: 'Number of Policies',\n        data: [\n          summary.activePolicies,\n          summary.pendingPolicies,\n          Math.floor(summary.totalPolicies * 0.1), // Mock expired\n          Math.floor(summary.totalPolicies * 0.05)  // Mock cancelled\n        ],\n        backgroundColor: [\n          '#28a745',\n          '#ffc107',\n          '#dc3545',\n          '#6c757d'\n        ],\n        borderColor: [\n          '#1e7e34',\n          '#e0a800',\n          '#c82333',\n          '#5a6268'\n        ],\n        borderWidth: 1\n      }\n    ]\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n      title: {\n        display: true,\n        text: 'Policy Status Distribution'\n      }\n    }\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h1 className=\"h3 mb-0 text-gray-800\">Reports & Analytics</h1>\n          <p className=\"text-muted\">Generate comprehensive policy reports with custom filters</p>\n        </Col>\n      </Row>\n\n      {/* Filters */}\n      <Card className=\"mb-4\">\n        <Card.Header>\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-filter me-2\"></i>\n            Report Filters\n          </h5>\n        </Card.Header>\n        <Card.Body>\n          <Form>\n            <Row>\n              <Col md={3}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Policy Type</Form.Label>\n                  <Form.Select\n                    name=\"policyType\"\n                    value={filters.policyType}\n                    onChange={handleFilterChange}\n                  >\n                    <option value=\"\">All Types</option>\n                    <option value=\"Life\">Life Insurance</option>\n                    <option value=\"Health\">Health Insurance</option>\n                    <option value=\"Property\">Property Insurance</option>\n                    <option value=\"Auto\">Auto Insurance</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={3}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Status</Form.Label>\n                  <Form.Select\n                    name=\"status\"\n                    value={filters.status}\n                    onChange={handleFilterChange}\n                  >\n                    <option value=\"\">All Statuses</option>\n                    <option value=\"Active\">Active</option>\n                    <option value=\"Pending\">Pending</option>\n                    <option value=\"Expired\">Expired</option>\n                    <option value=\"Cancelled\">Cancelled</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={3}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Date From</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    name=\"dateFrom\"\n                    value={filters.dateFrom}\n                    onChange={handleFilterChange}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={3}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Date To</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    name=\"dateTo\"\n                    value={filters.dateTo}\n                    onChange={handleFilterChange}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            <Row>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Agent Code</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"agentCode\"\n                    value={filters.agentCode}\n                    onChange={handleFilterChange}\n                    placeholder=\"Enter agent code\"\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Min Premium</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    name=\"premiumMin\"\n                    value={filters.premiumMin}\n                    onChange={handleFilterChange}\n                    placeholder=\"0\"\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Max Premium</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    name=\"premiumMax\"\n                    value={filters.premiumMax}\n                    onChange={handleFilterChange}\n                    placeholder=\"10000\"\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            <Row>\n              <Col>\n                <div className=\"d-flex gap-2\">\n                  <Button \n                    variant=\"primary\" \n                    onClick={generateReport}\n                    disabled={loading}\n                  >\n                    {loading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Generating...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-chart-bar me-2\"></i>\n                        Generate Report\n                      </>\n                    )}\n                  </Button>\n                  <Button \n                    variant=\"outline-secondary\" \n                    onClick={clearFilters}\n                  >\n                    <i className=\"fas fa-times me-2\"></i>\n                    Clear Filters\n                  </Button>\n                  {hasGenerated && reportData.length > 0 && (\n                    <Button \n                      variant=\"success\" \n                      onClick={exportToCSV}\n                    >\n                      <i className=\"fas fa-download me-2\"></i>\n                      Export CSV\n                    </Button>\n                  )}\n                </div>\n              </Col>\n            </Row>\n          </Form>\n        </Card.Body>\n      </Card>\n\n      {error && (\n        <Alert variant=\"danger\" className=\"mb-4\">\n          <i className=\"fas fa-exclamation-triangle me-2\"></i>\n          {error}\n        </Alert>\n      )}\n\n      {hasGenerated && (\n        <>\n          {/* Summary Cards */}\n          <Row className=\"mb-4\">\n            <Col xl={3} md={6} className=\"mb-4\">\n              <Card className=\"metric-card h-100\">\n                <Card.Body>\n                  <Row>\n                    <Col>\n                      <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                        Total Policies\n                      </div>\n                      <div className=\"h5 mb-0 font-weight-bold\">\n                        {summary.totalPolicies.toLocaleString()}\n                      </div>\n                    </Col>\n                    <Col xs=\"auto\">\n                      <i className=\"fas fa-file-contract fa-2x\"></i>\n                    </Col>\n                  </Row>\n                </Card.Body>\n              </Card>\n            </Col>\n\n            <Col xl={3} md={6} className=\"mb-4\">\n              <Card className=\"metric-card h-100\">\n                <Card.Body>\n                  <Row>\n                    <Col>\n                      <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                        Active Policies\n                      </div>\n                      <div className=\"h5 mb-0 font-weight-bold\">\n                        {summary.activePolicies.toLocaleString()}\n                      </div>\n                    </Col>\n                    <Col xs=\"auto\">\n                      <i className=\"fas fa-check-circle fa-2x\"></i>\n                    </Col>\n                  </Row>\n                </Card.Body>\n              </Card>\n            </Col>\n\n            <Col xl={3} md={6} className=\"mb-4\">\n              <Card className=\"metric-card h-100\">\n                <Card.Body>\n                  <Row>\n                    <Col>\n                      <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                        Total Premiums\n                      </div>\n                      <div className=\"h5 mb-0 font-weight-bold\">\n                        ${summary.totalPremiums.toLocaleString()}\n                      </div>\n                    </Col>\n                    <Col xs=\"auto\">\n                      <i className=\"fas fa-dollar-sign fa-2x\"></i>\n                    </Col>\n                  </Row>\n                </Card.Body>\n              </Card>\n            </Col>\n\n            <Col xl={3} md={6} className=\"mb-4\">\n              <Card className=\"metric-card h-100\">\n                <Card.Body>\n                  <Row>\n                    <Col>\n                      <div className=\"text-xs font-weight-bold text-uppercase mb-1\">\n                        Pending Policies\n                      </div>\n                      <div className=\"h5 mb-0 font-weight-bold\">\n                        {summary.pendingPolicies.toLocaleString()}\n                      </div>\n                    </Col>\n                    <Col xs=\"auto\">\n                      <i className=\"fas fa-clock fa-2x\"></i>\n                    </Col>\n                  </Row>\n                </Card.Body>\n              </Card>\n            </Col>\n          </Row>\n\n          {/* Chart and Table */}\n          <Row>\n            <Col lg={4}>\n              <Card className=\"shadow mb-4\">\n                <Card.Header>\n                  <h6 className=\"m-0 font-weight-bold text-primary\">Policy Distribution</h6>\n                </Card.Header>\n                <Card.Body>\n                  <Bar data={chartData} options={chartOptions} />\n                </Card.Body>\n              </Card>\n            </Col>\n\n            <Col lg={8}>\n              <Card className=\"shadow\">\n                <Card.Header className=\"py-3 d-flex flex-row align-items-center justify-content-between\">\n                  <h6 className=\"m-0 font-weight-bold text-primary\">\n                    Report Data ({reportData.length} records)\n                  </h6>\n                </Card.Header>\n                <Card.Body className=\"p-0\">\n                  {loading ? (\n                    <div className=\"text-center py-5\">\n                      <Spinner animation=\"border\" variant=\"primary\" />\n                    </div>\n                  ) : reportData.length > 0 ? (\n                    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n                      <Table responsive striped hover className=\"mb-0\">\n                        <thead className=\"table-light sticky-top\">\n                          <tr>\n                            <th>Policy No</th>\n                            <th>Package</th>\n                            <th>Agent</th>\n                            <th>Status</th>\n                            <th>Applicant Signed</th>\n                            <th>Date Created</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          {reportData.map((policy) => (\n                            <tr key={policy.id}>\n                              <td className=\"fw-bold\">{policy.policyno}</td>\n                              <td>{policy.packages}</td>\n                              <td>{policy.agentname}</td>\n                              <td>\n                                <Badge bg={getStatusBadge(policy.status)}>\n                                  {policy.status}\n                                </Badge>\n                              </td>\n                              <td>{policy.datecreated}</td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </Table>\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-5\">\n                      <i className=\"fas fa-chart-bar fa-3x text-muted mb-3\"></i>\n                      <h5 className=\"text-muted\">No data found</h5>\n                      <p className=\"text-muted\">Try adjusting your filters to see results.</p>\n                    </div>\n                  )}\n                </Card.Body>\n              </Card>\n            </Col>\n          </Row>\n        </>\n      )}\n    </Container>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AACvG,SAASC,KAAK,IAAIC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AAC3G,SAASC,GAAG,QAAQ,iBAAiB;AACrC,OAAOC,UAAU,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnDZ,OAAO,CAACa,QAAQ,CAACZ,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAEhF,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC;IACrC+B,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC;IACrC0C,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACAmD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,UAAU,CAAC4B,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMJ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMQ,gBAAgB,GAAG,MAAMtC,UAAU,CAACuC,WAAW,CAAC,CAAC;MACvD,IAAIC,QAAQ,GAAGF,gBAAgB,CAACG,IAAI;;MAEpC;MACA,IAAIjC,OAAO,CAACE,UAAU,EAAE;QACtB8B,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,OAAO,CAACE,UAAU,CAACmC,WAAW,CAAC,CAAC,CAAC,CAAC;MACtG;MAEA,IAAIrC,OAAO,CAACG,MAAM,EAAE;QAClB6B,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChC,MAAM,KAAKH,OAAO,CAACG,MAAM,CAAC;MAC9D;MAEA,IAAIH,OAAO,CAACM,SAAS,EAAE;QACrB0B,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACI,SAAS,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,OAAO,CAACM,SAAS,CAAC+B,WAAW,CAAC,CAAC,CAAC,CAAC;MACtG;MAEA,IAAIrC,OAAO,CAACI,QAAQ,EAAE;QACpB4B,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAI,IAAIK,IAAI,CAACL,CAAC,CAACM,WAAW,CAAC,IAAI,IAAID,IAAI,CAACxC,OAAO,CAACI,QAAQ,CAAC,CAAC;MACxF;MAEA,IAAIJ,OAAO,CAACK,MAAM,EAAE;QAClB2B,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAI,IAAIK,IAAI,CAACL,CAAC,CAACM,WAAW,CAAC,IAAI,IAAID,IAAI,CAACxC,OAAO,CAACK,MAAM,CAAC,CAAC;MACtF;;MAEA;MACA,MAAMQ,aAAa,GAAGmB,QAAQ,CAACU,MAAM;MACrC,MAAM3B,cAAc,GAAGiB,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChC,MAAM,KAAK,QAAQ,CAAC,CAACuC,MAAM;MACzE,MAAM1B,eAAe,GAAGgB,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChC,MAAM,KAAK,SAAS,CAAC,CAACuC,MAAM;;MAE3E;MACA,MAAM5B,aAAa,GAAGkB,QAAQ,CAACU,MAAM,GAAG,IAAI,CAAC,CAAC;;MAE9C9B,UAAU,CAAC;QACTC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC;MACF,CAAC,CAAC;MAEFN,aAAa,CAACsB,QAAQ,CAAC;IAEzB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,0BAA0B,EAAEwB,GAAG,CAAC;MAC9CvB,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB5C,UAAU,CAAC;MACTC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAE,eAAe,CAAC;IACjG,MAAMC,UAAU,GAAG,CACjBD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EACjB,GAAGxC,UAAU,CAACyC,GAAG,CAACC,MAAM,IAAI,CAC1BA,MAAM,CAACC,QAAQ,EACfD,MAAM,CAACf,QAAQ,EACfe,MAAM,CAACE,SAAS,EAChBF,MAAM,CAAChD,MAAM,EACbgD,MAAM,CAACV,WAAW,EAClBU,MAAM,CAACG,GAAG,CACX,CAACL,IAAI,CAAC,GAAG,CAAC,CAAC,CACb,CAACA,IAAI,CAAC,IAAI,CAAC;IAEZ,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,UAAU,CAAC,EAAE;MAAES,IAAI,EAAE;IAAW,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,iBAAiB,IAAI1B,IAAI,CAAC,CAAC,CAAC2B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC1EN,CAAC,CAACO,KAAK,CAAC,CAAC;IACTV,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC;EACjC,CAAC;EAED,MAAMa,cAAc,GAAIpE,MAAM,IAAK;IACjC,MAAMqE,SAAS,GAAG;MAChB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,SAAS,EAAE,QAAQ;MACnB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACrE,MAAM,CAAC,IAAI,WAAW;EACzC,CAAC;;EAED;EACA,MAAMsE,SAAS,GAAG;IAChBC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;IACrDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,oBAAoB;MAC3B3C,IAAI,EAAE,CACJtB,OAAO,CAACI,cAAc,EACtBJ,OAAO,CAACK,eAAe,EACvB6D,IAAI,CAACC,KAAK,CAACnE,OAAO,CAACE,aAAa,GAAG,GAAG,CAAC;MAAE;MACzCgE,IAAI,CAACC,KAAK,CAACnE,OAAO,CAACE,aAAa,GAAG,IAAI,CAAC,CAAE;MAAA,CAC3C;MACDkE,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;MACR;IACF;EACF,CAAC;EAED,oBACE/F,OAAA,CAACrB,SAAS;IAACqH,KAAK;IAAAC,QAAA,gBACdjG,OAAA,CAACpB,GAAG;MAACsH,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBjG,OAAA,CAACnB,GAAG;QAAAoH,QAAA,gBACFjG,OAAA;UAAIkG,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtG,OAAA;UAAGkG,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAyD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtG,OAAA,CAAClB,IAAI;MAACoH,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACpBjG,OAAA,CAAClB,IAAI,CAACyH,MAAM;QAAAN,QAAA,eACVjG,OAAA;UAAIkG,SAAS,EAAC,MAAM;UAAAD,QAAA,gBAClBjG,OAAA;YAAGkG,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACdtG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;QAAAP,QAAA,eACRjG,OAAA,CAACjB,IAAI;UAAAkH,QAAA,gBACHjG,OAAA,CAACpB,GAAG;YAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCtG,OAAA,CAACjB,IAAI,CAAC6H,MAAM;kBACV5E,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE3B,OAAO,CAACE,UAAW;kBAC1BqG,QAAQ,EAAE/E,kBAAmB;kBAAAmE,QAAA,gBAE7BjG,OAAA;oBAAQiC,KAAK,EAAC,EAAE;oBAAAgE,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnCtG,OAAA;oBAAQiC,KAAK,EAAC,MAAM;oBAAAgE,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtG,OAAA;oBAAQiC,KAAK,EAAC,QAAQ;oBAAAgE,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDtG,OAAA;oBAAQiC,KAAK,EAAC,UAAU;oBAAAgE,QAAA,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpDtG,OAAA;oBAAQiC,KAAK,EAAC,MAAM;oBAAAgE,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/BtG,OAAA,CAACjB,IAAI,CAAC6H,MAAM;kBACV5E,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAE3B,OAAO,CAACG,MAAO;kBACtBoG,QAAQ,EAAE/E,kBAAmB;kBAAAmE,QAAA,gBAE7BjG,OAAA;oBAAQiC,KAAK,EAAC,EAAE;oBAAAgE,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCtG,OAAA;oBAAQiC,KAAK,EAAC,QAAQ;oBAAAgE,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCtG,OAAA;oBAAQiC,KAAK,EAAC,SAAS;oBAAAgE,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCtG,OAAA;oBAAQiC,KAAK,EAAC,SAAS;oBAAAgE,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCtG,OAAA;oBAAQiC,KAAK,EAAC,WAAW;oBAAAgE,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClCtG,OAAA,CAACjB,IAAI,CAAC+H,OAAO;kBACX/C,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAE3B,OAAO,CAACI,QAAS;kBACxBmG,QAAQ,EAAE/E;gBAAmB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCtG,OAAA,CAACjB,IAAI,CAAC+H,OAAO;kBACX/C,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAE3B,OAAO,CAACK,MAAO;kBACtBkG,QAAQ,EAAE/E;gBAAmB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtG,OAAA,CAACpB,GAAG;YAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCtG,OAAA,CAACjB,IAAI,CAAC+H,OAAO;kBACX/C,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE3B,OAAO,CAACM,SAAU;kBACzBiG,QAAQ,EAAE/E,kBAAmB;kBAC7BiF,WAAW,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCtG,OAAA,CAACjB,IAAI,CAAC+H,OAAO;kBACX/C,IAAI,EAAC,QAAQ;kBACb/B,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE3B,OAAO,CAACO,UAAW;kBAC1BgG,QAAQ,EAAE/E,kBAAmB;kBAC7BiF,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtG,OAAA,CAACnB,GAAG;cAAC4H,EAAE,EAAE,CAAE;cAAAR,QAAA,eACTjG,OAAA,CAACjB,IAAI,CAAC2H,KAAK;gBAACR,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjG,OAAA,CAACjB,IAAI,CAAC4H,KAAK;kBAAAV,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCtG,OAAA,CAACjB,IAAI,CAAC+H,OAAO;kBACX/C,IAAI,EAAC,QAAQ;kBACb/B,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE3B,OAAO,CAACQ,UAAW;kBAC1B+F,QAAQ,EAAE/E,kBAAmB;kBAC7BiF,WAAW,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtG,OAAA,CAACpB,GAAG;YAAAqH,QAAA,eACFjG,OAAA,CAACnB,GAAG;cAAAoH,QAAA,eACFjG,OAAA;gBAAKkG,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC3BjG,OAAA,CAAChB,MAAM;kBACLgI,OAAO,EAAC,SAAS;kBACjBC,OAAO,EAAEpF,cAAe;kBACxBqF,QAAQ,EAAE3F,OAAQ;kBAAA0E,QAAA,EAEjB1E,OAAO,gBACNvB,OAAA,CAAAE,SAAA;oBAAA+F,QAAA,gBACEjG,OAAA,CAACb,OAAO;sBACNgI,EAAE,EAAC,MAAM;sBACTC,SAAS,EAAC,QAAQ;sBAClBC,IAAI,EAAC,IAAI;sBACTC,IAAI,EAAC,QAAQ;sBACb,eAAY,MAAM;sBAClBpB,SAAS,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,iBAEJ;kBAAA,eAAE,CAAC,gBAEHtG,OAAA,CAAAE,SAAA;oBAAA+F,QAAA,gBACEjG,OAAA;sBAAGkG,SAAS,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,mBAE3C;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACTtG,OAAA,CAAChB,MAAM;kBACLgI,OAAO,EAAC,mBAAmB;kBAC3BC,OAAO,EAAE9D,YAAa;kBAAA8C,QAAA,gBAEtBjG,OAAA;oBAAGkG,SAAS,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,iBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACR3E,YAAY,IAAIZ,UAAU,CAACiC,MAAM,GAAG,CAAC,iBACpChD,OAAA,CAAChB,MAAM;kBACLgI,OAAO,EAAC,SAAS;kBACjBC,OAAO,EAAE7D,WAAY;kBAAA6C,QAAA,gBAErBjG,OAAA;oBAAGkG,SAAS,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,cAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEN7E,KAAK,iBACJzB,OAAA,CAACd,KAAK;MAAC8H,OAAO,EAAC,QAAQ;MAACd,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACtCjG,OAAA;QAAGkG,SAAS,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACnD7E,KAAK;IAAA;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA3E,YAAY,iBACX3B,OAAA,CAAAE,SAAA;MAAA+F,QAAA,gBAEEjG,OAAA,CAACpB,GAAG;QAACsH,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBjG,OAAA,CAACnB,GAAG;UAAC0I,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACP,SAAS,EAAC,MAAM;UAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI;YAACoH,SAAS,EAAC,mBAAmB;YAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;cAAAP,QAAA,eACRjG,OAAA,CAACpB,GAAG;gBAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;kBAAAoH,QAAA,gBACFjG,OAAA;oBAAKkG,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAE9D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtG,OAAA;oBAAKkG,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,EACtChF,OAAO,CAACE,aAAa,CAACqG,cAAc,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtG,OAAA,CAACnB,GAAG;kBAAC4I,EAAE,EAAC,MAAM;kBAAAxB,QAAA,eACZjG,OAAA;oBAAGkG,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtG,OAAA,CAACnB,GAAG;UAAC0I,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACP,SAAS,EAAC,MAAM;UAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI;YAACoH,SAAS,EAAC,mBAAmB;YAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;cAAAP,QAAA,eACRjG,OAAA,CAACpB,GAAG;gBAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;kBAAAoH,QAAA,gBACFjG,OAAA;oBAAKkG,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAE9D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtG,OAAA;oBAAKkG,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,EACtChF,OAAO,CAACI,cAAc,CAACmG,cAAc,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtG,OAAA,CAACnB,GAAG;kBAAC4I,EAAE,EAAC,MAAM;kBAAAxB,QAAA,eACZjG,OAAA;oBAAGkG,SAAS,EAAC;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtG,OAAA,CAACnB,GAAG;UAAC0I,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACP,SAAS,EAAC,MAAM;UAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI;YAACoH,SAAS,EAAC,mBAAmB;YAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;cAAAP,QAAA,eACRjG,OAAA,CAACpB,GAAG;gBAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;kBAAAoH,QAAA,gBACFjG,OAAA;oBAAKkG,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAE9D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtG,OAAA;oBAAKkG,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,GAAC,GACvC,EAAChF,OAAO,CAACG,aAAa,CAACoG,cAAc,CAAC,CAAC;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtG,OAAA,CAACnB,GAAG;kBAAC4I,EAAE,EAAC,MAAM;kBAAAxB,QAAA,eACZjG,OAAA;oBAAGkG,SAAS,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtG,OAAA,CAACnB,GAAG;UAAC0I,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACP,SAAS,EAAC,MAAM;UAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI;YAACoH,SAAS,EAAC,mBAAmB;YAAAD,QAAA,eACjCjG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;cAAAP,QAAA,eACRjG,OAAA,CAACpB,GAAG;gBAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;kBAAAoH,QAAA,gBACFjG,OAAA;oBAAKkG,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAE9D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtG,OAAA;oBAAKkG,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,EACtChF,OAAO,CAACK,eAAe,CAACkG,cAAc,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtG,OAAA,CAACnB,GAAG;kBAAC4I,EAAE,EAAC,MAAM;kBAAAxB,QAAA,eACZjG,OAAA;oBAAGkG,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtG,OAAA,CAACpB,GAAG;QAAAqH,QAAA,gBACFjG,OAAA,CAACnB,GAAG;UAAC6I,EAAE,EAAE,CAAE;UAAAzB,QAAA,eACTjG,OAAA,CAAClB,IAAI;YAACoH,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC3BjG,OAAA,CAAClB,IAAI,CAACyH,MAAM;cAAAN,QAAA,eACVjG,OAAA;gBAAIkG,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACdtG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;cAAAP,QAAA,eACRjG,OAAA,CAACH,GAAG;gBAAC0C,IAAI,EAAEwC,SAAU;gBAAC4C,OAAO,EAAEnC;cAAa;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtG,OAAA,CAACnB,GAAG;UAAC6I,EAAE,EAAE,CAAE;UAAAzB,QAAA,eACTjG,OAAA,CAAClB,IAAI;YAACoH,SAAS,EAAC,QAAQ;YAAAD,QAAA,gBACtBjG,OAAA,CAAClB,IAAI,CAACyH,MAAM;cAACL,SAAS,EAAC,iEAAiE;cAAAD,QAAA,eACtFjG,OAAA;gBAAIkG,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,GAAC,eACnC,EAAClF,UAAU,CAACiC,MAAM,EAAC,WAClC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACdtG,OAAA,CAAClB,IAAI,CAAC0H,IAAI;cAACN,SAAS,EAAC,KAAK;cAAAD,QAAA,EACvB1E,OAAO,gBACNvB,OAAA;gBAAKkG,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,eAC/BjG,OAAA,CAACb,OAAO;kBAACiI,SAAS,EAAC,QAAQ;kBAACJ,OAAO,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,GACJvF,UAAU,CAACiC,MAAM,GAAG,CAAC,gBACvBhD,OAAA;gBAAK4H,KAAK,EAAE;kBAAEC,SAAS,EAAE,OAAO;kBAAEC,SAAS,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,eACpDjG,OAAA,CAACf,KAAK;kBAACwG,UAAU;kBAACsC,OAAO;kBAACC,KAAK;kBAAC9B,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC9CjG,OAAA;oBAAOkG,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eACvCjG,OAAA;sBAAAiG,QAAA,gBACEjG,OAAA;wBAAAiG,QAAA,EAAI;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBtG,OAAA;wBAAAiG,QAAA,EAAI;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChBtG,OAAA;wBAAAiG,QAAA,EAAI;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACdtG,OAAA;wBAAAiG,QAAA,EAAI;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACftG,OAAA;wBAAAiG,QAAA,EAAI;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzBtG,OAAA;wBAAAiG,QAAA,EAAI;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRtG,OAAA;oBAAAiG,QAAA,EACGlF,UAAU,CAACyC,GAAG,CAAEC,MAAM,iBACrBzD,OAAA;sBAAAiG,QAAA,gBACEjG,OAAA;wBAAIkG,SAAS,EAAC,SAAS;wBAAAD,QAAA,EAAExC,MAAM,CAACC;sBAAQ;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9CtG,OAAA;wBAAAiG,QAAA,EAAKxC,MAAM,CAACf;sBAAQ;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1BtG,OAAA;wBAAAiG,QAAA,EAAKxC,MAAM,CAACE;sBAAS;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3BtG,OAAA;wBAAAiG,QAAA,eACEjG,OAAA,CAACZ,KAAK;0BAAC6I,EAAE,EAAEpD,cAAc,CAACpB,MAAM,CAAChD,MAAM,CAAE;0BAAAwF,QAAA,EACtCxC,MAAM,CAAChD;wBAAM;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACLtG,OAAA;wBAAAiG,QAAA,EAAKxC,MAAM,CAACV;sBAAW;wBAAAoD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GATtB7C,MAAM,CAACyE,EAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAUd,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENtG,OAAA;gBAAKkG,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BjG,OAAA;kBAAGkG,SAAS,EAAC;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1DtG,OAAA;kBAAIkG,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CtG,OAAA;kBAAGkG,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAA0C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACjG,EAAA,CAxfID,OAAO;AAAA+H,EAAA,GAAP/H,OAAO;AA0fb,eAAeA,OAAO;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}