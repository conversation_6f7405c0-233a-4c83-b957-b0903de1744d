import React from 'react';
import { Navbar as BootstrapNavbar, Nav, NavDropdown, Container } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Navbar = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <BootstrapNavbar bg="dark" variant="dark" expand="lg" className="navbar-dark">
      <Container fluid>
        <BootstrapNavbar.Brand href="/dashboard">
          <i className="fas fa-shield-alt me-2"></i>
          Mthunzi Policy Management
        </BootstrapNavbar.Brand>
        
        <BootstrapNavbar.Toggle aria-controls="basic-navbar-nav" />
        
        <BootstrapNavbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link href="/dashboard">
              <i className="fas fa-tachometer-alt me-1"></i>
              Dashboard
            </Nav.Link>
            <Nav.Link href="/policies">
              <i className="fas fa-file-contract me-1"></i>
              Policies
            </Nav.Link>
            <Nav.Link href="/search">
              <i className="fas fa-search me-1"></i>
              Search
            </Nav.Link>
            <Nav.Link href="/reports">
              <i className="fas fa-chart-bar me-1"></i>
              Reports
            </Nav.Link>
          </Nav>
          
          <Nav>
            <NavDropdown 
              title={
                <span>
                  <i className="fas fa-user me-1"></i>
                  {user?.username || 'User'}
                </span>
              } 
              id="user-dropdown"
              align="end"
            >
              <NavDropdown.Item>
                <i className="fas fa-user-circle me-2"></i>
                Profile
              </NavDropdown.Item>
              <NavDropdown.Item>
                <i className="fas fa-cog me-2"></i>
                Settings
              </NavDropdown.Item>
              <NavDropdown.Divider />
              <NavDropdown.Item onClick={handleLogout}>
                <i className="fas fa-sign-out-alt me-2"></i>
                Logout
              </NavDropdown.Item>
            </NavDropdown>
          </Nav>
        </BootstrapNavbar.Collapse>
      </Container>
    </BootstrapNavbar>
  );
};

export default Navbar;
