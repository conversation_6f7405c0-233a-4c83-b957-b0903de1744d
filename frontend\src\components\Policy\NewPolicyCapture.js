import React, { useState } from 'react';
import { Container, <PERSON>, Col, Card, Form, Button, Alert, ProgressBar, Badge } from 'react-bootstrap';
import { FaPlus, FaTrash, FaCheck, FaArrowLeft, FaArrowRight } from 'react-icons/fa';
import apiService from '../../services/apiService';

const NewPolicyCapture = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Form data state
  const [policyDetails, setPolicyDetails] = useState({
    policyno: '',
    packages: 'Standard',
    bus: false,
    pep: false,
    agentcode: '',
    agentname: '',
    status: 'Active',
    schemecode: '',
    worksitecode: '',
    riskprofile: ''
  });

  const [policyHolder, setPolicyHolder] = useState({
    title: 'Mr',
    initials: '',
    firstname: '',
    middlename: '',
    surname: '',
    gender: 'Male',
    dateofbirth: '',
    maritalstatus: 'Single',
    capturedate: new Date().toISOString().split('T')[0],
    idnumber: '',
    idtype: '',
    occupation: '',
    mobilenumber: '',
    alternativenumber: '',
    emailaddress: '',
    town: '',
    residentialstatus: ''
  });

  const [paymentMandate, setPaymentMandate] = useState({
    firstname: '',
    surname: '',
    mobilenumber: '',
    frequency: 'Monthly',
    premium: '',
    modeofpayment: 'Debit Order',
    firstdeductiondate: '',
    paypointname: '',
    bankaccountnumber: '',
    bankaccounttype: '',
    branchname: ''
  });

  const [beneficiaries, setBeneficiaries] = useState([
    {
      title: 'Mr',
      initials: '',
      firstname: '',
      surname: '',
      gender: 'Male',
      birthdate: '',
      relationship: '',
      membertype: 'Family',
      idnumber: ''
    }
  ]);

  const steps = [
    { number: 1, title: 'Policy Details', description: 'Basic policy information' },
    { number: 2, title: 'Policy Holder', description: 'Policy holder details' },
    { number: 3, title: 'Payment Mandate', description: 'Payment information' },
    { number: 4, title: 'Beneficiaries', description: 'Beneficiary details' },
    { number: 5, title: 'Review & Submit', description: 'Review all information' }
  ];

  const packageOptions = ['Lite', 'Standard', 'Premier'];
  const titleOptions = ['Mr', 'Ms', 'Mrs', 'Dr', 'Prof'];
  const genderOptions = ['Male', 'Female'];
  const maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];
  const frequencyOptions = ['Monthly', 'Quarterly', 'Annually'];
  const paymentModeOptions = ['Debit Order', 'Stop Order', 'Mobile Money'];
  const memberTypeOptions = ['Family', 'Other', 'BFO'];

  const addBeneficiary = () => {
    setBeneficiaries([...beneficiaries, {
      title: 'Mr',
      initials: '',
      firstname: '',
      surname: '',
      gender: 'Male',
      birthdate: '',
      relationship: '',
      membertype: 'Family',
      idnumber: ''
    }]);
  };

  const removeBeneficiary = (index) => {
    if (beneficiaries.length > 1) {
      setBeneficiaries(beneficiaries.filter((_, i) => i !== index));
    }
  };

  const updateBeneficiary = (index, field, value) => {
    const updated = [...beneficiaries];
    updated[index][field] = value;
    setBeneficiaries(updated);
  };

  const validateStep = (step) => {
    switch (step) {
      case 1:
        return policyDetails.policyno && policyDetails.agentcode && policyDetails.agentname;
      case 2:
        return policyHolder.firstname && policyHolder.surname && policyHolder.dateofbirth;
      case 3:
        return paymentMandate.firstname && paymentMandate.surname && paymentMandate.premium && paymentMandate.firstdeductiondate;
      case 4:
        return beneficiaries.every(b => b.firstname && b.surname && b.relationship);
      default:
        return true;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
      setError(null);
    } else {
      setError('Please fill in all required fields before proceeding.');
    }
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
    setError(null);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      const payload = {
        policy_details: policyDetails,
        policy_holder: policyHolder,
        payment_mandate: paymentMandate,
        beneficiaries: beneficiaries
      };

      const response = await apiService.post('/policy-management/comprehensive', payload);
      
      setSuccess(`Policy ${response.data.policy_number} created successfully!`);
      setCurrentStep(6); // Success step
      
    } catch (err) {
      setError(err.response?.data?.detail || 'Error creating policy');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="mb-4">
      <ProgressBar now={(currentStep / 5) * 100} className="mb-3" />
      <div className="d-flex justify-content-between">
        {steps.map((step) => (
          <div key={step.number} className="text-center">
            <Badge 
              bg={currentStep >= step.number ? 'primary' : 'secondary'}
              className="rounded-circle p-2 mb-1"
            >
              {currentStep > step.number ? <FaCheck /> : step.number}
            </Badge>
            <div className="small">{step.title}</div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPolicyDetailsStep = () => (
    <Card>
      <Card.Header>
        <h5>Step 1: Policy Details</h5>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Policy Number *</Form.Label>
              <Form.Control
                type="text"
                value={policyDetails.policyno}
                onChange={(e) => setPolicyDetails({...policyDetails, policyno: e.target.value})}
                placeholder="Enter policy number"
                required
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Package *</Form.Label>
              <Form.Select
                value={policyDetails.packages}
                onChange={(e) => setPolicyDetails({...policyDetails, packages: e.target.value})}
              >
                {packageOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Agent Code *</Form.Label>
              <Form.Control
                type="text"
                value={policyDetails.agentcode}
                onChange={(e) => setPolicyDetails({...policyDetails, agentcode: e.target.value})}
                placeholder="Enter agent code"
                required
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Agent Name *</Form.Label>
              <Form.Control
                type="text"
                value={policyDetails.agentname}
                onChange={(e) => setPolicyDetails({...policyDetails, agentname: e.target.value})}
                placeholder="Enter agent name"
                required
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="Business Unit"
                checked={policyDetails.bus}
                onChange={(e) => setPolicyDetails({...policyDetails, bus: e.target.checked})}
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="PEP"
                checked={policyDetails.pep}
                onChange={(e) => setPolicyDetails({...policyDetails, pep: e.target.checked})}
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Status</Form.Label>
              <Form.Select
                value={policyDetails.status}
                onChange={(e) => setPolicyDetails({...policyDetails, status: e.target.value})}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Pending">Pending</option>
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );

  const renderPolicyHolderStep = () => (
    <Card>
      <Card.Header>
        <h5>Step 2: Policy Holder Details</h5>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Title *</Form.Label>
              <Form.Select
                value={policyHolder.title}
                onChange={(e) => setPolicyHolder({...policyHolder, title: e.target.value})}
              >
                {titleOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Initials *</Form.Label>
              <Form.Control
                type="text"
                value={policyHolder.initials}
                onChange={(e) => setPolicyHolder({...policyHolder, initials: e.target.value})}
                placeholder="e.g., J.D."
                required
              />
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>First Name *</Form.Label>
              <Form.Control
                type="text"
                value={policyHolder.firstname}
                onChange={(e) => setPolicyHolder({...policyHolder, firstname: e.target.value})}
                placeholder="Enter first name"
                required
              />
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Surname *</Form.Label>
              <Form.Control
                type="text"
                value={policyHolder.surname}
                onChange={(e) => setPolicyHolder({...policyHolder, surname: e.target.value})}
                placeholder="Enter surname"
                required
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Gender *</Form.Label>
              <Form.Select
                value={policyHolder.gender}
                onChange={(e) => setPolicyHolder({...policyHolder, gender: e.target.value})}
              >
                {genderOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Date of Birth *</Form.Label>
              <Form.Control
                type="date"
                value={policyHolder.dateofbirth}
                onChange={(e) => setPolicyHolder({...policyHolder, dateofbirth: e.target.value})}
                required
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Marital Status *</Form.Label>
              <Form.Select
                value={policyHolder.maritalstatus}
                onChange={(e) => setPolicyHolder({...policyHolder, maritalstatus: e.target.value})}
              >
                {maritalStatusOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Mobile Number</Form.Label>
              <Form.Control
                type="text"
                value={policyHolder.mobilenumber}
                onChange={(e) => setPolicyHolder({...policyHolder, mobilenumber: e.target.value})}
                placeholder="Enter mobile number"
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Email Address</Form.Label>
              <Form.Control
                type="email"
                value={policyHolder.emailaddress}
                onChange={(e) => setPolicyHolder({...policyHolder, emailaddress: e.target.value})}
                placeholder="Enter email address"
              />
            </Form.Group>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );

  const renderPaymentMandateStep = () => (
    <Card>
      <Card.Header>
        <h5>Step 3: Payment Mandate</h5>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>First Name *</Form.Label>
              <Form.Control
                type="text"
                value={paymentMandate.firstname}
                onChange={(e) => setPaymentMandate({...paymentMandate, firstname: e.target.value})}
                placeholder="Enter first name"
                required
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Surname *</Form.Label>
              <Form.Control
                type="text"
                value={paymentMandate.surname}
                onChange={(e) => setPaymentMandate({...paymentMandate, surname: e.target.value})}
                placeholder="Enter surname"
                required
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Frequency *</Form.Label>
              <Form.Select
                value={paymentMandate.frequency}
                onChange={(e) => setPaymentMandate({...paymentMandate, frequency: e.target.value})}
              >
                {frequencyOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Premium *</Form.Label>
              <Form.Control
                type="number"
                step="0.01"
                value={paymentMandate.premium}
                onChange={(e) => setPaymentMandate({...paymentMandate, premium: e.target.value})}
                placeholder="Enter premium amount"
                required
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Mode of Payment *</Form.Label>
              <Form.Select
                value={paymentMandate.modeofpayment}
                onChange={(e) => setPaymentMandate({...paymentMandate, modeofpayment: e.target.value})}
              >
                {paymentModeOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>First Deduction Date *</Form.Label>
              <Form.Control
                type="date"
                value={paymentMandate.firstdeductiondate}
                onChange={(e) => setPaymentMandate({...paymentMandate, firstdeductiondate: e.target.value})}
                required
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Paypoint Name *</Form.Label>
              <Form.Control
                type="text"
                value={paymentMandate.paypointname}
                onChange={(e) => setPaymentMandate({...paymentMandate, paypointname: e.target.value})}
                placeholder="Enter paypoint name"
                required
              />
            </Form.Group>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );

  const renderBeneficiariesStep = () => (
    <Card>
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h5>Step 4: Beneficiaries</h5>
        <Button variant="outline-primary" size="sm" onClick={addBeneficiary}>
          <FaPlus className="me-1" />
          Add Beneficiary
        </Button>
      </Card.Header>
      <Card.Body>
        {beneficiaries.map((beneficiary, index) => (
          <div key={index} className="border rounded p-3 mb-3">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h6>Beneficiary {index + 1}</h6>
              {beneficiaries.length > 1 && (
                <Button
                  variant="outline-danger"
                  size="sm"
                  onClick={() => removeBeneficiary(index)}
                >
                  <FaTrash />
                </Button>
              )}
            </div>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Title *</Form.Label>
                  <Form.Select
                    value={beneficiary.title}
                    onChange={(e) => updateBeneficiary(index, 'title', e.target.value)}
                  >
                    {titleOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Initials *</Form.Label>
                  <Form.Control
                    type="text"
                    value={beneficiary.initials}
                    onChange={(e) => updateBeneficiary(index, 'initials', e.target.value)}
                    placeholder="e.g., J.D."
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name *</Form.Label>
                  <Form.Control
                    type="text"
                    value={beneficiary.firstname}
                    onChange={(e) => updateBeneficiary(index, 'firstname', e.target.value)}
                    placeholder="Enter first name"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Surname *</Form.Label>
                  <Form.Control
                    type="text"
                    value={beneficiary.surname}
                    onChange={(e) => updateBeneficiary(index, 'surname', e.target.value)}
                    placeholder="Enter surname"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Gender *</Form.Label>
                  <Form.Select
                    value={beneficiary.gender}
                    onChange={(e) => updateBeneficiary(index, 'gender', e.target.value)}
                  >
                    {genderOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Birth Date *</Form.Label>
                  <Form.Control
                    type="date"
                    value={beneficiary.birthdate}
                    onChange={(e) => updateBeneficiary(index, 'birthdate', e.target.value)}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Relationship *</Form.Label>
                  <Form.Control
                    type="text"
                    value={beneficiary.relationship}
                    onChange={(e) => updateBeneficiary(index, 'relationship', e.target.value)}
                    placeholder="e.g., Son, Daughter, Spouse"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
          </div>
        ))}
      </Card.Body>
    </Card>
  );

  const renderReviewStep = () => (
    <Card>
      <Card.Header>
        <h5>Step 5: Review & Submit</h5>
      </Card.Header>
      <Card.Body>
        <div className="mb-4">
          <h6>Policy Details</h6>
          <p><strong>Policy Number:</strong> {policyDetails.policyno}</p>
          <p><strong>Package:</strong> {policyDetails.packages}</p>
          <p><strong>Agent:</strong> {policyDetails.agentname} ({policyDetails.agentcode})</p>
        </div>
        <div className="mb-4">
          <h6>Policy Holder</h6>
          <p><strong>Name:</strong> {policyHolder.title} {policyHolder.firstname} {policyHolder.surname}</p>
          <p><strong>Date of Birth:</strong> {policyHolder.dateofbirth}</p>
        </div>
        <div className="mb-4">
          <h6>Payment Details</h6>
          <p><strong>Premium:</strong> {paymentMandate.premium} ({paymentMandate.frequency})</p>
          <p><strong>Mode:</strong> {paymentMandate.modeofpayment}</p>
        </div>
        <div className="mb-4">
          <h6>Beneficiaries ({beneficiaries.length})</h6>
          {beneficiaries.map((b, i) => (
            <p key={i}><strong>{i + 1}.</strong> {b.firstname} {b.surname} ({b.relationship})</p>
          ))}
        </div>
      </Card.Body>
    </Card>
  );

  return (
    <Container fluid className="p-4">
      <Row>
        <Col>
          <h2 className="mb-4">New Policy Capture</h2>
          {renderStepIndicator()}
          
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          
          {currentStep === 1 && renderPolicyDetailsStep()}
          {currentStep === 2 && renderPolicyHolderStep()}
          {currentStep === 3 && renderPaymentMandateStep()}
          {currentStep === 4 && renderBeneficiariesStep()}
          {currentStep === 5 && renderReviewStep()}
          
          <div className="d-flex justify-content-between mt-4">
            <Button 
              variant="secondary" 
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              <FaArrowLeft className="me-2" />
              Previous
            </Button>
            
            {currentStep < 5 ? (
              <Button 
                variant="primary" 
                onClick={nextStep}
                disabled={!validateStep(currentStep)}
              >
                Next
                <FaArrowRight className="ms-2" />
              </Button>
            ) : (
              <Button 
                variant="success" 
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? 'Creating...' : 'Create Policy'}
              </Button>
            )}
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default NewPolicyCapture;
