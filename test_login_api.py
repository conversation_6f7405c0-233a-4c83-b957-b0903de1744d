#!/usr/bin/env python3
"""
Test login API functionality
"""

import requests
import json

def test_login():
    """Test login with default admin user"""
    try:
        # Test login with admin credentials
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            'http://localhost:8000/login',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"   User: {data.get('user', {}).get('username', 'Unknown')}")
            print(f"   Token: {data.get('access_token', 'No token')[:20]}...")
            
            # Test token validation
            token = data.get('access_token')
            if token:
                headers = {'Authorization': f'Bearer {token}'}
                check_response = requests.get(
                    'http://localhost:8000/checktoken/',
                    headers=headers
                )
                
                if check_response.status_code == 200:
                    print("✅ Token validation successful!")
                    return True
                else:
                    print(f"❌ Token validation failed: {check_response.status_code}")
                    return False
            else:
                print("❌ No token received")
                return False
                
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Login test failed: {e}")
        return False

def test_signup():
    """Test signup functionality"""
    try:
        signup_data = {
            "username": "testuser123",
            "email": "<EMAIL>",
            "password": "testpass123",
            "status": "active",
            "viewonly": "no",
            "policyadmin": "no",
            "servicing": "yes",
            "mimoadmin": "no",
            "sysadmin": "no",
            "createdby": "admin",
            "datecreated": "2024-01-01 10:00:00"
        }
        
        response = requests.post(
            'http://localhost:8000/signup',
            json=signup_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Signup successful!")
            print(f"   New user: {data.get('username', 'Unknown')}")
            return True
        elif response.status_code == 400:
            print("⚠️  User might already exist (this is expected)")
            return True
        else:
            print(f"❌ Signup failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Signup test failed: {e}")
        return False

def main():
    print("Testing API Authentication...")
    print("=" * 50)
    
    login_ok = test_login()
    signup_ok = test_signup()
    
    print("\n" + "=" * 50)
    if login_ok and signup_ok:
        print("🎉 All authentication tests passed!")
        print("\nDefault login credentials:")
        print("- Username: admin")
        print("- Password: admin123")
        print("\nOther available users:")
        print("- policymanager/policy123")
        print("- serviceagent/service123")
        print("- viewer/viewer123")
    else:
        print("❌ Some authentication tests failed")

if __name__ == "__main__":
    main()
