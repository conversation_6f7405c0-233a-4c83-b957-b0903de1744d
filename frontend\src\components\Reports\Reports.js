import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Table, Alert, Spinner, Badge } from 'react-bootstrap';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';
import apiService from '../../services/apiService';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const Reports = () => {
  const [filters, setFilters] = useState({
    policyType: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    agentCode: '',
    premiumMin: '',
    premiumMax: ''
  });
  
  const [reportData, setReportData] = useState([]);
  const [summary, setSummary] = useState({
    totalPolicies: 0,
    totalPremiums: 0,
    activePolicies: 0,
    pendingPolicies: 0
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasGenerated, setHasGenerated] = useState(false);

  useEffect(() => {
    // Load initial data
    generateReport();
  }, []);

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const generateReport = async () => {
    setLoading(true);
    setError('');
    setHasGenerated(true);
    
    try {
      // Fetch policies data
      const policiesResponse = await apiService.getPolicies();
      let policies = policiesResponse.data;
      
      // Apply filters
      if (filters.policyType) {
        policies = policies.filter(p => p.packages.toLowerCase().includes(filters.policyType.toLowerCase()));
      }
      
      if (filters.status) {
        policies = policies.filter(p => p.status === filters.status);
      }
      
      if (filters.agentCode) {
        policies = policies.filter(p => p.agentcode.toLowerCase().includes(filters.agentCode.toLowerCase()));
      }
      
      if (filters.dateFrom) {
        policies = policies.filter(p => new Date(p.datecreated) >= new Date(filters.dateFrom));
      }
      
      if (filters.dateTo) {
        policies = policies.filter(p => new Date(p.datecreated) <= new Date(filters.dateTo));
      }
      
      // Calculate summary
      const totalPolicies = policies.length;
      const activePolicies = policies.filter(p => p.status === 'Active').length;
      const pendingPolicies = policies.filter(p => p.status === 'Pending').length;
      
      // For premium calculation, we'll use mock data since the API doesn't have premium amounts
      const totalPremiums = policies.length * 1500; // Mock calculation
      
      setSummary({
        totalPolicies,
        totalPremiums,
        activePolicies,
        pendingPolicies
      });
      
      setReportData(policies);
      
    } catch (err) {
      console.error('Error generating report:', err);
      setError('Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const clearFilters = () => {
    setFilters({
      policyType: '',
      status: '',
      dateFrom: '',
      dateTo: '',
      agentCode: '',
      premiumMin: '',
      premiumMax: ''
    });
  };

  const exportToCSV = () => {
    const headers = ['Policy No', 'Package', 'Agent Name', 'Status', 'Date Created', 'Business Unit'];
    const csvContent = [
      headers.join(','),
      ...reportData.map(policy => [
        policy.policyno,
        policy.packages,
        policy.agentname,
        policy.status,
        policy.datecreated,
        policy.bus
      ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `policy_report_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'Active': 'success',
      'Pending': 'warning',
      'Expired': 'danger',
      'Cancelled': 'secondary'
    };
    return statusMap[status] || 'secondary';
  };

  // Chart data
  const chartData = {
    labels: ['Active', 'Pending', 'Expired', 'Cancelled'],
    datasets: [
      {
        label: 'Number of Policies',
        data: [
          summary.activePolicies,
          summary.pendingPolicies,
          Math.floor(summary.totalPolicies * 0.1), // Mock expired
          Math.floor(summary.totalPolicies * 0.05)  // Mock cancelled
        ],
        backgroundColor: [
          '#28a745',
          '#ffc107',
          '#dc3545',
          '#6c757d'
        ],
        borderColor: [
          '#1e7e34',
          '#e0a800',
          '#c82333',
          '#5a6268'
        ],
        borderWidth: 1
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Policy Status Distribution'
      }
    }
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0 text-gray-800">Reports & Analytics</h1>
          <p className="text-muted">Generate comprehensive policy reports with custom filters</p>
        </Col>
      </Row>

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-filter me-2"></i>
            Report Filters
          </h5>
        </Card.Header>
        <Card.Body>
          <Form>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Policy Type</Form.Label>
                  <Form.Select
                    name="policyType"
                    value={filters.policyType}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Types</option>
                    <option value="Life">Life Insurance</option>
                    <option value="Health">Health Insurance</option>
                    <option value="Property">Property Insurance</option>
                    <option value="Auto">Auto Insurance</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Status</Form.Label>
                  <Form.Select
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Statuses</option>
                    <option value="Active">Active</option>
                    <option value="Pending">Pending</option>
                    <option value="Expired">Expired</option>
                    <option value="Cancelled">Cancelled</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date From</Form.Label>
                  <Form.Control
                    type="date"
                    name="dateFrom"
                    value={filters.dateFrom}
                    onChange={handleFilterChange}
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date To</Form.Label>
                  <Form.Control
                    type="date"
                    name="dateTo"
                    value={filters.dateTo}
                    onChange={handleFilterChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Agent Code</Form.Label>
                  <Form.Control
                    type="text"
                    name="agentCode"
                    value={filters.agentCode}
                    onChange={handleFilterChange}
                    placeholder="Enter agent code"
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Min Premium</Form.Label>
                  <Form.Control
                    type="number"
                    name="premiumMin"
                    value={filters.premiumMin}
                    onChange={handleFilterChange}
                    placeholder="0"
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Max Premium</Form.Label>
                  <Form.Control
                    type="number"
                    name="premiumMax"
                    value={filters.premiumMax}
                    onChange={handleFilterChange}
                    placeholder="10000"
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col>
                <div className="d-flex gap-2">
                  <Button 
                    variant="primary" 
                    onClick={generateReport}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Generating...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-chart-bar me-2"></i>
                        Generate Report
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline-secondary" 
                    onClick={clearFilters}
                  >
                    <i className="fas fa-times me-2"></i>
                    Clear Filters
                  </Button>
                  {hasGenerated && reportData.length > 0 && (
                    <Button 
                      variant="success" 
                      onClick={exportToCSV}
                    >
                      <i className="fas fa-download me-2"></i>
                      Export CSV
                    </Button>
                  )}
                </div>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {hasGenerated && (
        <>
          {/* Summary Cards */}
          <Row className="mb-4">
            <Col xl={3} md={6} className="mb-4">
              <Card className="metric-card h-100">
                <Card.Body>
                  <Row>
                    <Col>
                      <div className="text-xs font-weight-bold text-uppercase mb-1">
                        Total Policies
                      </div>
                      <div className="h5 mb-0 font-weight-bold">
                        {summary.totalPolicies.toLocaleString()}
                      </div>
                    </Col>
                    <Col xs="auto">
                      <i className="fas fa-file-contract fa-2x"></i>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>

            <Col xl={3} md={6} className="mb-4">
              <Card className="metric-card h-100">
                <Card.Body>
                  <Row>
                    <Col>
                      <div className="text-xs font-weight-bold text-uppercase mb-1">
                        Active Policies
                      </div>
                      <div className="h5 mb-0 font-weight-bold">
                        {summary.activePolicies.toLocaleString()}
                      </div>
                    </Col>
                    <Col xs="auto">
                      <i className="fas fa-check-circle fa-2x"></i>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>

            <Col xl={3} md={6} className="mb-4">
              <Card className="metric-card h-100">
                <Card.Body>
                  <Row>
                    <Col>
                      <div className="text-xs font-weight-bold text-uppercase mb-1">
                        Total Premiums
                      </div>
                      <div className="h5 mb-0 font-weight-bold">
                        ${summary.totalPremiums.toLocaleString()}
                      </div>
                    </Col>
                    <Col xs="auto">
                      <i className="fas fa-dollar-sign fa-2x"></i>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>

            <Col xl={3} md={6} className="mb-4">
              <Card className="metric-card h-100">
                <Card.Body>
                  <Row>
                    <Col>
                      <div className="text-xs font-weight-bold text-uppercase mb-1">
                        Pending Policies
                      </div>
                      <div className="h5 mb-0 font-weight-bold">
                        {summary.pendingPolicies.toLocaleString()}
                      </div>
                    </Col>
                    <Col xs="auto">
                      <i className="fas fa-clock fa-2x"></i>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          {/* Chart and Table */}
          <Row>
            <Col lg={4}>
              <Card className="shadow mb-4">
                <Card.Header>
                  <h6 className="m-0 font-weight-bold text-primary">Policy Distribution</h6>
                </Card.Header>
                <Card.Body>
                  <Bar data={chartData} options={chartOptions} />
                </Card.Body>
              </Card>
            </Col>

            <Col lg={8}>
              <Card className="shadow">
                <Card.Header className="py-3 d-flex flex-row align-items-center justify-content-between">
                  <h6 className="m-0 font-weight-bold text-primary">
                    Report Data ({reportData.length} records)
                  </h6>
                </Card.Header>
                <Card.Body className="p-0">
                  {loading ? (
                    <div className="text-center py-5">
                      <Spinner animation="border" variant="primary" />
                    </div>
                  ) : reportData.length > 0 ? (
                    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                      <Table responsive striped hover className="mb-0">
                        <thead className="table-light sticky-top">
                          <tr>
                            <th>Policy No</th>
                            <th>Package</th>
                            <th>Agent</th>
                            <th>Status</th>
                            <th>Applicant Signed</th>
                            <th>Date Created</th>
                          </tr>
                        </thead>
                        <tbody>
                          {reportData.map((policy) => (
                            <tr key={policy.id}>
                              <td className="fw-bold">{policy.policyno}</td>
                              <td>{policy.packages}</td>
                              <td>{policy.agentname}</td>
                              <td>
                                <Badge bg={getStatusBadge(policy.status)}>
                                  {policy.status}
                                </Badge>
                              </td>
                              <td>{policy.applicantsigneddate || '-'}</td>
                              <td>{policy.datecreated}</td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-5">
                      <i className="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                      <h5 className="text-muted">No data found</h5>
                      <p className="text-muted">Try adjusting your filters to see results.</p>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </>
      )}
    </Container>
  );
};

export default Reports;
