#!/usr/bin/env python3
"""
Check database tables
"""

from db import engine
from sqlalchemy import text

def check_tables():
    """Check what tables exist in the database"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public'"))
            tables = [row[0] for row in result]
            print("Existing tables:", tables)
            return tables
    except Exception as e:
        print(f"Error checking tables: {e}")
        return []

def create_tables():
    """Create tables using SQLAlchemy"""
    try:
        from models import Base
        Base.metadata.create_all(bind=engine)
        print("Tables created successfully")
        return True
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

if __name__ == "__main__":
    print("Checking database...")
    tables = check_tables()
    
    if not tables or 'users' not in tables:
        print("Creating tables...")
        create_tables()
        print("Checking tables again...")
        check_tables()
    else:
        print("All tables exist!")
