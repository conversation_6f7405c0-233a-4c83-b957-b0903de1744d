# from sqlalchemy.orm import declarative_base
from db import Base
from sqlalchemy import Column, Integer, String, Boolean, Index

# Base = declarative_base()

class User(Base):
    __tablename__ = 'users' 

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    status = Column(String, index=True)
    viewonly = Column(String, index=True)
    policyadmin = Column(String, index=True)
    servicing = Column(String, index=True)
    mimoadmin = Column(String, index=True)
    sysadmin = Column(String, index=True)
    createdby = Column(String, index=True)
    datecreated = Column(String, index=True)
    hashed_password = Column(String)
    password = Column(String)

    __table_args__ = (Index('ix_depart_id', 'id'),Index('ix_depart_username', 'username'))

# class Beneficiaries(Base):
# id,policyid,title,initials,firstname,middlename,surname,gender,birthdate,maritalstatus,membertype,relationship,benofownership,status,datecreated,createdby
class Beneficiaries(Base):
    __tablename__ = 'beneficiaries'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyid = Column(String, index=True)
    policyno = Column(String, index=True)  # Added missing policyno field
    title = Column(String, index=True)
    initials = Column(String, index=True)
    firstname = Column(String, index=True)
    middlename = Column(String, index=True)
    surname = Column(String, index=True)
    gender = Column(String, index=True)
    birthdate = Column(String, index=True)
    maritalstatus = Column(String, index=True)
    membertype = Column(String, index=True)
    relationship = Column(String, index=True)
    benofownership = Column(String, index=True)
    status = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)
    idnumber = Column(String, index=True)  # Added missing idnumber field

    __table_args__ = (Index('ix_ben_id', 'id'),Index('ix_ben_policyid', 'policyid'))

# class Policy(Base):
# id,policyno,packages,bus,agentcode,agentname,pep,aml,applicantsigneddate,agentsigneddate,applicationform,kycform,mandateform,schemecode,worksitecode,riskprofile,status
class Policy(Base):
    __tablename__ = 'policy'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    packages = Column(String, index=True)
    bus = Column(Boolean, default=False)
    policypayer = Column(String, index=True)
    agentcode = Column(String, index=True)
    agentname = Column(String, index=True)
    pep = Column(Boolean, default=False)
    aml = Column(Boolean, default=False)
    applicantsigneddate = Column(String, index=True, nullable=False)
    agentsigneddate = Column(String, index=True, nullable=False)
    applicationform = Column(Boolean, default=False, nullable=False)
    kycform = Column(Boolean, default=False, nullable=False)
    mandateform = Column(Boolean, default=False, nullable=False)
    schemecode = Column(String, index=True)
    worksitecode = Column(String, index=True)
    riskprofile = Column(String, index=True)
    status = Column(String, index=True)
    datecreated= Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_policy_id', 'id'),Index('ix_policy_policyno', 'policyno'))

# policyholder
# id,policyno,title,initials,middlename,surname,previousname,gender,dateofbirth,countryofbirth,maritalstatus,residentialstatus,primaryid,primaryidexpirydate,secondaryid,secondaryidexpirydate
# ,expatriate,workpermit,workpermitexpirydate,sourceofincome,netmonthlyincome,specifyincome,proofofsourceoffunds,specifyproofofsourceoffunds,occupation,industry,employer,other,commpreference
# ,emailaddress,phoneno,altmobileno,physicaladdressline1,physicaladdressline2,physicaladdressline3,physicaladdressline4,physicaladdressline5city,physicaladdressline6country
# ,permanentaddressvillage,permanentaddressta,permanentaddressdistrict
# ,postaladdressline1,postaladdressline2,postaladdressline3,postaladdressline4,postaladdressline5city,postaladdressline6country
class PolicyHolder(Base):
    __tablename__ = 'policyholder'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    title = Column(String, index=True)
    initials = Column(String, index=True)
    firstname = Column(String, index=True)  # Added missing firstname field
    middlename = Column(String, index=True)
    surname = Column(String, index=True)
    previousname = Column(String, index=True)
    gender = Column(String, index=True)
    dateofbirth = Column(String, index=True)
    countryofbirth = Column(String, index=True)
    maritalstatus = Column(String, index=True)
    residentialstatus = Column(String, index=True)
    primaryid = Column(String, index=True)
    primaryidexpirydate = Column(String, index=True)
    secondaryid = Column(String, index=True)
    secondaryidexpirydate = Column(String, index=True)
    expatriate = Column(String, index=True)
    workpermit = Column(String, index=True)
    workpermitexpirydate = Column(String, index=True)
    sourceofincome = Column(String, index=True)
    netmonthlyincome = Column(String, index=True)
    specifyincome = Column(String, index=True)
    proofofsourceoffunds = Column(String, index=True)
    specifyproofofsourceoffunds = Column(String, index=True)
    occupation = Column(String, index=True)
    industry = Column(String, index=True)
    employer = Column(String, index=True)
    other = Column(String, index=True)
    commpreference = Column(String, index=True)
    emailaddress = Column(String, index=True)
    phoneno = Column(String, index=True)
    altmobileno = Column(String, index=True)
    physicaladdressline1 = Column(String, index=True)
    physicaladdressline2 = Column(String, index=True)
    physicaladdressline3 = Column(String, index=True)
    physicaladdressline4 = Column(String, index=True)
    physicaladdressline5city = Column(String, index=True)
    physicaladdressline6country = Column(String, index=True)
    permanentaddressvillage = Column(String, index=True)
    permanentaddressta = Column(String, index=True)
    permanentaddressdistrict = Column(String, index=True)
    postaladdressline1 = Column(String, index=True)
    postaladdressline2 = Column(String, index=True)
    postaladdressline3 = Column(String, index=True)
    postaladdressline4 = Column(String, index=True)
    postaladdressline5city = Column(String, index=True)
    postaladdressline6country = Column(String, index=True)
    datecreated = Column(String, index=True)  # Added missing datecreated field
    createdby = Column(Integer, index=True)   # Added missing createdby field
    capturedate = Column(String, index=True)  # Added missing capturedate field
    idnumber = Column(String, index=True)     # Added missing idnumber field
    idtype = Column(String, index=True)       # Added missing idtype field
    mobilenumber = Column(String, index=True) # Added missing mobilenumber field
    alternativenumber = Column(String, index=True) # Added missing alternativenumber field
    postalcity = Column(String, index=True)   # Added missing postalcity field
    postalcountrycode = Column(String, index=True) # Added missing postalcountrycode field
    town = Column(String, index=True)         # Added missing town field
    residentialaddressline1 = Column(String, index=True) # Added missing residentialaddressline1 field
    residentialcountrycode = Column(String, index=True)  # Added missing residentialcountrycode field
    residentialdistrict = Column(String, index=True)     # Added missing residentialdistrict field
    residentialvillage = Column(String, index=True)      # Added missing residentialvillage field
    traditionalauthority = Column(String, index=True)    # Added missing traditionalauthority field
    contractsigneddate = Column(String, index=True)      # Added missing contractsigneddate field

    __table_args__ = (Index('ix_policyholder_id', 'id'),Index('ix_policyholder_policyno', 'policyno'))


# class PremiumPayer(Base):
#     __tablename__ = 'premiumpayer'

#     id = Column(Integer, primary_key=True, index=True)
#     policyno = Column(String, index=True)
#     title = Column(String, index=True)
#     initials = Column(String, index=True)
#     middlename = Column(String, index=True)
#     surname = Column(String, index=True)
#     previousname = Column(String, index=True)
#     gender = Column(String, index=True)
#     dateofbirth = Column(String, index=True)
#     countryofbirth = Column(String, index=True)
#     maritalstatus = Column(String, index=True)
#     residentialstatus = Column(String, index=True)
#     primaryid = Column(String, index=True)
#     primaryidexpirydate = Column(String, index=True)
#     secondaryid = Column(String, index=True)
#     secondaryidexpirydate = Column(String, index=True)
#     expatriate = Column(String, index=True)
#     workpermit = Column(String, index=True)
#     workpermitexpirydate = Column(String, index=True)
#     sourceofincome = Column(String, index=True)
#     netmonthlyincome = Column(String, index=True)
#     specifyincome = Column(String, index=True)
#     proofofsourceoffunds = Column(String, index=True)
#     specifyproofofsourceoffunds = Column(String, index=True)
#     occupation = Column(String, index=True)
#     industry = Column(String, index=True)
#     employer = Column(String, index=True)
#     other = Column(String, index=True)
#     commpreference = Column(String, index=True)
#     emailaddress = Column(String, index=True)
#     phoneno = Column(String, index=True)
#     altmobileno = Column(String, index=True)
#     physicaladdressline1 = Column(String, index=True)
#     physicaladdressline2 = Column(String, index=True)
#     physicaladdressline3 = Column(String, index=True)
#     physicaladdressline4 = Column(String, index=True)
#     physicaladdressline5city = Column(String, index=True)
#     physicaladdressline6country = Column(String, index=True)
#     permanentaddressvillage = Column(String, index=True)
#     permanentaddressta = Column(String, index=True)
#     permanentaddressdistrict = Column(String, index=True)
#     postaladdressline1 = Column(String, index=True)
#     postaladdressline2 = Column(String, index=True)
#     postaladdressline3 = Column(String, index=True)
#     postaladdressline4 = Column(String, index=True)
#     postaladdressline5city = Column(String, index=True)
#     postaladdressline6country = Column(String, index=True)

#     __table_args__ = (Index('ix_premiumpayer_id', 'id'),Index('ix_premiumpayer_policyno', 'policyno'))


# policymandate
# id,policyno,modeofpayment,frequency,premium,firstdeductiondate,debitorderFirstname,debitorderLastname,debitorderNameofbank,debitorderBranch,debitorderAccountno,debitorderTypeofaccount 
# ,debitorderSalaryfundingdate,debitorderPremiumonapplication
# ,stoporderEmployeeid,stoporderEmployeename,stoporderEmployername,stoporderEmployeraddress
# ,mobileoperator,mobilephoneno
class PolicyMandate(Base):
    __tablename__ = 'policymandate'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    modeofpayment = Column(String, index=True)
    frequency = Column(String, index=True)
    premium = Column(String, index=True)
    firstdeductiondate = Column(String, index=True)
    debitorderFirstname = Column(String, index=True)
    debitorderLastname = Column(String, index=True)
    debitorderNameofbank = Column(String, index=True)
    debitorderBranch = Column(String, index=True)
    debitorderAccountno = Column(String, index=True)
    debitorderTypeofaccount = Column(String, index=True)
    debitorderSalaryfundingdate = Column(String, index=True)
    debitorderPremiumonapplication = Column(String, index=True)
    stoporderEmployeeid = Column(String, index=True)
    stoporderEmployeename = Column(String, index=True)
    stoporderEmployername = Column(String, index=True)
    stoporderEmployeraddress = Column(String, index=True)
    mobileoperator = Column(String, index=True)
    mobilephoneno = Column(String, index=True)
    datecreated = Column(String, index=True)  # Added missing datecreated field
    createdby = Column(Integer, index=True)   # Added missing createdby field

    __table_args__ = (Index('ix_policymandate_id', 'id'), Index('ix_policymandate_policyno', 'policyno'))

# policysummary
# id,policyno,premiumsdue,premiumspaid,totalpremiumspaid,outstanding 
class PolicySummary(Base):
    __tablename__ = 'policysummary'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    premiumsdue = Column(String, index=True)
    premiumspaid = Column(String, index=True)
    totalpremiumspaid = Column(String, index=True)
    outstanding = Column(String, index=True)

    __table_args__ = (Index('ix_policysummary_id', 'id'), Index('ix_policysummary_policyno', 'policyno'))

class PremiumPayer(Base):
    __tablename__ = 'premiumpayer'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    title = Column(String, index=True)
    initials = Column(String, index=True)
    middlename = Column(String, index=True)
    surname = Column(String, index=True)
    previousname = Column(String, index=True)
    gender = Column(String, index=True)
    dateofbirth = Column(String, index=True)
    countryofbirth = Column(String, index=True)
    maritalstatus = Column(String, index=True)
    residentialstatus = Column(String, index=True)
    primaryid = Column(String, index=True)
    primaryidexpirydate = Column(String, index=True)
    secondaryid = Column(String, index=True)
    secondaryidexpirydate = Column(String, index=True)
    expatriate = Column(String, index=True)
    workpermit = Column(String, index=True)
    workpermitexpirydate = Column(String, index=True)
    sourceofincome = Column(String, index=True)
    netmonthlyincome = Column(String, index=True)
    specifyincome = Column(String, index=True)
    proofofsourceoffunds = Column(String, index=True)
    specifyproofofsourceoffunds = Column(String, index=True)
    occupation = Column(String, index=True)
    industry = Column(String, index=True)
    employer = Column(String, index=True)
    other = Column(String, index=True)
    commpreference = Column(String, index=True)
    emailaddress = Column(String, index=True)
    phoneno = Column(String, index=True)
    altmobileno = Column(String, index=True)
    physicaladdressline1 = Column(String, index=True)
    physicaladdressline2 = Column(String, index=True)
    physicaladdressline3 = Column(String, index=True)
    physicaladdressline4 = Column(String, index=True)
    physicaladdressline5city = Column(String, index=True)
    physicaladdressline6country = Column(String, index=True)
    permanentaddressvillage = Column(String, index=True)
    permanentaddressta = Column(String, index=True)
    permanentaddressdistrict = Column(String, index=True)
    postaladdressline1 = Column(String, index=True)
    postaladdressline2 = Column(String, index=True)
    postaladdressline3 = Column(String, index=True)
    postaladdressline4 = Column(String, index=True)
    postaladdressline5city = Column(String, index=True)
    postaladdressline6country = Column(String, index=True)

    __table_args__ = (Index('ix_premiumpayer_id', 'id'),Index('ix_premiumpayer_policyno', 'policyno'))

# policyactivities
# id,policyid,activitydesc,remarks,status,datecreated,createdby,activitydetails,effectivedate
class PolicyActivities(Base):
    __tablename__ = 'policyactivities'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyid = Column(String, index=True)
    policyno = Column(String, index=True)
    activitydesc = Column(String, index=True)
    remarks = Column(String, index=True)
    status = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)
    activitydetails = Column(String, index=True)
    effectivedate = Column(String, index=True)

    __table_args__ = (Index('ix_policyactivities_id', 'id'), Index('ix_policyactivities_policyid', 'policyid'), Index('ix_policyactivities_policyno', 'policyno'))