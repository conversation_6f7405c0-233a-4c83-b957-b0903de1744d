<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import Validation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Import Validation</h1>
        
        <div class="form-group">
            <label for="token">JWT Token (get from browser localStorage):</label>
            <input type="text" id="token" placeholder="Paste your JWT token here">
            <small>Open browser dev tools → Application → Local Storage → token</small>
        </div>
        
        <div class="form-group">
            <label for="file">Select CSV File:</label>
            <input type="file" id="file" accept=".csv,.xlsx,.xls">
        </div>
        
        <div class="form-group">
            <label for="importType">Import Type:</label>
            <select id="importType">
                <option value="policy">Policy</option>
                <option value="policyholder">Policy Holder</option>
                <option value="policymandate">Policy Mandate</option>
                <option value="beneficiaries">Beneficiaries</option>
            </select>
        </div>
        
        <button onclick="testValidation()">Test Validation</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function testValidation() {
            const token = document.getElementById('token').value;
            const fileInput = document.getElementById('file');
            const importType = document.getElementById('importType').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">Please enter a JWT token</div>';
                return;
            }
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="result error">Please select a file</div>';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('import_type', importType);
            
            try {
                console.log('Testing validation with:', {
                    filename: fileInput.files[0].name,
                    size: fileInput.files[0].size,
                    type: fileInput.files[0].type,
                    importType: importType
                });
                
                const response = await fetch('http://localhost:8000/imports/validate', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Validation Successful</h3>
                            <p><strong>Status:</strong> ${data.status}</p>
                            <p><strong>Total Rows:</strong> ${data.total_rows}</p>
                            <p><strong>Columns:</strong> ${data.columns.length}</p>
                            ${data.missing_columns.length > 0 ? 
                                `<p><strong>⚠️ Missing Columns:</strong> ${data.missing_columns.join(', ')}</p>` : 
                                '<p><strong>✅ All required columns present</strong></p>'
                            }
                            <details>
                                <summary>Full Response</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Validation Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                            <details>
                                <summary>Full Response</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
