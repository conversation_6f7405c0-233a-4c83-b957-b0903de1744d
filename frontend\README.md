# Mthunzi Policy Management Frontend

A modern React-based frontend application for the Mthunzi Policy Management System, featuring a green, white, and black color scheme with Bootstrap styling.

## Features

- **User Authentication**: Secure login/logout with JWT token management
- **Dashboard**: Key metrics and policy statistics with interactive charts
- **Policy Management**: 
  - Create new policies with comprehensive forms
  - View all policies with filtering and sorting
  - Edit existing policy details
  - View detailed policy information
- **Search Functionality**: Search policies by policy number, policyholder name, or policy type
- **Reports**: Generate policy reports with custom filters and export to CSV
- **Responsive Design**: Mobile-friendly interface using Bootstrap

## Technology Stack

- **React 18**: Modern React with hooks
- **React Router 6**: Client-side routing
- **Bootstrap 5**: UI framework with custom green/white/black theme
- **React Bootstrap**: Bootstrap components for React
- **Axios**: HTTP client for API calls
- **Chart.js**: Interactive charts and graphs
- **Font Awesome**: Icons

## Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager
- Backend API running on http://localhost:5000

## Installation

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the frontend directory (optional):
   ```
   REACT_APP_API_URL=http://localhost:5000
   ```

4. Start the development server:
   ```bash
   npm start
   ```

5. Open your browser and navigate to `http://localhost:3000`

## Available Scripts

- `npm start`: Runs the app in development mode
- `npm build`: Builds the app for production
- `npm test`: Launches the test runner
- `npm eject`: Ejects from Create React App (one-way operation)

## Project Structure

```
frontend/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── components/
│   │   ├── Auth/
│   │   │   └── Login.js
│   │   ├── Dashboard/
│   │   │   └── Dashboard.js
│   │   ├── Layout/
│   │   │   ├── Navbar.js
│   │   │   └── Sidebar.js
│   │   ├── Policy/
│   │   │   ├── PolicyList.js
│   │   │   ├── PolicyForm.js
│   │   │   └── PolicyDetails.js
│   │   ├── Reports/
│   │   │   └── Reports.js
│   │   └── Search/
│   │       └── Search.js
│   ├── contexts/
│   │   └── AuthContext.js
│   ├── services/
│   │   └── apiService.js
│   ├── App.js
│   ├── index.js
│   └── index.css
├── package.json
└── README.md
```

## API Integration

The frontend integrates with the FastAPI backend through the following endpoints:

- **Authentication**: `/login`, `/signup`, `/checktoken`
- **Policies**: `/policies/` (CRUD operations)
- **Policyholders**: `/policyholder/` (CRUD operations)
- **Search**: `/policies/search/{term}`, `/policyholder/search/{term}`
- **Reports**: Various endpoints for data aggregation

## Theme Customization

The application uses a custom green, white, and black color scheme:

- **Primary Green**: #28a745
- **Dark Green**: #1e7e34
- **Light Green**: #d4edda
- **Primary Black**: #212529
- **Light Gray**: #f8f9fa
- **White**: #ffffff

## Features Overview

### Dashboard
- Policy statistics and metrics
- Interactive charts showing policy distribution
- Recent policies table
- Quick action buttons

### Policy Management
- Comprehensive policy creation form
- Policy listing with search and filters
- Detailed policy view with related information
- Edit functionality for existing policies

### Search
- Multi-type search (policies, policyholders)
- Real-time search results
- Advanced filtering options

### Reports
- Custom report generation with filters
- Visual charts and graphs
- CSV export functionality
- Summary statistics

## Authentication

The application uses JWT-based authentication:
- Tokens are stored in localStorage
- Automatic token validation on app load
- Protected routes require authentication
- Automatic logout on token expiration

## Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is proprietary software for Mthunzi Policy Management System.
