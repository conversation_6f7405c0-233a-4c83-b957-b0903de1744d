import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, Form, Button, Table, Al<PERSON>, Spin<PERSON>, Badge, InputGroup } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';

const Search = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState('policy');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);
  const navigate = useNavigate();

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!searchTerm.trim()) {
      setError('Please enter a search term');
      return;
    }

    setLoading(true);
    setError('');
    setHasSearched(true);
    
    try {
      let response;
      
      switch (searchType) {
        case 'policy':
          response = await apiService.searchPolicies(searchTerm);
          break;
        case 'policyholder':
          response = await apiService.searchPolicyholders(searchTerm);
          break;
        default:
          response = await apiService.searchPolicies(searchTerm);
      }
      
      setResults(response.data);
    } catch (err) {
      console.error('Search error:', err);
      setError('Search failed. Please try again.');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchTerm('');
    setResults([]);
    setError('');
    setHasSearched(false);
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'Active': 'success',
      'Pending': 'warning',
      'Expired': 'danger',
      'Cancelled': 'secondary'
    };
    return statusMap[status] || 'secondary';
  };

  const renderPolicyResults = () => (
    <Table responsive hover>
      <thead className="table-light">
        <tr>
          <th>Policy No</th>
          <th>Agent</th>
          <th>Package</th>
          <th>Business</th>
          <th>Status</th>
          <th>Applicant Signed</th>
          <th>Date Created</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {results.map((policy) => (
          <tr key={policy.id}>
            <td className="fw-bold">{policy.policyno}</td>
            <td>{policy.agentname}</td>
            <td>{policy.packages}</td>
            <td>{policy.bus}</td>
            <td>
              <Badge bg={getStatusBadge(policy.status)}>
                {policy.status}
              </Badge>
            </td>
            <td>{policy.applicantsigneddate || '-'}</td>
            <td>{policy.datecreated}</td>
            <td>
              <div className="d-flex gap-2">
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={() => navigate(`/policies/${policy.id}`)}
                >
                  <i className="fas fa-eye"></i>
                </Button>
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => navigate(`/policies/${policy.id}/edit`)}
                >
                  <i className="fas fa-edit"></i>
                </Button>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );

  const renderPolicyholderResults = () => (
    <Table responsive hover>
      <thead className="table-light">
        <tr>
          <th>Policy No</th>
          <th>Name</th>
          <th>Gender</th>
          <th>Date of Birth</th>
          <th>Primary ID</th>
          <th>Email</th>
          <th>Phone</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {results.map((holder) => (
          <tr key={holder.id}>
            <td className="fw-bold">{holder.policyno}</td>
            <td>{`${holder.title} ${holder.initials} ${holder.surname}`}</td>
            <td>{holder.gender}</td>
            <td>{holder.dateofbirth}</td>
            <td>{holder.primaryid}</td>
            <td>{holder.emailaddress || 'N/A'}</td>
            <td>{holder.phoneno || 'N/A'}</td>
            <td>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => navigate(`/policies?search=${holder.policyno}`)}
              >
                <i className="fas fa-eye"></i>
              </Button>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0 text-gray-800">Search Policies</h1>
          <p className="text-muted">Search for policies, policyholders, and related information</p>
        </Col>
      </Row>

      {/* Search Form */}
      <Card className="search-container mb-4">
        <Card.Body>
          <Form onSubmit={handleSearch}>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Search Type</Form.Label>
                  <Form.Select
                    value={searchType}
                    onChange={(e) => setSearchType(e.target.value)}
                  >
                    <option value="policy">Policy</option>
                    <option value="policyholder">Policyholder</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={7}>
                <Form.Group className="mb-3">
                  <Form.Label>Search Term</Form.Label>
                  <InputGroup>
                    <InputGroup.Text>
                      <i className="fas fa-search"></i>
                    </InputGroup.Text>
                    <Form.Control
                      type="text"
                      placeholder={
                        searchType === 'policy' 
                          ? "Enter policy number, agent name, or package type..."
                          : "Enter policy number or policyholder name..."
                      }
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={2} className="d-flex align-items-end">
                <div className="d-flex gap-2 w-100">
                  <Button 
                    type="submit" 
                    variant="primary" 
                    disabled={loading}
                    className="flex-grow-1"
                  >
                    {loading ? (
                      <Spinner
                        as="span"
                        animation="border"
                        size="sm"
                        role="status"
                        aria-hidden="true"
                      />
                    ) : (
                      <i className="fas fa-search"></i>
                    )}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline-secondary" 
                    onClick={clearSearch}
                  >
                    <i className="fas fa-times"></i>
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Search Results */}
      {hasSearched && (
        <Card className="shadow">
          <Card.Header className="py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 className="m-0 font-weight-bold text-primary">
              Search Results ({results.length} found)
            </h6>
            {results.length > 0 && (
              <small className="text-muted">
                Showing results for "{searchTerm}" in {searchType}s
              </small>
            )}
          </Card.Header>
          <Card.Body className="p-0">
            {loading ? (
              <div className="text-center py-5">
                <Spinner animation="border" variant="primary" />
                <p className="mt-3 text-muted">Searching...</p>
              </div>
            ) : results.length > 0 ? (
              searchType === 'policy' ? renderPolicyResults() : renderPolicyholderResults()
            ) : (
              <div className="text-center py-5">
                <i className="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 className="text-muted">No results found</h5>
                <p className="text-muted">
                  No {searchType}s found matching "{searchTerm}". Try adjusting your search terms.
                </p>
              </div>
            )}
          </Card.Body>
        </Card>
      )}

      {/* Search Tips */}
      {!hasSearched && (
        <Card className="mt-4">
          <Card.Header>
            <h5 className="mb-0">
              <i className="fas fa-lightbulb me-2"></i>
              Search Tips
            </h5>
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Policy Search</h6>
                <ul className="list-unstyled">
                  <li><i className="fas fa-check text-success me-2"></i>Search by policy number</li>
                  <li><i className="fas fa-check text-success me-2"></i>Search by agent name</li>
                  <li><i className="fas fa-check text-success me-2"></i>Search by package type</li>
                  <li><i className="fas fa-check text-success me-2"></i>Partial matches supported</li>
                </ul>
              </Col>
              <Col md={6}>
                <h6>Policyholder Search</h6>
                <ul className="list-unstyled">
                  <li><i className="fas fa-check text-success me-2"></i>Search by policy number</li>
                  <li><i className="fas fa-check text-success me-2"></i>Search by policyholder name</li>
                  <li><i className="fas fa-check text-success me-2"></i>Search by ID number</li>
                  <li><i className="fas fa-check text-success me-2"></i>Case insensitive search</li>
                </ul>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      )}
    </Container>
  );
};

export default Search;
