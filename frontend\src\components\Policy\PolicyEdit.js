import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, Form, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { FaUser, FaCreditCard, FaFileContract, FaUsers, FaCheck, FaExclamationTriangle, FaArrowLeft, FaSave } from 'react-icons/fa';
import apiService from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';
import { PolicyHolderStep, PremiumPayerStep, PolicyMandateStep, BeneficiariesStep } from './PolicySteps';

const PolicyEdit = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { id } = useParams();
  const { user } = useAuth();

  // Policy form data - matching the comprehensive PolicyForm structure
  const [policyData, setPolicyData] = useState({
    policyno: '',
    packages: '',
    bus: false,
    policypayer: '',
    agentcode: '',
    agentname: '',
    pep: false,
    aml: false,
    applicantsigneddate: '',
    agentsigneddate: '',
    applicationform: false,
    kycform: false,
    mandateform: false,
    schemecode: '',
    worksitecode: '',
    riskprofile: '',
    status: 'Active',
    datecreated: new Date().toISOString().split('T')[0],
    createdby: user?.id || 1
  });

  // Policy Holder data - comprehensive structure
  const [policyHolderData, setPolicyHolderData] = useState({
    policyno: '',
    title: '',
    initials: '',
    firstname: '',
    middlename: '',
    surname: '',
    previousname: '',
    gender: '',
    dateofbirth: '',
    countryofbirth: '',
    maritalstatus: '',
    residentialstatus: '',
    primaryid: '',
    primaryidexpirydate: '',
    secondaryid: '',
    secondaryidexpirydate: '',
    expatriate: '',
    workpermit: '',
    workpermitexpirydate: '',
    sourceofincome: '',
    netmonthlyincome: '',
    specifyincome: '',
    proofofsourceoffunds: '',
    specifyproofofsourceoffunds: '',
    occupation: '',
    industry: '',
    employer: '',
    other: '',
    commpreference: '',
    emailaddress: '',
    phoneno: '',
    altmobileno: '',
    physicaladdressline1: '',
    physicaladdressline2: '',
    physicaladdressline3: '',
    physicaladdressline4: '',
    physicaladdressline5city: '',
    physicaladdressline6country: '',
    permanentaddressvillage: '',
    permanentaddressta: '',
    permanentaddressdistrict: '',
    postaladdressline1: '',
    postaladdressline2: '',
    postaladdressline3: '',
    postaladdressline4: '',
    postaladdressline5city: '',
    postaladdressline6country: '',
    datecreated: new Date().toISOString().split('T')[0],
    createdby: user?.id || 1
  });

  // Premium Payer data - comprehensive structure
  const [premiumPayerData, setPremiumPayerData] = useState({
    policyno: '',
    title: '',
    initials: '',
    firstname: '',
    middlename: '',
    surname: '',
    previousname: '',
    gender: '',
    dateofbirth: '',
    countryofbirth: '',
    maritalstatus: '',
    residentialstatus: '',
    primaryid: '',
    primaryidexpirydate: '',
    secondaryid: '',
    secondaryidexpirydate: '',
    expatriate: '',
    workpermit: '',
    workpermitexpirydate: '',
    sourceofincome: '',
    netmonthlyincome: '',
    specifyincome: '',
    proofofsourceoffunds: '',
    specifyproofofsourceoffunds: '',
    occupation: '',
    industry: '',
    employer: '',
    other: '',
    commpreference: '',
    emailaddress: '',
    phoneno: '',
    altmobileno: '',
    physicaladdressline1: '',
    physicaladdressline2: '',
    physicaladdressline3: '',
    physicaladdressline4: '',
    physicaladdressline5city: '',
    physicaladdressline6country: '',
    permanentaddressvillage: '',
    permanentaddressta: '',
    permanentaddressdistrict: '',
    postaladdressline1: '',
    postaladdressline2: '',
    postaladdressline3: '',
    postaladdressline4: '',
    postaladdressline5city: '',
    postaladdressline6country: '',
    datecreated: new Date().toISOString().split('T')[0],
    createdby: user?.id || 1
  });

  // Policy Mandate data - comprehensive structure
  const [policyMandateData, setPolicyMandateData] = useState({
    policyno: '',
    modeofpayment: '',
    frequency: '',
    premium: '',
    firstdeductiondate: '',
    debitorderFirstname: '',
    debitorderLastname: '',
    debitorderNameofbank: '',
    debitorderBranch: '',
    debitorderAccountno: '',
    debitorderTypeofaccount: '',
    debitorderSalaryfundingdate: '',
    debitorderPremiumonapplication: '',
    stoporderEmployeeid: '',
    stoporderEmployeename: '',
    stoporderEmployername: '',
    stoporderEmployeraddress: '',
    mobileoperator: '',
    mobilephoneno: '',
    datecreated: new Date().toISOString().split('T')[0],
    createdby: user?.id || 1
  });

  // Beneficiaries data - comprehensive structure
  const [beneficiariesData, setBeneficiariesData] = useState([
    {
      policyno: '',
      title: '',
      initials: '',
      firstname: '',
      middlename: '',
      surname: '',
      gender: '',
      birthdate: '',
      maritalstatus: '',
      membertype: '',
      relationship: '',
      benofownership: '',
      status: 'ACTIVE',
      datecreated: new Date().toISOString().split('T')[0],
      createdby: user?.id || 1,
      idnumber: ''
    }
  ]);

  const [sameAsPolicyHolder, setSameAsPolicyHolder] = useState(false);

  useEffect(() => {
    fetchPolicyData();
  }, [id]);

  // Handler functions for form changes
  const handlePolicyChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPolicyData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handlePolicyHolderChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPolicyHolderData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handlePremiumPayerChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPremiumPayerData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handlePolicyMandateChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPolicyMandateData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleBeneficiaryChange = (index, field, value) => {
    const updatedBeneficiaries = [...beneficiariesData];
    updatedBeneficiaries[index][field] = value;
    setBeneficiariesData(updatedBeneficiaries);
  };

  const addBeneficiary = () => {
    setBeneficiariesData([...beneficiariesData, {
      policyno: policyData.policyno,
      title: '',
      initials: '',
      firstname: '',
      middlename: '',
      surname: '',
      gender: '',
      birthdate: '',
      maritalstatus: '',
      membertype: '',
      relationship: '',
      benofownership: '',
      status: 'ACTIVE',
      datecreated: new Date().toISOString().split('T')[0],
      createdby: user?.id || 1,
      idnumber: ''
    }]);
  };

  const removeBeneficiary = (index) => {
    if (beneficiariesData.length > 1) {
      setBeneficiariesData(beneficiariesData.filter((_, i) => i !== index));
    }
  };

  const fetchPolicyData = async () => {
    try {
      setLoading(true);

      // Fetch comprehensive policy data from the policy-management endpoint
      const response = await apiService.get(`/policy-management/${id}`);

      const data = response.data.data;

      // Populate policy data
      if (data.policy) {
        const policy = data.policy;
        setPolicyData({
          ...policyData,
          policyno: policy.policyno || '',
          packages: policy.packages || '',
          bus: policy.bus || false,
          policypayer: policy.policypayer || '',
          agentcode: policy.agentcode || '',
          agentname: policy.agentname || '',
          pep: policy.pep || false,
          aml: policy.aml || false,
          applicantsigneddate: policy.applicantsigneddate || '',
          agentsigneddate: policy.agentsigneddate || '',
          applicationform: policy.applicationform || false,
          kycform: policy.kycform || false,
          mandateform: policy.mandateform || false,
          schemecode: policy.schemecode || '',
          worksitecode: policy.worksitecode || '',
          riskprofile: policy.riskprofile || '',
          status: policy.status || 'Active'
        });
      }

      // Populate policy holder data
      if (data.holder) {
        const holder = data.holder;
        setPolicyHolderData({
          ...policyHolderData,
          policyno: holder.policyno || data.policy?.policyno || '',
          title: holder.title || '',
          initials: holder.initials || '',
          firstname: holder.firstname || '',
          middlename: holder.middlename || '',
          surname: holder.surname || '',
          previousname: holder.previousname || '',
          gender: holder.gender || '',
          dateofbirth: holder.dateofbirth || '',
          countryofbirth: holder.countryofbirth || '',
          maritalstatus: holder.maritalstatus || '',
          residentialstatus: holder.residentialstatus || '',
          primaryid: holder.primaryid || '',
          primaryidexpirydate: holder.primaryidexpirydate || '',
          secondaryid: holder.secondaryid || '',
          secondaryidexpirydate: holder.secondaryidexpirydate || '',
          expatriate: holder.expatriate || '',
          workpermit: holder.workpermit || '',
          workpermitexpirydate: holder.workpermitexpirydate || '',
          sourceofincome: holder.sourceofincome || '',
          netmonthlyincome: holder.netmonthlyincome || '',
          specifyincome: holder.specifyincome || '',
          proofofsourceoffunds: holder.proofofsourceoffunds || '',
          specifyproofofsourceoffunds: holder.specifyproofofsourceoffunds || '',
          occupation: holder.occupation || '',
          industry: holder.industry || '',
          employer: holder.employer || '',
          other: holder.other || '',
          commpreference: holder.commpreference || '',
          emailaddress: holder.emailaddress || '',
          phoneno: holder.phoneno || '',
          altmobileno: holder.altmobileno || '',
          physicaladdressline1: holder.physicaladdressline1 || '',
          physicaladdressline2: holder.physicaladdressline2 || '',
          physicaladdressline3: holder.physicaladdressline3 || '',
          physicaladdressline4: holder.physicaladdressline4 || '',
          physicaladdressline5city: holder.physicaladdressline5city || '',
          physicaladdressline6country: holder.physicaladdressline6country || '',
          permanentaddressvillage: holder.permanentaddressvillage || '',
          permanentaddressta: holder.permanentaddressta || '',
          permanentaddressdistrict: holder.permanentaddressdistrict || '',
          postaladdressline1: holder.postaladdressline1 || '',
          postaladdressline2: holder.postaladdressline2 || '',
          postaladdressline3: holder.postaladdressline3 || '',
          postaladdressline4: holder.postaladdressline4 || '',
          postaladdressline5city: holder.postaladdressline5city || '',
          postaladdressline6country: holder.postaladdressline6country || ''
        });
      }

      // Populate policy mandate data
      if (data.mandate) {
        const mandate = data.mandate;
        setPolicyMandateData({
          ...policyMandateData,
          policyno: mandate.policyno || data.policy?.policyno || '',
          modeofpayment: mandate.modeofpayment || '',
          frequency: mandate.frequency || '',
          premium: mandate.premium || '',
          firstdeductiondate: mandate.firstdeductiondate || '',
          debitorderFirstname: mandate.debitorderFirstname || '',
          debitorderLastname: mandate.debitorderLastname || '',
          debitorderNameofbank: mandate.debitorderNameofbank || '',
          debitorderBranch: mandate.debitorderBranch || '',
          debitorderAccountno: mandate.debitorderAccountno || '',
          debitorderTypeofaccount: mandate.debitorderTypeofaccount || '',
          debitorderSalaryfundingdate: mandate.debitorderSalaryfundingdate || '',
          debitorderPremiumonapplication: mandate.debitorderPremiumonapplication || '',
          stoporderEmployeeid: mandate.stoporderEmployeeid || '',
          stoporderEmployeename: mandate.stoporderEmployeename || '',
          stoporderEmployername: mandate.stoporderEmployername || '',
          stoporderEmployeraddress: mandate.stoporderEmployeraddress || '',
          mobileoperator: mandate.mobileoperator || '',
          mobilephoneno: mandate.mobilephoneno || ''
        });
      }

      // Populate beneficiaries data
      if (data.beneficiaries && data.beneficiaries.length > 0) {
        setBeneficiariesData(data.beneficiaries.map(b => ({
          policyno: b.policyno || data.policy?.policyno || '',
          title: b.title || '',
          initials: b.initials || '',
          firstname: b.firstname || '',
          middlename: b.middlename || '',
          surname: b.surname || '',
          gender: b.gender || '',
          birthdate: b.birthdate || '',
          maritalstatus: b.maritalstatus || '',
          membertype: b.membertype || '',
          relationship: b.relationship || '',
          benofownership: b.benofownership || '',
          status: b.status || 'ACTIVE',
          idnumber: b.idnumber || '',
          datecreated: new Date().toISOString().split('T')[0],
          createdby: user?.id || 1
        })));
      }

    } catch (err) {
      setError('Error fetching policy data: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  // Copy policy holder data to premium payer
  const copyPolicyHolderToPremiumPayer = () => {
    setPremiumPayerData({
      ...policyHolderData,
      datecreated: new Date().toISOString().split('T')[0],
      createdby: user?.id || 1
    });
    setSameAsPolicyHolder(true);
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step) => {
    setCurrentStep(step);
  };

  // Comprehensive save function
  const handleSave = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Prepare the comprehensive update payload
      const payload = {
        policy_details: {
          policyno: policyData.policyno,
          packages: policyData.packages,
          bus: policyData.bus,
          pep: policyData.pep,
          agentcode: policyData.agentcode,
          agentname: policyData.agentname,
          status: policyData.status,
          schemecode: policyData.schemecode,
          worksitecode: policyData.worksitecode,
          riskprofile: policyData.riskprofile
        },
        policy_holder: {
          title: policyHolderData.title,
          initials: policyHolderData.initials,
          firstname: policyHolderData.firstname,
          middlename: policyHolderData.middlename,
          surname: policyHolderData.surname,
          gender: policyHolderData.gender,
          dateofbirth: policyHolderData.dateofbirth,
          maritalstatus: policyHolderData.maritalstatus,
          capturedate: policyHolderData.datecreated,
          idnumber: policyHolderData.primaryid,
          occupation: policyHolderData.occupation,
          mobilenumber: policyHolderData.phoneno,
          alternativenumber: policyHolderData.altmobileno,
          emailaddress: policyHolderData.emailaddress,
          town: policyHolderData.physicaladdressline5city,
          residentialstatus: policyHolderData.residentialstatus
        },
        payment_mandate: {
          firstname: policyMandateData.debitorderFirstname,
          surname: policyMandateData.debitorderLastname,
          mobilenumber: policyMandateData.mobilephoneno,
          frequency: policyMandateData.frequency,
          premium: policyMandateData.premium,
          modeofpayment: policyMandateData.modeofpayment,
          firstdeductiondate: policyMandateData.firstdeductiondate,
          paypointname: policyMandateData.stoporderEmployername,
          bankaccountnumber: policyMandateData.debitorderAccountno,
          bankaccounttype: policyMandateData.debitorderTypeofaccount,
          branchname: policyMandateData.debitorderBranch
        },
        beneficiaries: beneficiariesData.map(b => ({
          title: b.title,
          initials: b.initials,
          firstname: b.firstname,
          surname: b.surname,
          gender: b.gender,
          birthdate: b.birthdate,
          relationship: b.relationship,
          membertype: b.membertype,
          idnumber: b.idnumber
        }))
      };

      // Use the existing policy-management update endpoint
      const response = await apiService.put(`/policy-management/${id}`, payload);

      setSuccess('Policy updated successfully!');

      // Redirect back to policy details after a short delay
      setTimeout(() => {
        navigate(`/policy-management/${id}`);
      }, 2000);

    } catch (err) {
      setError('Error updating policy: ' + (err.response?.data?.detail || err.message));
    } finally {
      setSaving(false);
    }
  };

  // Step configuration
  const steps = [
    { number: 1, title: 'Policy Holder', icon: FaUser, component: 'policyholder' },
    { number: 2, title: 'Premium Payer', icon: FaCreditCard, component: 'premiumpayer' },
    { number: 3, title: 'Policy Mandate', icon: FaFileContract, component: 'policymandate' },
    { number: 4, title: 'Beneficiaries', icon: FaUsers, component: 'beneficiaries' }
  ];

  if (loading) {
    return (
      <Container fluid className="p-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading policy data...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="p-4">
      <Row>
        <Col>
          {/* Header */}
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h2>Edit Policy - {policyData.policyno}</h2>
            <div className="d-flex gap-2">
              <Button variant="secondary" onClick={() => navigate(`/policy-management/${id}`)}>
                <FaArrowLeft className="me-2" />
                Cancel
              </Button>
              <Button variant="success" onClick={handleSave} disabled={saving}>
                <FaSave className="me-2" />
                {saving ? 'Saving...' : 'Save All Changes'}
              </Button>
            </div>
          </div>

          {/* Alerts */}
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}

          {/* Step Navigation */}
          <Card className="mb-4">
            <Card.Body>
              <Row className="text-center">
                {steps.map((step) => (
                  <Col key={step.number} md={3}>
                    <div
                      className="step-indicator"
                      onClick={() => goToStep(step.number)}
                      style={{ cursor: 'pointer', padding: '10px' }}
                    >
                      <div
                        className={`step-circle d-flex align-items-center justify-content-center mx-auto mb-2 ${
                          currentStep >= step.number ? 'bg-primary text-white' : 'bg-light text-muted'
                        }`}
                        style={{
                          width: '50px',
                          height: '50px',
                          borderRadius: '50%',
                          border: currentStep === step.number ? '3px solid #0d6efd' : '2px solid #dee2e6'
                        }}
                      >
                        {currentStep > step.number ? <FaCheck /> : <step.icon />}
                      </div>
                      <div className={`step-title ${currentStep === step.number ? 'text-primary fw-bold' : ''}`}>
                        {step.title}
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </Card.Body>
          </Card>

          {/* Step Content */}
          <Card>
            <Card.Body>
              {currentStep === 1 && (
                <PolicyHolderStep
                  policyHolderData={policyHolderData}
                  handlePolicyHolderChange={handlePolicyHolderChange}
                />
              )}

              {currentStep === 2 && (
                <PremiumPayerStep
                  premiumPayerData={premiumPayerData}
                  handlePremiumPayerChange={handlePremiumPayerChange}
                  sameAsPolicyHolder={sameAsPolicyHolder}
                  setSameAsPolicyHolder={setSameAsPolicyHolder}
                />
              )}

              {currentStep === 3 && (
                <PolicyMandateStep
                  policyMandateData={policyMandateData}
                  handlePolicyMandateChange={handlePolicyMandateChange}
                />
              )}

              {currentStep === 4 && (
                <BeneficiariesStep
                  beneficiariesData={beneficiariesData}
                  handleBeneficiaryChange={handleBeneficiaryChange}
                  addBeneficiary={addBeneficiary}
                  removeBeneficiary={removeBeneficiary}
                />
              )}
            </Card.Body>
          </Card>

          {/* Navigation Buttons */}
          <div className="d-flex justify-content-between mt-4">
            <Button
              variant="outline-secondary"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              Previous
            </Button>

            <div className="d-flex gap-2">
              {currentStep < 4 ? (
                <Button variant="primary" onClick={nextStep}>
                  Next
                </Button>
              ) : (
                <Button variant="success" onClick={handleSave} disabled={saving}>
                  <FaSave className="me-2" />
                  {saving ? 'Saving...' : 'Save All Changes'}
                </Button>
              )}
            </div>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default PolicyEdit;
