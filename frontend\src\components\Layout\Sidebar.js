import React from 'react';
import { Nav } from 'react-bootstrap';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  const menuItems = [
    {
      path: '/dashboard',
      icon: 'fas fa-tachometer-alt',
      label: 'Dashboard',
      active: location.pathname === '/dashboard' || location.pathname === '/'
    },
    {
      path: '/policies',
      icon: 'fas fa-file-contract',
      label: 'All Policies',
      active: location.pathname === '/policies'
    },
    {
      path: '/policies/new',
      icon: 'fas fa-plus-circle',
      label: 'New Policy (Comprehensive)',
      active: location.pathname === '/policies/new'
    },
    {
      path: '/policy-capture',
      icon: 'fas fa-plus-square',
      label: 'New Policy (Quick)',
      active: location.pathname === '/policy-capture'
    },
    {
      path: '/policy-search',
      icon: 'fas fa-search-plus',
      label: 'Policy Search & Management',
      active: location.pathname === '/policy-search'
    },
    {
      path: '/search',
      icon: 'fas fa-search',
      label: 'Search (Old)',
      active: location.pathname === '/search'
    },
    {
      path: '/reports',
      icon: 'fas fa-chart-bar',
      label: 'Reports',
      active: location.pathname === '/reports'
    },
    {
      path: '/import',
      icon: 'fas fa-file-import',
      label: 'Import Data',
      active: location.pathname === '/import'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  // Check if user has admin permissions
  const isAdmin = user && (
    user.sysadmin === 'yes' ||
    user.mimoadmin === 'yes'
  );

  return (
    <div className="sidebar p-3">
      <Nav className="flex-column">
        <div className="mb-3">
          <h6 className="text-muted text-uppercase small fw-bold">
            Policy Management
          </h6>
        </div>
        
        {menuItems.map((item, index) => (
          <Nav.Link
            key={index}
            className={`d-flex align-items-center py-2 px-3 mb-1 rounded ${
              item.active ? 'bg-primary text-white' : 'text-dark'
            }`}
            style={{ cursor: 'pointer' }}
            onClick={() => handleNavigation(item.path)}
          >
            <i className={`${item.icon} me-3`}></i>
            {item.label}
          </Nav.Link>
        ))}
        
        <hr className="my-3" />

        {/* Admin Section */}
        {isAdmin && (
          <>
            <div className="mb-2">
              <h6 className="text-muted text-uppercase small fw-bold">
                Administration
              </h6>
            </div>

            <Nav.Link
              className={`d-flex align-items-center py-2 px-3 mb-1 rounded ${
                location.pathname === '/users' ? 'bg-primary text-white' : 'text-dark'
              }`}
              style={{ cursor: 'pointer' }}
              onClick={() => handleNavigation('/users')}
            >
              <i className="fas fa-users me-3"></i>
              User Management
            </Nav.Link>

            <hr className="my-3" />
          </>
        )}

        <div className="mb-2">
          <h6 className="text-muted text-uppercase small fw-bold">
            Quick Actions
          </h6>
        </div>
        
        <Nav.Link
          className="d-flex align-items-center py-2 px-3 mb-1 rounded text-dark"
          style={{ cursor: 'pointer' }}
          onClick={() => handleNavigation('/policies/new')}
        >
          <i className="fas fa-plus me-3"></i>
          Create Policy
        </Nav.Link>
        
        <Nav.Link
          className="d-flex align-items-center py-2 px-3 mb-1 rounded text-dark"
          style={{ cursor: 'pointer' }}
          onClick={() => handleNavigation('/search')}
        >
          <i className="fas fa-search me-3"></i>
          Quick Search
        </Nav.Link>
        
        <Nav.Link
          className="d-flex align-items-center py-2 px-3 mb-1 rounded text-dark"
          style={{ cursor: 'pointer' }}
          onClick={() => handleNavigation('/reports')}
        >
          <i className="fas fa-download me-3"></i>
          Export Data
        </Nav.Link>
      </Nav>
    </div>
  );
};

export default Sidebar;
