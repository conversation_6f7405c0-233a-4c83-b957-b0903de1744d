import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Form, InputGroup, <PERSON><PERSON>, Spin<PERSON>, Badge } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';

const PolicyList = () => {
  const [policies, setPolicies] = useState([]);
  const [filteredPolicies, setFilteredPolicies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortField, setSortField] = useState('datecreated');
  const [sortDirection, setSortDirection] = useState('desc');
  const navigate = useNavigate();

  useEffect(() => {
    fetchPolicies();
  }, []);

  useEffect(() => {
    filterAndSortPolicies();
  }, [policies, searchTerm, statusFilter, sortField, sortDirection]);

  const fetchPolicies = async () => {
    try {
      setLoading(true);
      const response = await apiService.getPolicies();
      setPolicies(response.data);
    } catch (err) {
      console.error('Error fetching policies:', err);
      setError('Failed to load policies');
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortPolicies = () => {
    let filtered = [...policies];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(policy =>
        policy.policyno.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.agentname.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.packages.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(policy => policy.status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredPolicies(filtered);
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return 'fas fa-sort';
    return sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'Active': 'success',
      'Pending': 'warning',
      'Expired': 'danger',
      'Cancelled': 'secondary'
    };
    return statusMap[status] || 'secondary';
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" variant="primary" />
      </div>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-0 text-gray-800">Policy Management</h1>
              <p className="text-muted">Manage and view all insurance policies</p>
            </div>
            <Button 
              variant="primary" 
              onClick={() => navigate('/policies/new')}
              className="d-flex align-items-center"
            >
              <i className="fas fa-plus me-2"></i>
              New Policy
            </Button>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Search Policies</Form.Label>
                <InputGroup>
                  <InputGroup.Text>
                    <i className="fas fa-search"></i>
                  </InputGroup.Text>
                  <Form.Control
                    type="text"
                    placeholder="Search by policy number, agent name, or package..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group>
                <Form.Label>Filter by Status</Form.Label>
                <Form.Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="">All Statuses</option>
                  <option value="Active">Active</option>
                  <option value="Pending">Pending</option>
                  <option value="Expired">Expired</option>
                  <option value="Cancelled">Cancelled</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={3} className="d-flex align-items-end">
              <Button 
                variant="outline-secondary" 
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                }}
                className="w-100"
              >
                <i className="fas fa-times me-2"></i>
                Clear Filters
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Policies Table */}
      <Card className="shadow">
        <Card.Header className="py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 className="m-0 font-weight-bold text-primary">
            Policies ({filteredPolicies.length})
          </h6>
        </Card.Header>
        <Card.Body className="p-0">
          <Table responsive hover className="mb-0">
            <thead className="table-light">
              <tr>
                <th 
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleSort('policyno')}
                >
                  Policy No <i className={getSortIcon('policyno')}></i>
                </th>
                <th 
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleSort('agentname')}
                >
                  Agent <i className={getSortIcon('agentname')}></i>
                </th>
                <th 
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleSort('packages')}
                >
                  Package <i className={getSortIcon('packages')}></i>
                </th>
                <th>Business</th>
                <th 
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleSort('status')}
                >
                  Status <i className={getSortIcon('status')}></i>
                </th>
                <th 
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleSort('datecreated')}
                >
                  Date Created <i className={getSortIcon('datecreated')}></i>
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPolicies.map((policy) => (
                <tr key={policy.id}>
                  <td className="fw-bold">{policy.policyno}</td>
                  <td>{policy.agentname}</td>
                  <td>{policy.packages}</td>
                  <td>{policy.bus}</td>
                  <td>
                    <Badge bg={getStatusBadge(policy.status)}>
                      {policy.status}
                    </Badge>
                  </td>
                  <td>{policy.datecreated}</td>
                  <td>
                    <div className="d-flex gap-2">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => navigate(`/policies/${policy.id}`)}
                      >
                        <i className="fas fa-eye"></i>
                      </Button>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={() => navigate(`/policies/${policy.id}/edit`)}
                      >
                        <i className="fas fa-edit"></i>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
          
          {filteredPolicies.length === 0 && (
            <div className="text-center py-5">
              <i className="fas fa-file-contract fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">No policies found</h5>
              <p className="text-muted">Try adjusting your search criteria or create a new policy.</p>
              <Button variant="primary" onClick={() => navigate('/policies/new')}>
                <i className="fas fa-plus me-2"></i>
                Create New Policy
              </Button>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default PolicyList;
