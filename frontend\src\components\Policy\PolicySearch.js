import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Table, Alert, Modal, Pagination, Badge } from 'react-bootstrap';
import { FaSearch, FaEdit, FaTrash, FaEye, FaPlus } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';

const PolicySearch = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [policies, setPolicies] = useState([]);
  const [pagination, setPagination] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState(null);

  // Search filters
  const [filters, setFilters] = useState({
    policy_number: '',
    first_name: '',
    surname: '',
    status: '',
    package: '',
    mode_of_payment: '',
    frequency: '',
    page: 1,
    limit: 10
  });

  const packageOptions = ['', 'Lite', 'Standard', 'Premier'];
  const statusOptions = ['', 'Active', 'Inactive', 'Pending'];
  const paymentModeOptions = ['', 'Debit Order', 'Stop Order', 'Mobile Money'];
  const frequencyOptions = ['', 'Monthly', 'Quarterly', 'Annually'];

  useEffect(() => {
    searchPolicies();
  }, [filters.page]);

  const searchPolicies = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== '') {
          params.append(key, value);
        }
      });

      const response = await apiService.get(`/policy-management/search?${params.toString()}`);
      
      setPolicies(response.data.data);
      setPagination(response.data.pagination);
      
    } catch (err) {
      setError(err.response?.data?.detail || 'Error searching policies');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setFilters({ ...filters, page: 1 });
    searchPolicies();
  };

  const handleClearFilters = () => {
    setFilters({
      policy_number: '',
      first_name: '',
      surname: '',
      status: '',
      package: '',
      mode_of_payment: '',
      frequency: '',
      page: 1,
      limit: 10
    });
  };

  const handlePageChange = (page) => {
    setFilters({ ...filters, page });
  };

  const handleView = (policy) => {
    navigate(`/policy-management/${policy.id}`);
  };

  const handleEdit = (policy) => {
    navigate(`/policy-management/${policy.id}/edit`);
  };

  const handleDeleteClick = (policy) => {
    setSelectedPolicy(policy);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedPolicy) return;

    try {
      await apiService.delete(`/policy-management/${selectedPolicy.id}`);
      setSuccess(`Policy ${selectedPolicy.policyno} deleted successfully`);
      setShowDeleteModal(false);
      setSelectedPolicy(null);
      searchPolicies(); // Refresh the list
    } catch (err) {
      setError(err.response?.data?.detail || 'Error deleting policy');
    }
  };

  const renderSearchFilters = () => (
    <Card className="mb-4">
      <Card.Header>
        <h5>Search Policies</h5>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Policy Number</Form.Label>
              <Form.Control
                type="text"
                value={filters.policy_number}
                onChange={(e) => setFilters({ ...filters, policy_number: e.target.value })}
                placeholder="Enter policy number"
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>First Name</Form.Label>
              <Form.Control
                type="text"
                value={filters.first_name}
                onChange={(e) => setFilters({ ...filters, first_name: e.target.value })}
                placeholder="Enter first name"
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Surname</Form.Label>
              <Form.Control
                type="text"
                value={filters.surname}
                onChange={(e) => setFilters({ ...filters, surname: e.target.value })}
                placeholder="Enter surname"
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Status</Form.Label>
              <Form.Select
                value={filters.status}
                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              >
                {statusOptions.map((option, index) => (
                  <option key={index} value={option}>
                    {option || 'All Statuses'}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Package</Form.Label>
              <Form.Select
                value={filters.package}
                onChange={(e) => setFilters({ ...filters, package: e.target.value })}
              >
                {packageOptions.map((option, index) => (
                  <option key={index} value={option}>
                    {option || 'All Packages'}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Mode of Payment</Form.Label>
              <Form.Select
                value={filters.mode_of_payment}
                onChange={(e) => setFilters({ ...filters, mode_of_payment: e.target.value })}
              >
                {paymentModeOptions.map((option, index) => (
                  <option key={index} value={option}>
                    {option || 'All Modes'}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group className="mb-3">
              <Form.Label>Frequency</Form.Label>
              <Form.Select
                value={filters.frequency}
                onChange={(e) => setFilters({ ...filters, frequency: e.target.value })}
              >
                {frequencyOptions.map((option, index) => (
                  <option key={index} value={option}>
                    {option || 'All Frequencies'}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>
        <div className="d-flex gap-2">
          <Button variant="primary" onClick={handleSearch} disabled={loading}>
            <FaSearch className="me-2" />
            {loading ? 'Searching...' : 'Search'}
          </Button>
          <Button variant="outline-secondary" onClick={handleClearFilters}>
            Clear Filters
          </Button>
          <Button variant="success" onClick={() => navigate('/policy-capture')}>
            <FaPlus className="me-2" />
            New Policy
          </Button>
        </div>
      </Card.Body>
    </Card>
  );

  const renderPolicyTable = () => (
    <Card>
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h5>Policy Results</h5>
        <Badge bg="info">
          {pagination.total || 0} policies found
        </Badge>
      </Card.Header>
      <Card.Body>
        {policies.length === 0 ? (
          <div className="text-center py-4">
            <p>No policies found. Try adjusting your search criteria.</p>
          </div>
        ) : (
          <>
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>Policy No.</th>
                  <th>Policy Holder</th>
                  <th>Package</th>
                  <th>Status</th>
                  <th>Premium</th>
                  <th>Payment Mode</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {policies.map((policy) => (
                  <tr key={policy.id}>
                    <td>
                      <strong>{policy.policyno}</strong>
                    </td>
                    <td>
                      {policy.holder ? (
                        <div>
                          <div>{policy.holder.firstname} {policy.holder.surname}</div>
                          <small className="text-muted">{policy.holder.mobilenumber}</small>
                        </div>
                      ) : (
                        <span className="text-muted">No holder data</span>
                      )}
                    </td>
                    <td>
                      <Badge bg="secondary">{policy.packages}</Badge>
                    </td>
                    <td>
                      <Badge bg={policy.status === 'Active' ? 'success' : 'warning'}>
                        {policy.status}
                      </Badge>
                    </td>
                    <td>
                      {policy.mandate ? (
                        <div>
                          <div>{policy.mandate.premium}</div>
                          <small className="text-muted">({policy.mandate.frequency})</small>
                        </div>
                      ) : (
                        <span className="text-muted">-</span>
                      )}
                    </td>
                    <td>
                      {policy.mandate?.modeofpayment || '-'}
                    </td>
                    <td>
                      <small>{policy.datecreated}</small>
                    </td>
                    <td>
                      <div className="d-flex gap-1">
                        <Button
                          variant="outline-info"
                          size="sm"
                          onClick={() => handleView(policy)}
                          title="View Details"
                        >
                          <FaEye />
                        </Button>
                        <Button
                          variant="outline-warning"
                          size="sm"
                          onClick={() => handleEdit(policy)}
                          title="Edit Policy"
                        >
                          <FaEdit />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDeleteClick(policy)}
                          title="Delete Policy"
                        >
                          <FaTrash />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>

            {pagination.pages > 1 && (
              <div className="d-flex justify-content-center mt-3">
                <Pagination>
                  <Pagination.First 
                    onClick={() => handlePageChange(1)}
                    disabled={pagination.page === 1}
                  />
                  <Pagination.Prev 
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                  />
                  
                  {[...Array(pagination.pages)].map((_, index) => {
                    const page = index + 1;
                    if (
                      page === 1 ||
                      page === pagination.pages ||
                      (page >= pagination.page - 2 && page <= pagination.page + 2)
                    ) {
                      return (
                        <Pagination.Item
                          key={page}
                          active={page === pagination.page}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Pagination.Item>
                      );
                    }
                    return null;
                  })}
                  
                  <Pagination.Next 
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.pages}
                  />
                  <Pagination.Last 
                    onClick={() => handlePageChange(pagination.pages)}
                    disabled={pagination.page === pagination.pages}
                  />
                </Pagination>
              </div>
            )}
          </>
        )}
      </Card.Body>
    </Card>
  );

  return (
    <Container fluid className="p-4">
      <Row>
        <Col>
          <h2 className="mb-4">Policy Management</h2>
          
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          
          {renderSearchFilters()}
          {renderPolicyTable()}
          
          {/* Delete Confirmation Modal */}
          <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
            <Modal.Header closeButton>
              <Modal.Title>Confirm Delete</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              Are you sure you want to delete policy <strong>{selectedPolicy?.policyno}</strong>?
              This action cannot be undone and will delete all related data including policy holder, 
              payment mandate, and beneficiaries.
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                Cancel
              </Button>
              <Button variant="danger" onClick={handleDeleteConfirm}>
                Delete Policy
              </Button>
            </Modal.Footer>
          </Modal>
        </Col>
      </Row>
    </Container>
  );
};

export default PolicySearch;
